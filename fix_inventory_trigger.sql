CREATE OR <PERSON><PERSON>LACE FUNCTION public.process_order_inventory()
R<PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  should_process_inventory BOOLEAN := FALSE;
  deduction_count INTEGER := 0;
BEGIN
  -- Skip if inventory already processed
  IF NEW.inventory_processed = TRUE THEN
    RAISE NOTICE 'Skipping inventory processing for order: % - already processed', NEW.id;
    RETURN NEW;
  END IF;
  
  -- Determine if we should process inventory deduction
  IF TG_OP = 'UPDATE' AND OLD.status IN ('new', 'pending', 'preparing', 'ready') AND NEW.status = 'completed' THEN
    should_process_inventory := TRUE;
    RAISE NOTICE 'Processing inventory deduction for completed order: % (% -> %)', NEW.id, OLD.status, NEW.status;
  END IF;
  
  -- Process inventory deduction if needed
  IF should_process_inventory THEN
    
    -- Check if order has items before processing
    SELECT COUNT(*) INTO deduction_count FROM order_items WHERE order_id = NEW.id;
    
    IF deduction_count > 0 THEN
      
      RAISE NOTICE 'Found % order items for order %', deduction_count, NEW.id;
      
      -- Deduct ingredients from inventory
      WITH order_ingredients AS (
        SELECT 
          mii.inventory_item_id,
          SUM(mii.quantity_needed * oi.quantity) as total_quantity_needed
        FROM order_items oi
        JOIN menu_item_ingredients mii ON oi.item_id = mii.menu_item_id
        WHERE oi.order_id = NEW.id
        GROUP BY mii.inventory_item_id
      )
      UPDATE inventory_items 
      SET 
        quantity = GREATEST(0, quantity - oi.total_quantity_needed),
        updated_at = NOW()
      FROM order_ingredients oi
      WHERE inventory_items.id = oi.inventory_item_id;
      
      GET DIAGNOSTICS deduction_count = ROW_COUNT;
      RAISE NOTICE 'Updated % inventory items for order %', deduction_count, NEW.id;
      
      -- Mark order as processed in the NEW record
      NEW.inventory_processed := TRUE;
      RAISE NOTICE 'Marked order % as inventory_processed', NEW.id;
      
    ELSE
      RAISE NOTICE 'No order items found for order: %', NEW.id;
    END IF;
    
  END IF;
  
  RETURN NEW;
END;
$function$;
