'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group'
import { AlertCircle, X } from 'lucide-react'

interface CafeteriaCancellationDialogProps {
  isOpen: boolean
  onClose: () => void
  onConfirm: (reason: string, customReason?: string) => void
  orderNumber: string
  isLoading?: boolean
}

const CAFETERIA_CANCELLATION_REASONS = [
  'Item out of stock',
  'Kitchen equipment issue',
  'Staff shortage',
  'Ingredient unavailable',
  'Too busy to fulfill',
  'Customer request',
  'Technical issue',
  'Other',
]

export function CafeteriaCancellationDialog({
  isOpen,
  onClose,
  onConfirm,
  orderNumber,
  isLoading = false
}: CafeteriaCancellationDialogProps) {
  const [selectedReason, setSelectedReason] = useState<string>('')
  const [customReason, setCustomReason] = useState<string>('')

  const handleConfirm = () => {
    if (!selectedReason) return
    
    const finalReason = selectedReason === 'Other' ? customReason.trim() : selectedReason
    if (selectedReason === 'Other' && !finalReason) return
    
    onConfirm(finalReason, selectedReason === 'Other' ? customReason.trim() : undefined)
  }

  const handleClose = () => {
    if (!isLoading) {
      setSelectedReason('')
      setCustomReason('')
      onClose()
    }
  }

  const isValid = selectedReason && (selectedReason !== 'Other' || customReason.trim())

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-3">
            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-red-100">
              <AlertCircle className="h-5 w-5 text-red-600" />
            </div>
            <div>
              <DialogTitle className="text-lg font-semibold">Cancel Order</DialogTitle>
              <DialogDescription className="text-sm text-gray-600">
                Order #{orderNumber}
              </DialogDescription>
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={isLoading}
            className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none"
          >
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>
        </DialogHeader>

        <div className="space-y-6">
          <div>
            <Label className="text-sm font-medium text-gray-900">
              Please select a reason for cancellation:
            </Label>
            <RadioGroup
              value={selectedReason}
              onValueChange={setSelectedReason}
              className="mt-3 space-y-2"
              disabled={isLoading}
            >
              {CAFETERIA_CANCELLATION_REASONS.map((reason) => (
                <div key={reason} className="flex items-center space-x-3">
                  <RadioGroupItem
                    value={reason}
                    id={reason}
                    className="border-gray-300 text-orange-600 focus:ring-orange-500"
                  />
                  <Label
                    htmlFor={reason}
                    className="text-sm font-normal text-gray-700 cursor-pointer flex-1"
                  >
                    {reason}
                  </Label>
                </div>
              ))}
            </RadioGroup>
          </div>

          {selectedReason === 'Other' && (
            <div className="space-y-2">
              <Label htmlFor="custom-reason" className="text-sm font-medium text-gray-900">
                Please specify the reason:
              </Label>
              <Textarea
                id="custom-reason"
                value={customReason}
                onChange={(e) => setCustomReason(e.target.value)}
                placeholder="Enter your custom cancellation reason..."
                className="min-h-[80px] resize-none"
                disabled={isLoading}
                maxLength={500}
              />
              <p className="text-xs text-gray-500">
                {customReason.length}/500 characters
              </p>
            </div>
          )}

          <div className="flex gap-3 pt-4">
            <Button
              variant="outline"
              onClick={handleClose}
              disabled={isLoading}
              className="flex-1"
            >
              Keep Order
            </Button>
            <Button
              onClick={handleConfirm}
              disabled={!isValid || isLoading}
              className="flex-1 bg-red-600 hover:bg-red-700 text-white"
            >
              {isLoading ? (
                <div className="flex items-center gap-2">
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  Cancelling...
                </div>
              ) : (
                'Cancel Order'
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
