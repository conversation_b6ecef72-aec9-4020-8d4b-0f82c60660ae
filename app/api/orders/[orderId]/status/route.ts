import { NextRequest, NextResponse } from 'next/server'
import { OrderProcessingService } from '@/lib/order-processing-service'
import { supabase } from '@/lib/supabase'

export async function PUT(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params
    const { status } = await request.json()

    console.log(`🔄 [API] Updating order ${orderId} status to: ${status}`)

    if (!orderId || !status) {
      return NextResponse.json(
        { error: 'Order ID and status are required' },
        { status: 400 }
      )
    }

    // Validate status
    const validStatuses = ['pending', 'confirmed', 'preparing', 'ready', 'completed', 'cancelled']
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        { error: 'Invalid status' },
        { status: 400 }
      )
    }

    // Process the order status update
    const result = await OrderProcessingService.updateOrderStatus(orderId, status)

    if (!result.success) {
      return NextResponse.json(
        { error: result.error || 'Failed to update order status' },
        { status: 500 }
      )
    }

    // Return detailed response
    return NextResponse.json({
      success: true,
      orderId,
      status,
      inventoryDeductions: result.inventoryDeductions,
      unavailableMenuItems: result.unavailableMenuItems,
      message: status === 'completed' 
        ? `Order completed and inventory updated. ${result.inventoryDeductions.length} items deducted.`
        : `Order status updated to ${status}`
    })

  } catch (error) {
    console.error('❌ [API] Error updating order status:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function GET(
  request: NextRequest,
  { params }: { params: { orderId: string } }
) {
  try {
    const { orderId } = params

    console.log(`🔍 [API] Getting order processing summary for: ${orderId}`)

    // Get order processing summary
    const summary = await OrderProcessingService.getOrderProcessingSummary(orderId)

    return NextResponse.json({
      success: true,
      ...summary
    })

  } catch (error) {
    console.error('❌ [API] Error getting order summary:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
