import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/payment_method.dart';

class PaymentMethodsService {
  final SupabaseClient _client = Supabase.instance.client;

  /// Get all payment methods for a user
  Future<List<PaymentMethod>> getUserPaymentMethods(String userId) async {
    try {
      debugPrint('🔍 Getting payment methods for user: $userId');
      
      final response = await _client
          .from('payment_methods')
          .select()
          .eq('user_id', userId)
          .eq('is_active', true)
          .order('is_default', ascending: false)
          .order('created_at', ascending: false);

      debugPrint('📦 Found ${response.length} payment methods');
      
      return response.map((json) => PaymentMethod.fromJson(json)).toList();
    } catch (e) {
      debugPrint('❌ Error getting payment methods: $e');
      rethrow;
    }
  }

  /// Add a new payment method
  Future<PaymentMethod> addPaymentMethod(CreatePaymentMethodRequest request, String userId) async {
    try {
      debugPrint('➕ Adding payment method for user: $userId');
      
      // If this is set as default, first unset all other defaults
      if (request.isDefault) {
        await _setAllPaymentMethodsNonDefault(userId);
      }

      final response = await _client
          .from('payment_methods')
          .insert(request.toJson(userId))
          .select()
          .single();

      debugPrint('✅ Payment method added successfully');
      return PaymentMethod.fromJson(response);
    } catch (e) {
      debugPrint('❌ Error adding payment method: $e');
      rethrow;
    }
  }

  /// Set a payment method as default
  Future<void> setDefaultPaymentMethod(String paymentMethodId, String userId) async {
    try {
      debugPrint('🎯 Setting payment method as default: $paymentMethodId');
      
      // First, unset all other defaults for this user
      await _setAllPaymentMethodsNonDefault(userId);
      
      // Then set the selected one as default
      await _client
          .from('payment_methods')
          .update({'is_default': true, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', paymentMethodId)
          .eq('user_id', userId);

      debugPrint('✅ Payment method set as default');
    } catch (e) {
      debugPrint('❌ Error setting default payment method: $e');
      rethrow;
    }
  }

  /// Delete a payment method
  Future<void> deletePaymentMethod(String paymentMethodId, String userId) async {
    try {
      debugPrint('🗑️ Deleting payment method: $paymentMethodId');
      
      // Soft delete by setting is_active to false
      await _client
          .from('payment_methods')
          .update({'is_active': false, 'updated_at': DateTime.now().toIso8601String()})
          .eq('id', paymentMethodId)
          .eq('user_id', userId);

      debugPrint('✅ Payment method deleted successfully');
    } catch (e) {
      debugPrint('❌ Error deleting payment method: $e');
      rethrow;
    }
  }

  /// Get the default payment method for a user
  Future<PaymentMethod?> getDefaultPaymentMethod(String userId) async {
    try {
      debugPrint('🎯 Getting default payment method for user: $userId');
      
      final response = await _client
          .from('payment_methods')
          .select()
          .eq('user_id', userId)
          .eq('is_active', true)
          .eq('is_default', true)
          .maybeSingle();

      if (response != null) {
        debugPrint('✅ Found default payment method');
        return PaymentMethod.fromJson(response);
      } else {
        debugPrint('ℹ️ No default payment method found');
        return null;
      }
    } catch (e) {
      debugPrint('❌ Error getting default payment method: $e');
      rethrow;
    }
  }

  /// Helper method to unset all payment methods as non-default
  Future<void> _setAllPaymentMethodsNonDefault(String userId) async {
    await _client
        .from('payment_methods')
        .update({'is_default': false, 'updated_at': DateTime.now().toIso8601String()})
        .eq('user_id', userId)
        .eq('is_active', true);
  }

  /// Validate card number and return card type
  static String getCardType(String cardNumber) {
    // Remove spaces and non-digits
    final cleanNumber = cardNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanNumber.startsWith('4')) {
      return 'visa';
    } else if (cleanNumber.startsWith(RegExp(r'^5[1-5]')) || 
               cleanNumber.startsWith(RegExp(r'^2[2-7]'))) {
      return 'mastercard';
    } else if (cleanNumber.startsWith(RegExp(r'^3[47]'))) {
      return 'amex';
    } else if (cleanNumber.startsWith('6')) {
      return 'discover';
    } else {
      return 'unknown';
    }
  }

  /// Validate card number using Luhn algorithm
  static bool isValidCardNumber(String cardNumber) {
    final cleanNumber = cardNumber.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanNumber.length < 13 || cleanNumber.length > 19) {
      return false;
    }

    int sum = 0;
    bool alternate = false;
    
    for (int i = cleanNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cleanNumber[i]);
      
      if (alternate) {
        digit *= 2;
        if (digit > 9) {
          digit = (digit % 10) + 1;
        }
      }
      
      sum += digit;
      alternate = !alternate;
    }
    
    return sum % 10 == 0;
  }

  /// Validate expiry date
  static bool isValidExpiryDate(int month, int year) {
    if (month < 1 || month > 12) return false;
    
    final now = DateTime.now();
    final currentYear = now.year;
    final currentMonth = now.month;
    
    if (year < currentYear) return false;
    if (year == currentYear && month < currentMonth) return false;
    
    return true;
  }
}
