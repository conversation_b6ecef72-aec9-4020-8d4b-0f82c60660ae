import 'dart:async';
import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/utils/navigation_helper.dart';

// Notification model
class NotificationModel {
  final String id;
  final String userId;
  final String title;
  final String message;
  final String type;
  final bool isRead;
  final DateTime createdAt;
  final String? relatedOrderId;

  NotificationModel({
    required this.id,
    required this.userId,
    required this.title,
    required this.message,
    required this.type,
    required this.isRead,
    required this.createdAt,
    this.relatedOrderId,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] ?? '',
      userId: json['user_id'] ?? '',
      title: json['title'] ?? '',
      message: json['message'] ?? '',
      type: json['type'] ?? 'general',
      isRead: json['is_read'] ?? false,
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      relatedOrderId: json['related_order_id'],
    );
  }
}

class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  StreamSubscription<List<Map<String, dynamic>>>? _notificationSubscription;
  String? _currentUserId;

  // Stream controller for real-time notifications
  final StreamController<NotificationModel> _notificationController =
      StreamController<NotificationModel>.broadcast();

  Stream<NotificationModel> get notificationStream => _notificationController.stream;

  // Use the centralized navigation helper instead of separate GlobalKey
  static GlobalKey<NavigatorState> get navigatorKey => NavigationHelper.navigatorKey;

  // Initialize the notification service with real-time capabilities
  Future<void> init({String? userId}) async {
    debugPrint('Notification service initialized');
    if (userId != null) {
      await initialize(userId);
    }
  }

  // Initialize with user ID for real-time notifications
  Future<void> initialize(String userId) async {
    _currentUserId = userId;
    await _setupRealtimeSubscription();
    await _registerPushToken();
  }

  // Setup real-time subscription for notifications
  Future<void> _setupRealtimeSubscription() async {
    if (_currentUserId == null) return;

    // Cancel existing subscription
    await _notificationSubscription?.cancel();

    try {
      // Subscribe to notifications table changes
      _notificationSubscription = _supabase
          .from('notifications')
          .stream(primaryKey: ['id'])
          .eq('user_id', _currentUserId!)
          .order('created_at', ascending: false)
          .listen((data) {
            if (data.isNotEmpty) {
              final latestNotification = data.first;
              final notification = NotificationModel.fromJson(latestNotification);

              // Add to stream
              _notificationController.add(notification);

              // Show in-app notification
              _showInAppNotification(notification);
            }
          });

      debugPrint('Real-time notification subscription setup for user: $_currentUserId');
    } catch (e) {
      debugPrint('Error setting up notification subscription: $e');
    }
  }

  // Register push notification token
  Future<void> _registerPushToken() async {
    if (_currentUserId == null) return;

    try {
      // For now, create a simple token for mobile
      final mobileToken = 'mobile_${_currentUserId}_${DateTime.now().millisecondsSinceEpoch}';

      await _supabase.from('push_notification_tokens').upsert({
        'user_id': _currentUserId,
        'token': mobileToken,
        'platform': defaultTargetPlatform == TargetPlatform.iOS ? 'ios' : 'android',
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('Push token registered: $mobileToken');
    } catch (e) {
      debugPrint('Error registering push token: $e');
    }
  }

  // Show in-app notification
  void _showInAppNotification(NotificationModel notification) {
    final context = NavigationHelper.currentContext;
    if (context != null) {
      // Show a snackbar using our utility
      SnackBarUtils.showOrderSnackBar(
        context: context,
        message: notification.message,
        status: notification.title,
        onViewOrder: () => _navigateToNotification(notification),
      );
    }
  }

  // Navigate to notification target
  void _navigateToNotification(NotificationModel notification) {
    final context = NavigationHelper.currentContext;
    if (context == null) return;

    switch (notification.type) {
      case 'order_status':
      case 'order_ready':
      case 'order_completed':
        if (notification.relatedOrderId != null) {
          // Navigate to order details
          Navigator.of(context).pushNamed('/order-details', arguments: notification.relatedOrderId);
        }
        break;
      case 'payment_success':
      case 'payment_failed':
        if (notification.relatedOrderId != null) {
          Navigator.of(context).pushNamed('/order-details', arguments: notification.relatedOrderId);
        }
        break;
      case 'promotion':
      case 'announcement':
        // Navigate to promotions or home
        Navigator.of(context).pushNamed('/home');
        break;
      default:
        // Navigate to notifications page if it exists
        Navigator.of(context).pushNamed('/notifications');
        break;
    }
  }

  // Request notification permissions (simplified version)
  Future<void> requestPermissions() async {
    debugPrint('Notification permissions requested');
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications({int limit = 50}) async {
    if (_currentUserId == null) return [];

    try {
      final response = await _supabase
          .from('notifications')
          .select()
          .eq('user_id', _currentUserId!)
          .order('created_at', ascending: false)
          .limit(limit);

      return response.map<NotificationModel>((json) => NotificationModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error fetching notifications: $e');
      return [];
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(String notificationId) async {
    try {
      await _supabase
          .from('notifications')
          .update({'is_read': true})
          .eq('id', notificationId);
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Get unread count
  Future<int> getUnreadCount() async {
    if (_currentUserId == null) return 0;

    try {
      final response = await _supabase
          .from('notifications')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', _currentUserId!)
          .eq('is_read', false);

      return response.count ?? 0;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }

  // Create notification (for testing or admin use)
  Future<bool> createNotification({
    required String userId,
    required String title,
    required String message,
    required String type,
    String? relatedOrderId,
  }) async {
    try {
      await _supabase.from('notifications').insert({
        'user_id': userId,
        'title': title,
        'message': message,
        'type': type,
        'is_read': false,
        'related_order_id': relatedOrderId,
        'created_at': DateTime.now().toIso8601String(),
      });
      return true;
    } catch (e) {
      debugPrint('Error creating notification: $e');
      return false;
    }
  }

  // Legacy methods for backward compatibility
  // Show a notification for order status change
  void showOrderStatusNotification({
    required String title,
    required String body,
    required String orderNumber,
    String? status,
    VoidCallback? onTap,
  }) {
    // Show in-app notification if the app is in the foreground
    final context = NavigationHelper.currentContext;
    if (context != null) {
      // Show a snackbar using our utility
      SnackBarUtils.showOrderSnackBar(
        context: context,
        message: body,
        status: status ?? 'Updated',
        onViewOrder: onTap,
      );
    }
  }

  // Show a notification for order ready for pickup
  void showOrderReadyNotification(Order order, {VoidCallback? onTap}) {
    final String cafeteriaName = order.items.isNotEmpty
        ? order.items.first.cafeteriaName
        : 'the cafeteria';

    showOrderStatusNotification(
      title: 'Order Ready for Pickup',
      body: 'Your order #${order.orderNumber} is ready for pickup at $cafeteriaName.',
      orderNumber: order.orderNumber,
      status: 'Ready for pickup',
      onTap: onTap,
    );
  }

  // Show a notification for order completed
  void showOrderCompletedNotification(Order order, {VoidCallback? onTap}) {
    showOrderStatusNotification(
      title: 'Order Completed',
      body: 'Your order #${order.orderNumber} has been completed. Thank you for using UniEats!',
      orderNumber: order.orderNumber,
      status: 'Completed',
      onTap: onTap,
    );
  }

  // Show a notification for order cancelled
  void showOrderCancelledNotification(Order order, {VoidCallback? onTap}) {
    showOrderStatusNotification(
      title: 'Order Cancelled',
      body: 'Your order #${order.orderNumber} has been cancelled.',
      orderNumber: order.orderNumber,
      status: 'Cancelled',
      onTap: onTap,
    );
  }

  // Cleanup
  void dispose() {
    _notificationSubscription?.cancel();
    _notificationController.close();
    _currentUserId = null;
  }
}

  // Register push notification token
  Future<void> _registerPushToken() async {
    if (_currentUserId == null) return;

    try {
      // For now, create a simple token for mobile
      final mobileToken = 'mobile_${_currentUserId}_${DateTime.now().millisecondsSinceEpoch}';

      await _supabase.from('push_notification_tokens').upsert({
        'user_id': _currentUserId,
        'token': mobileToken,
        'platform': defaultTargetPlatform == TargetPlatform.iOS ? 'ios' : 'android',
        'is_active': true,
        'updated_at': DateTime.now().toIso8601String(),
      });

      debugPrint('Push token registered: $mobileToken');
    } catch (e) {
      debugPrint('Error registering push token: $e');
    }
  }

  // Show in-app notification
  void _showInAppNotification(NotificationModel notification) {
    final context = NavigationHelper.currentContext;
    if (context != null) {
      // Show a snackbar using our utility
      SnackBarUtils.showOrderSnackBar(
        context: context,
        message: notification.message,
        status: notification.title,
        onViewOrder: () => _navigateToNotification(notification),
      );
    }
  }

  // Navigate to notification target
  void _navigateToNotification(NotificationModel notification) {
    final context = NavigationHelper.currentContext;
    if (context == null) return;

    switch (notification.type) {
      case 'order_status':
      case 'order_ready':
      case 'order_completed':
        if (notification.relatedOrderId != null) {
          // Navigate to order details
          Navigator.of(context).pushNamed('/order-details', arguments: notification.relatedOrderId);
        }
        break;
      case 'payment_success':
      case 'payment_failed':
        if (notification.relatedOrderId != null) {
          Navigator.of(context).pushNamed('/order-details', arguments: notification.relatedOrderId);
        }
        break;
      case 'promotion':
      case 'announcement':
        // Navigate to promotions or home
        Navigator.of(context).pushNamed('/home');
        break;
      default:
        // Navigate to notifications page if it exists
        Navigator.of(context).pushNamed('/notifications');
        break;
    }
  }

  // Request notification permissions (simplified version)
  Future<void> requestPermissions() async {
    debugPrint('Notification permissions requested');
  }

  // Get all notifications
  Future<List<NotificationModel>> getNotifications({int limit = 50}) async {
    if (_currentUserId == null) return [];

    try {
      final response = await _supabase
          .from('notifications')
          .select()
          .eq('user_id', _currentUserId!)
          .order('created_at', ascending: false)
          .limit(limit);

      return response.map<NotificationModel>((json) => NotificationModel.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error fetching notifications: $e');
      return [];
    }
  }

  // Mark notification as read
  Future<bool> markAsRead(String notificationId) async {
    try {
      await _supabase
          .from('notifications')
          .update({'is_read': true})
          .eq('id', notificationId);
      return true;
    } catch (e) {
      debugPrint('Error marking notification as read: $e');
      return false;
    }
  }

  // Get unread count
  Future<int> getUnreadCount() async {
    if (_currentUserId == null) return 0;

    try {
      final response = await _supabase
          .from('notifications')
          .select('id', const FetchOptions(count: CountOption.exact))
          .eq('user_id', _currentUserId!)
          .eq('is_read', false);

      return response.count ?? 0;
    } catch (e) {
      debugPrint('Error getting unread count: $e');
      return 0;
    }
  }
