import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class CafeteriaStatusService {
  static final CafeteriaStatusService _instance = CafeteriaStatusService._internal();
  factory CafeteriaStatusService() => _instance;
  CafeteriaStatusService._internal();

  final SupabaseClient _supabase = Supabase.instance.client;
  StreamSubscription<List<Map<String, dynamic>>>? _statusSubscription;
  Timer? _refreshTimer;

  // Stream controller for cafeteria status updates
  final StreamController<List<CafeteriaStatus>> _statusController =
      StreamController<List<CafeteriaStatus>>.broadcast();

  Stream<List<CafeteriaStatus>> get statusStream => _statusController.stream;

  // Initialize real-time status monitoring
  Future<void> initialize() async {
    await _setupRealtimeSubscription();
    await loadAllCafeteriaStatuses();
    _startPeriodicRefresh();
    debugPrint('CafeteriaStatusService initialized with real-time sync');
  }

  // Setup real-time subscription for cafeteria status changes
  Future<void> _setupRealtimeSubscription() async {
    try {
      // Cancel existing subscription
      await _statusSubscription?.cancel();

      // Subscribe to cafeteria table changes
      _statusSubscription = _supabase
          .from('cafeterias')
          .stream(primaryKey: ['id'])
          .eq('is_active', true)
          .listen((data) {
            debugPrint('Cafeteria status update received: ${data.length} cafeterias');
            final statuses = data.map((json) => CafeteriaStatus.fromJson(json)).toList();
            _statusController.add(statuses);
          });

      debugPrint('Real-time cafeteria status subscription setup');
    } catch (e) {
      debugPrint('Error setting up cafeteria status subscription: $e');
    }
  }

  // Start periodic refresh to ensure sync
  void _startPeriodicRefresh() {
    _refreshTimer?.cancel();
    _refreshTimer = Timer.periodic(const Duration(seconds: 30), (timer) {
      debugPrint('Periodic cafeteria status refresh');
      loadAllCafeteriaStatuses();
    });
  }

  // Load all cafeteria statuses
  Future<List<CafeteriaStatus>> loadAllCafeteriaStatuses() async {
    try {
      final response = await _supabase
          .from('cafeterias')
          .select('''
            id,
            name,
            operational_status,
            status_message,
            status_updated_at,
            is_open,
            is_active,
            description,
            image_url,
            location,
            rating
          ''')
          .eq('is_active', true)
          .order('name');

      final statuses = response.map<CafeteriaStatus>((json) => CafeteriaStatus.fromJson(json)).toList();
      _statusController.add(statuses);
      return statuses;
    } catch (e) {
      debugPrint('Error loading cafeteria statuses: $e');
      return [];
    }
  }

  // Get status for a specific cafeteria
  Future<CafeteriaStatus?> getCafeteriaStatus(String cafeteriaId) async {
    try {
      final response = await _supabase
          .from('cafeterias')
          .select('''
            id,
            name,
            operational_status,
            status_message,
            status_updated_at,
            is_open,
            is_active,
            description,
            image_url,
            location,
            rating
          ''')
          .eq('id', cafeteriaId)
          .single();

      return CafeteriaStatus.fromJson(response);
    } catch (e) {
      debugPrint('Error getting cafeteria status: $e');
      return null;
    }
  }

  // Check if cafeteria is available for orders
  bool isCafeteriaAvailable(CafeteriaStatus status) {
    return status.isActive && status.operationalStatus == 'open';
  }

  // Check if cafeteria is busy
  bool isCafeteriaBusy(CafeteriaStatus status) {
    return status.isActive && status.operationalStatus == 'busy';
  }

  // Check if cafeteria is closed
  bool isCafeteriaClosed(CafeteriaStatus status) {
    return !status.isActive || ['closed', 'temporarily_closed'].contains(status.operationalStatus);
  }

  // Get status display information
  StatusDisplay getStatusDisplay(String operationalStatus) {
    switch (operationalStatus) {
      case 'open':
        return StatusDisplay(
          label: 'Open',
          color: 'green',
          icon: '🟢',
          description: 'Accepting orders',
        );
      case 'busy':
        return StatusDisplay(
          label: 'Busy',
          color: 'orange',
          icon: '🟡',
          description: 'High demand - longer wait times',
        );
      case 'closed':
        return StatusDisplay(
          label: 'Closed',
          color: 'red',
          icon: '🔴',
          description: 'Not accepting orders',
        );
      case 'temporarily_closed':
        return StatusDisplay(
          label: 'Temporarily Closed',
          color: 'red',
          icon: '⏸️',
          description: 'Temporarily not accepting orders',
        );
      default:
        return StatusDisplay(
          label: 'Unknown',
          color: 'gray',
          icon: '⚪',
          description: 'Status unknown',
        );
    }
  }

  // Cleanup
  void dispose() {
    _statusSubscription?.cancel();
    _refreshTimer?.cancel();
    _statusController.close();
  }
}

// Cafeteria Status Model
class CafeteriaStatus {
  final String id;
  final String name;
  final String operationalStatus;
  final String statusMessage;
  final DateTime statusUpdatedAt;
  final bool isOpen;
  final bool isActive;
  final String? description;
  final String? imageUrl;
  final String? location;
  final double? rating;

  CafeteriaStatus({
    required this.id,
    required this.name,
    required this.operationalStatus,
    required this.statusMessage,
    required this.statusUpdatedAt,
    required this.isOpen,
    required this.isActive,
    this.description,
    this.imageUrl,
    this.location,
    this.rating,
  });

  factory CafeteriaStatus.fromJson(Map<String, dynamic> json) {
    return CafeteriaStatus(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      operationalStatus: json['operational_status'] ?? 'open',
      statusMessage: json['status_message'] ?? '',
      statusUpdatedAt: DateTime.parse(json['status_updated_at'] ?? DateTime.now().toIso8601String()),
      isOpen: json['is_open'] ?? true,
      isActive: json['is_active'] ?? true,
      description: json['description'],
      imageUrl: json['image_url'],
      location: json['location'],
      rating: json['rating']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'operational_status': operationalStatus,
      'status_message': statusMessage,
      'status_updated_at': statusUpdatedAt.toIso8601String(),
      'is_open': isOpen,
      'is_active': isActive,
      'description': description,
      'image_url': imageUrl,
      'location': location,
      'rating': rating,
    };
  }

  // Helper getters
  bool get isAvailable => isActive && operationalStatus == 'open';
  bool get isBusy => isActive && operationalStatus == 'busy';
  bool get isClosed => !isActive || ['closed', 'temporarily_closed'].contains(operationalStatus);
  
  StatusDisplay get statusDisplay => CafeteriaStatusService().getStatusDisplay(operationalStatus);
}

// Status Display Model
class StatusDisplay {
  final String label;
  final String color;
  final String icon;
  final String description;

  StatusDisplay({
    required this.label,
    required this.color,
    required this.icon,
    required this.description,
  });
}
