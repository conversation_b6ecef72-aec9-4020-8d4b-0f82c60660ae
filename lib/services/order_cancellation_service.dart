import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class OrderCancellationService {
  final SupabaseClient _client = Supabase.instance.client;

  /// User cancellation reasons
  static const List<String> userCancellationReasons = [
    'Changed my mind',
    'Found a better option',
    'Taking too long',
    'Wrong order placed',
    'Payment issues',
    'Emergency came up',
    'Other',
  ];

  /// Cafeteria cancellation reasons
  static const List<String> cafeteriaCancellationReasons = [
    'Item out of stock',
    'Kitchen equipment issue',
    'Staff shortage',
    'Ingredient unavailable',
    'Too busy to fulfill',
    'Customer request',
    'Technical issue',
    'Other',
  ];

  /// Cancel order by user
  Future<bool> cancelOrderByUser({
    required String orderId,
    required String userId,
    required String reason,
    String? customReason,
  }) async {
    try {
      debugPrint('🚫 User cancelling order: $orderId');
      debugPrint('📝 Reason: $reason');
      
      final finalReason = reason == 'Other' ? customReason ?? 'Other' : reason;
      
      // Update order status and cancellation details
      final response = await _client
          .from('orders')
          .update({
            'status': 'cancelled',
            'cancellation_reason': finalReason,
            'cancelled_by': 'user',
            'cancelled_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId)
          .eq('user_id', userId);

      debugPrint('✅ Order cancelled successfully by user');
      return true;
    } catch (e) {
      debugPrint('❌ Error cancelling order by user: $e');
      return false;
    }
  }

  /// Cancel order by cafeteria
  Future<bool> cancelOrderByCafeteria({
    required String orderId,
    required String cafeteriaId,
    required String reason,
    String? customReason,
  }) async {
    try {
      debugPrint('🚫 Cafeteria cancelling order: $orderId');
      debugPrint('📝 Reason: $reason');
      
      final finalReason = reason == 'Other' ? customReason ?? 'Other' : reason;
      
      // Update order status and cancellation details
      final response = await _client
          .from('orders')
          .update({
            'status': 'cancelled',
            'cancellation_reason': finalReason,
            'cancelled_by': 'cafeteria',
            'cancelled_at': DateTime.now().toIso8601String(),
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', orderId)
          .eq('cafeteria_id', cafeteriaId);

      debugPrint('✅ Order cancelled successfully by cafeteria');
      return true;
    } catch (e) {
      debugPrint('❌ Error cancelling order by cafeteria: $e');
      return false;
    }
  }

  /// Check if order can be cancelled by user
  Future<bool> canUserCancelOrder(String orderId, String userId) async {
    try {
      final response = await _client
          .from('orders')
          .select('status, user_id')
          .eq('id', orderId)
          .eq('user_id', userId)
          .maybeSingle();

      if (response == null) {
        debugPrint('❌ Order not found or user not authorized');
        return false;
      }

      final status = response['status'] as String;
      
      // User can cancel if order is pending or new (not preparing, ready, completed, or already cancelled)
      final canCancel = ['pending', 'new'].contains(status.toLowerCase());
      
      debugPrint('🔍 Order status: $status, Can cancel: $canCancel');
      return canCancel;
    } catch (e) {
      debugPrint('❌ Error checking if user can cancel order: $e');
      return false;
    }
  }

  /// Check if order can be cancelled by cafeteria
  Future<bool> canCafeteriaCancelOrder(String orderId, String cafeteriaId) async {
    try {
      final response = await _client
          .from('orders')
          .select('status, cafeteria_id')
          .eq('id', orderId)
          .eq('cafeteria_id', cafeteriaId)
          .maybeSingle();

      if (response == null) {
        debugPrint('❌ Order not found or cafeteria not authorized');
        return false;
      }

      final status = response['status'] as String;
      
      // Cafeteria can cancel if order is pending, new, or preparing (not ready, completed, or already cancelled)
      final canCancel = ['pending', 'new', 'preparing'].contains(status.toLowerCase());
      
      debugPrint('🔍 Order status: $status, Can cancel: $canCancel');
      return canCancel;
    } catch (e) {
      debugPrint('❌ Error checking if cafeteria can cancel order: $e');
      return false;
    }
  }

  /// Get order cancellation details
  Future<Map<String, dynamic>?> getCancellationDetails(String orderId) async {
    try {
      final response = await _client
          .from('orders')
          .select('cancellation_reason, cancelled_by, cancelled_at, status')
          .eq('id', orderId)
          .maybeSingle();

      if (response == null || response['status'] != 'cancelled') {
        return null;
      }

      return {
        'reason': response['cancellation_reason'],
        'cancelled_by': response['cancelled_by'],
        'cancelled_at': response['cancelled_at'],
      };
    } catch (e) {
      debugPrint('❌ Error getting cancellation details: $e');
      return null;
    }
  }
}
