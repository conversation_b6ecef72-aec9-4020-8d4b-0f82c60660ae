import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

/// Service to handle inventory management and deductions
class InventoryService {
  static final SupabaseService _supabaseService = SupabaseService();

  /// Process completed order and deduct ingredients from inventory
  static Future<Map<String, dynamic>> processCompletedOrder(String orderId) async {
    try {
      debugPrint('🍽️ [INVENTORY] Processing completed order: $orderId');

      // Get order items with menu item details
      final orderItemsResponse = await _supabaseService.client
          .from('order_items')
          .select('''
            item_id,
            quantity,
            menu_items!inner (
              id,
              name,
              cafeteria_id
            )
          ''')
          .eq('order_id', orderId);

      if (orderItemsResponse.isEmpty) {
        debugPrint('⚠️ [INVENTORY] No order items found for order: $orderId');
        return {'success': true, 'deductions': [], 'unavailableMenuItems': []};
      }

      debugPrint('📦 [INVENTORY] Order items found: ${orderItemsResponse.length}');

      final List<Map<String, dynamic>> allDeductions = [];
      String? cafeteriaId;

      // Process each menu item in the order
      for (final orderItem in orderItemsResponse) {
        final menuItemId = orderItem['item_id'];
        final quantity = orderItem['quantity'] ?? 1;
        final menuItem = orderItem['menu_items'];
        
        if (menuItem == null || menuItemId == null) continue;
        
        cafeteriaId ??= menuItem['cafeteria_id'];
        
        debugPrint('🍽️ [INVENTORY] Processing menu item: ${menuItem['name']} (qty: $quantity)');

        // Get ingredients for this menu item
        final ingredients = await _getMenuItemIngredients(menuItemId);
        
        debugPrint('📋 [INVENTORY] Ingredients found: ${ingredients.length}');

        // Deduct ingredients for each quantity ordered
        for (final ingredient in ingredients) {
          final totalQuantityNeeded = ingredient['quantity_needed'] * quantity;
          
          debugPrint('🔢 [INVENTORY] Deducting $totalQuantityNeeded ${ingredient['unit']} of ${ingredient['name']}');

          final deduction = await _deductInventoryItem(
            ingredient['inventory_item_id'],
            totalQuantityNeeded,
            ingredient['name'],
            ingredient['unit'],
          );

          if (deduction != null) {
            allDeductions.add(deduction);
          }
        }
      }

      // Check menu item availability after deductions
      final unavailableMenuItems = cafeteriaId != null 
          ? await _checkAndUpdateMenuItemAvailability(cafeteriaId)
          : <Map<String, dynamic>>[];

      debugPrint('✅ [INVENTORY] Order processing completed');
      debugPrint('📊 [INVENTORY] Total deductions: ${allDeductions.length}');
      debugPrint('⚠️ [INVENTORY] Unavailable menu items: ${unavailableMenuItems.length}');

      return {
        'success': true,
        'deductions': allDeductions,
        'unavailableMenuItems': unavailableMenuItems,
      };

    } catch (error) {
      debugPrint('❌ [INVENTORY] Error processing completed order: $error');
      return {
        'success': false,
        'deductions': [],
        'unavailableMenuItems': [],
        'error': error.toString(),
      };
    }
  }

  /// Get ingredients for a menu item
  static Future<List<Map<String, dynamic>>> _getMenuItemIngredients(String menuItemId) async {
    try {
      final response = await _supabaseService.client
          .from('menu_item_ingredients')
          .select('''
            *,
            inventory_items (
              id,
              name,
              unit
            )
          ''')
          .eq('menu_item_id', menuItemId);

      return response.map<Map<String, dynamic>>((item) => {
        'inventory_item_id': item['inventory_item_id'],
        'name': item['inventory_items']?['name'] ?? 'Unknown Item',
        'quantity_needed': double.parse(item['quantity_needed'].toString()),
        'unit': item['unit'],
      }).toList();

    } catch (error) {
      debugPrint('❌ [INVENTORY] Error fetching menu item ingredients: $error');
      return [];
    }
  }

  /// Deduct quantity from a specific inventory item
  static Future<Map<String, dynamic>?> _deductInventoryItem(
    String inventoryItemId, 
    double quantityToDeduct,
    String itemName,
    String unit,
  ) async {
    try {
      debugPrint('🔽 [INVENTORY] Deducting $quantityToDeduct from inventory item: $inventoryItemId');

      // Get current inventory item
      final inventoryResponse = await _supabaseService.client
          .from('inventory_items')
          .select('id, name, quantity, unit, min_quantity')
          .eq('id', inventoryItemId)
          .maybeSingle();

      if (inventoryResponse == null) {
        debugPrint('❌ [INVENTORY] Inventory item not found: $inventoryItemId');
        return null;
      }

      final currentQuantity = double.parse(inventoryResponse['quantity'].toString());
      final newQuantity = (currentQuantity - quantityToDeduct).clamp(0.0, double.infinity);

      debugPrint('📊 [INVENTORY] ${inventoryResponse['name']}: $currentQuantity → $newQuantity ${inventoryResponse['unit']}');

      // Update inventory quantity
      await _supabaseService.client
          .from('inventory_items')
          .update({
            'quantity': newQuantity,
            'updated_at': DateTime.now().toIso8601String(),
          })
          .eq('id', inventoryItemId);

      // Check if item is now below minimum quantity
      final minQuantity = double.parse(inventoryResponse['min_quantity']?.toString() ?? '0');
      if (newQuantity <= minQuantity) {
        debugPrint('⚠️ [INVENTORY] ${inventoryResponse['name']} is now below minimum quantity ($minQuantity)');
        
        // Update status to low_stock
        await _supabaseService.client
            .from('inventory_items')
            .update({'status': 'low_stock'})
            .eq('id', inventoryItemId);
      }

      return {
        'inventoryItemId': inventoryItemId,
        'inventoryItemName': inventoryResponse['name'],
        'quantityUsed': quantityToDeduct,
        'unit': inventoryResponse['unit'],
        'remainingQuantity': newQuantity,
        'wasSuccessful': true,
      };

    } catch (error) {
      debugPrint('❌ [INVENTORY] Error deducting inventory item: $error');
      return null;
    }
  }

  /// Check menu item availability and update status
  static Future<List<Map<String, dynamic>>> _checkAndUpdateMenuItemAvailability(String cafeteriaId) async {
    try {
      debugPrint('🔍 [INVENTORY] Checking menu item availability for cafeteria: $cafeteriaId');

      // Get all menu items for this cafeteria
      final menuItemsResponse = await _supabaseService.client
          .from('menu_items')
          .select('id, name, is_available')
          .eq('cafeteria_id', cafeteriaId);

      final List<Map<String, dynamic>> unavailableItems = [];

      for (final menuItem in menuItemsResponse) {
        final availability = await _checkSingleMenuItemAvailability(
          menuItem['id'], 
          menuItem['name']
        );

        if (!availability['isAvailable'] && menuItem['is_available'] == true) {
          // Mark menu item as unavailable
          await _supabaseService.client
              .from('menu_items')
              .update({'is_available': false})
              .eq('id', menuItem['id']);

          unavailableItems.add({
            'menuItemId': menuItem['id'],
            'menuItemName': menuItem['name'],
            'missingIngredients': availability['missingIngredients'],
          });
        }
      }

      return unavailableItems;

    } catch (error) {
      debugPrint('❌ [INVENTORY] Error checking menu item availability: $error');
      return [];
    }
  }

  /// Check if a single menu item can be made with current inventory
  static Future<Map<String, dynamic>> _checkSingleMenuItemAvailability(String menuItemId, String menuItemName) async {
    try {
      // Get ingredients for this menu item
      final ingredients = await _getMenuItemIngredients(menuItemId);
      
      if (ingredients.isEmpty) {
        // No ingredients required, item is available
        return {
          'isAvailable': true,
          'missingIngredients': [],
        };
      }

      final List<Map<String, dynamic>> missingIngredients = [];

      for (final ingredient in ingredients) {
        // Get current inventory for this ingredient
        final inventoryResponse = await _supabaseService.client
            .from('inventory_items')
            .select('quantity, status')
            .eq('id', ingredient['inventory_item_id'])
            .maybeSingle();

        if (inventoryResponse == null) continue;

        final availableQuantity = double.parse(inventoryResponse['quantity'].toString());
        
        // Check if there's enough quantity and item is in stock
        if (availableQuantity < ingredient['quantity_needed'] || inventoryResponse['status'] == 'out_of_stock') {
          missingIngredients.add({
            'name': ingredient['name'],
            'required': ingredient['quantity_needed'],
            'available': availableQuantity,
            'unit': ingredient['unit'],
          });
        }
      }

      return {
        'isAvailable': missingIngredients.isEmpty,
        'missingIngredients': missingIngredients,
      };

    } catch (error) {
      debugPrint('❌ [INVENTORY] Error checking single menu item availability: $error');
      return {
        'isAvailable': false,
        'missingIngredients': [],
      };
    }
  }
}
