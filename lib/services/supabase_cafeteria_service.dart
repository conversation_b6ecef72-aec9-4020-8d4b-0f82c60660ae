import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

class SupabaseCafeteriaService {
  static final SupabaseCafeteriaService _instance =
      SupabaseCafeteriaService._internal();
  factory SupabaseCafeteriaService() => _instance;
  SupabaseCafeteriaService._internal();

  final SupabaseService _supabaseService = SupabaseService();

  // Table name
  static const String _tableName = 'cafeterias';

  /// Get all cafeterias
  Future<List<SupabaseCafeteria>> getAllCafeterias() async {
    try {
      debugPrint('Fetching all cafeterias from Supabase...');

      // First, let's check if we can get any cafeterias without filters
      final allResponse =
          await _supabaseService.client.from(_tableName).select();

      debugPrint('Found ${allResponse.length} total cafeterias in database');

      // Now get the active ones with all status fields
      final response = await _supabaseService.client
          .from(_tableName)
          .select('*, operational_status, status_message, status_updated_at, is_open')
          .eq('is_active', true);

      debugPrint('Fetched ${response.length} cafeterias from Supabase');

      // Debug each cafeteria
      for (var cafeteria in response) {
        debugPrint(
            'Cafeteria: ${cafeteria['name']}, ID: ${cafeteria['id']}, is_active: ${cafeteria['is_active']}, is_open: ${cafeteria['is_open']}, operational_status: ${cafeteria['operational_status']}');
      }

      final cafeterias =
          response.map((json) => SupabaseCafeteria.fromJson(json)).toList();

      // Debug the parsed cafeterias
      for (var cafeteria in cafeterias) {
        debugPrint(
            'Parsed Cafeteria: ${cafeteria.name}, ID: ${cafeteria.id}, isActive: ${cafeteria.isActive}, isOpen: ${cafeteria.isOpen}, operationalStatus: ${cafeteria.operationalStatus}');
      }

      return cafeterias;
    } catch (e) {
      debugPrint('Error getting cafeterias: $e');
      return [];
    }
  }

  /// Get a cafeteria by ID
  Future<SupabaseCafeteria?> getCafeteriaById(String id) async {
    try {
      final response = await _supabaseService.client
          .from(_tableName)
          .select('*, operational_status, status_message, status_updated_at, is_open')
          .eq('id', id)
          .single();

      return SupabaseCafeteria.fromJson(response);
    } catch (e) {
      debugPrint('Error getting cafeteria by ID: $e');
      return null;
    }
  }

  /// Create a new cafeteria
  Future<SupabaseCafeteria?> createCafeteria({
    required String name,
    String? description,
    String? imageUrl,
    String? location,
    bool isOpen = true,
  }) async {
    try {
      final cafeteriaData = {
        'name': name,
        'description': description,
        'image_url': imageUrl,
        'location': location,
        'created_at': DateTime.now().toIso8601String(),
        'is_active': true,
        'is_open': isOpen,
      };

      final response = await _supabaseService.client
          .from(_tableName)
          .insert(cafeteriaData)
          .select()
          .single();

      return SupabaseCafeteria.fromJson(response);
    } catch (e) {
      debugPrint('Error creating cafeteria: $e');
      return null;
    }
  }

  /// Update a cafeteria
  Future<SupabaseCafeteria?> updateCafeteria({
    required String id,
    String? name,
    String? description,
    String? imageUrl,
    String? location,
    double? rating,
    bool? isActive,
    bool? isOpen,
  }) async {
    try {
      final updateData = <String, dynamic>{};

      if (name != null) updateData['name'] = name;
      if (description != null) updateData['description'] = description;
      if (imageUrl != null) updateData['image_url'] = imageUrl;
      if (location != null) updateData['location'] = location;
      if (rating != null) updateData['rating'] = rating;
      if (isActive != null) updateData['is_active'] = isActive;
      if (isOpen != null) updateData['is_open'] = isOpen;

      final response = await _supabaseService.client
          .from(_tableName)
          .update(updateData)
          .eq('id', id)
          .select()
          .single();

      return SupabaseCafeteria.fromJson(response);
    } catch (e) {
      debugPrint('Error updating cafeteria: $e');
      return null;
    }
  }

  /// Delete a cafeteria (soft delete by setting is_active to false)
  Future<bool> deleteCafeteria(String id) async {
    try {
      await _supabaseService.client
          .from(_tableName)
          .update({'is_active': false}).eq('id', id);

      return true;
    } catch (e) {
      debugPrint('Error deleting cafeteria: $e');
      return false;
    }
  }

  /// Search cafeterias by name
  Future<List<SupabaseCafeteria>> searchCafeterias(String query) async {
    try {
      final response = await _supabaseService.client
          .from(_tableName)
          .select()
          .eq('is_active', true)
          .ilike('name', '%$query%')
          .order('name');

      return response.map((json) => SupabaseCafeteria.fromJson(json)).toList();
    } catch (e) {
      debugPrint('Error searching cafeterias: $e');
      return [];
    }
  }
}
