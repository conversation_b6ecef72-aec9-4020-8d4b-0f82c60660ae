import 'package:flutter/material.dart';
import '../services/cafeteria_status_service.dart';

class CafeteriaStatusBadge extends StatelessWidget {
  final String cafeteriaId;
  final String? fallbackStatus;
  final bool showLabel;
  final double? fontSize;

  const CafeteriaStatusBadge({
    Key? key,
    required this.cafeteriaId,
    this.fallbackStatus,
    this.showLabel = true,
    this.fontSize,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<CafeteriaStatus>>(
      stream: CafeteriaStatusService().statusStream,
      builder: (context, snapshot) {
        // Find current cafeteria status
        CafeteriaStatus? currentStatus;
        if (snapshot.hasData) {
          currentStatus = snapshot.data!
              .where((status) => status.id == cafeteriaId)
              .firstOrNull;
        }

        // Use current status or fallback
        final operationalStatus = currentStatus?.operationalStatus ?? fallbackStatus ?? 'open';
        final statusDisplay = CafeteriaStatusService().getStatusDisplay(operationalStatus);
        final statusMessage = currentStatus?.statusMessage ?? '';

        return _buildStatusBadge(context, statusDisplay, statusMessage);
      },
    );
  }

  Widget _buildStatusBadge(BuildContext context, StatusDisplay statusDisplay, String statusMessage) {
    Color statusColor;
    switch (statusDisplay.color) {
      case 'green':
        statusColor = Colors.green;
        break;
      case 'orange':
        statusColor = Colors.orange;
        break;
      case 'red':
        statusColor = Colors.red;
        break;
      case 'gray':
      default:
        statusColor = Colors.grey;
        break;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: statusColor,
              shape: BoxShape.circle,
            ),
          ),
          if (showLabel) ...[
            const SizedBox(width: 6),
            Text(
              statusDisplay.label,
              style: TextStyle(
                color: statusColor,
                fontWeight: FontWeight.w600,
                fontSize: fontSize ?? 12,
              ),
            ),
          ],
        ],
      ),
    );
  }
}

class CafeteriaStatusCard extends StatelessWidget {
  final String cafeteriaId;
  final String cafeteriaName;

  const CafeteriaStatusCard({
    Key? key,
    required this.cafeteriaId,
    required this.cafeteriaName,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<CafeteriaStatus>>(
      stream: CafeteriaStatusService().statusStream,
      builder: (context, snapshot) {
        // Find current cafeteria status
        CafeteriaStatus? currentStatus;
        if (snapshot.hasData) {
          currentStatus = snapshot.data!
              .where((status) => status.id == cafeteriaId)
              .firstOrNull;
        }

        if (currentStatus == null) {
          return const SizedBox.shrink();
        }

        final statusDisplay = currentStatus.statusDisplay;
        final hasMessage = currentStatus.statusMessage.isNotEmpty;

        Color statusColor;
        switch (statusDisplay.color) {
          case 'green':
            statusColor = Colors.green;
            break;
          case 'orange':
            statusColor = Colors.orange;
            break;
          case 'red':
            statusColor = Colors.red;
            break;
          case 'gray':
          default:
            statusColor = Colors.grey;
            break;
        }

        return Card(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: statusColor,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Text(
                      statusDisplay.label,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                        color: statusColor,
                      ),
                    ),
                    const Spacer(),
                    Text(
                      statusDisplay.icon,
                      style: const TextStyle(fontSize: 20),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Text(
                  statusDisplay.description,
                  style: TextStyle(
                    color: Colors.grey[600],
                    fontSize: 14,
                  ),
                ),
                if (hasMessage) ...[
                  const SizedBox(height: 8),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: statusColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: statusColor.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          size: 16,
                          color: statusColor,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            currentStatus.statusMessage,
                            style: TextStyle(
                              color: statusColor,
                              fontSize: 13,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 8),
                Text(
                  'Last updated: ${_formatTime(currentStatus.statusUpdatedAt)}',
                  style: TextStyle(
                    color: Colors.grey[500],
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}m ago';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}h ago';
    } else {
      return '${difference.inDays}d ago';
    }
  }
}

class CafeteriaAvailabilityIndicator extends StatelessWidget {
  final String cafeteriaId;
  final Widget child;

  const CafeteriaAvailabilityIndicator({
    Key? key,
    required this.cafeteriaId,
    required this.child,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return StreamBuilder<List<CafeteriaStatus>>(
      stream: CafeteriaStatusService().statusStream,
      builder: (context, snapshot) {
        // Find current cafeteria status
        CafeteriaStatus? currentStatus;
        if (snapshot.hasData) {
          currentStatus = snapshot.data!
              .where((status) => status.id == cafeteriaId)
              .firstOrNull;
        }

        final isAvailable = currentStatus?.isAvailable ?? true;
        final isBusy = currentStatus?.isBusy ?? false;
        final isClosed = currentStatus?.isClosed ?? false;

        return Stack(
          children: [
            // Apply opacity if closed
            Opacity(
              opacity: isClosed ? 0.6 : 1.0,
              child: child,
            ),
            // Overlay for closed/busy status
            if (isClosed || isBusy)
              Positioned.fill(
                child: Container(
                  decoration: BoxDecoration(
                    color: isClosed 
                        ? Colors.black.withOpacity(0.3)
                        : Colors.orange.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Center(
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(
                        color: isClosed ? Colors.red : Colors.orange,
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        isClosed ? 'CLOSED' : 'BUSY',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 12,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
          ],
        );
      },
    );
  }
}
