import 'package:flutter/material.dart';
import 'package:unieatsappv0/models/menu_item.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
// import 'package:unieatsappv0/screens/item_details_screen.dart';

class MenuItemCard extends StatelessWidget {
  final MenuItem menuItem;
  final VoidCallback? onTap;

  const MenuItemCard({
    super.key,
    required this.menuItem,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    CartItem? cartItem;
    for (final item in cartProvider.items) {
      if (item.id == menuItem.id) {
        cartItem = item;
        break;
      }
    }
    int quantity = cartItem?.quantity ?? 0;
    return GestureDetector(
      onTap: () {
        Navigator.of(context).pushNamed(
          '/item_details',
          arguments: menuItem,
        );
      },
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: Theme.of(context).colorScheme.outline.withAlpha(30),
            width: 1,
          ),
        ),
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.symmetric(horizontal: 4, vertical: 6),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Menu item image with badge
            Stack(
              children: [
                // Image
                SizedBox(
                  height: 140,
                  width: double.infinity,
                  child: RobustImage(
                    imageUrl: menuItem.image,
                    width: double.infinity,
                    height: 140,
                    fit: BoxFit.cover,
                    errorWidget: Container(
                      height: 140,
                      width: double.infinity,
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest,
                      child: Icon(
                        Icons.restaurant_menu,
                        size: 40,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    placeholder: Container(
                      height: 140,
                      width: double.infinity,
                      color: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest,
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                  ),
                ),

                // Discount badge if applicable
                if (menuItem.discount > 0)
                  Positioned(
                    top: 12,
                    right: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .secondary
                            .withAlpha(230),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Text(
                        '${menuItem.discount}% OFF',
                        style: Theme.of(context).textTheme.labelSmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),

                // Rating badge if high rating
                if (menuItem.rating >= 4.5)
                  Positioned(
                    top: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 10, vertical: 6),
                      decoration: BoxDecoration(
                        color: Theme.of(context)
                            .colorScheme
                            .primary
                            .withAlpha(230),
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withAlpha(50),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.star,
                            color: Colors.white,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            menuItem.rating.toStringAsFixed(1),
                            style: Theme.of(context)
                                .textTheme
                                .labelSmall
                                ?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Menu item name
                  Text(
                    menuItem.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 6),

                  // Menu item description
                  Text(
                    menuItem.description,
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),

                  const SizedBox(height: 8),

                  // Rating display
                  Row(
                    children: [
                      Row(
                        children: List.generate(5, (index) {
                          return Icon(
                            index < menuItem.rating.floor()
                                ? Icons.star
                                : (index < menuItem.rating
                                    ? Icons.star_half
                                    : Icons.star_border),
                            color: Colors.amber,
                            size: 16,
                          );
                        }),
                      ),
                      const SizedBox(width: 6),
                      Text(
                        menuItem.rating > 0
                            ? '⭐ ${menuItem.rating.toStringAsFixed(1)}'
                            : 'No ratings yet',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Price and add button
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Price with potential discount
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if (menuItem.discount > 0)
                            Text(
                              '${(menuItem.price * (1 + menuItem.discount / 100)).toStringAsFixed(2)} EGP',
                              style: Theme.of(context)
                                  .textTheme
                                  .bodyMedium
                                  ?.copyWith(
                                    decoration: TextDecoration.lineThrough,
                                    color: Theme.of(context)
                                        .colorScheme
                                        .onSurfaceVariant,
                                    fontSize: 12,
                                  ),
                            ),
                          Text(
                            '${menuItem.price.toStringAsFixed(2)} EGP',
                            style: Theme.of(context)
                                .textTheme
                                .titleMedium
                                ?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: Theme.of(context).colorScheme.primary,
                                ),
                          ),
                        ],
                      ),

                      // Quantity controls
                      _buildQuantityControls(quantity, cartProvider),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuantityControls(int quantity, CartProvider cartProvider) {
    if (!menuItem.isAvailable) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
        ),
        child: Text(
          'Unavailable',
          style: ModernTheme.bodySmall.copyWith(
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    if (quantity == 0) {
      // Show add button
      return Container(
        decoration: BoxDecoration(
          gradient: ModernTheme.accentGradient,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: ModernTheme.accentColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            onTap: () async {
              final cartItem = CartItem(
                id: menuItem.id,
                name: menuItem.name,
                price: menuItem.price,
                image: menuItem.image,
                cafeteriaName: '',
                buildingName: '',
                quantity: 1,
                customizations: {},
                menuItem: menuItem,
              );
              await cartProvider.addItem(cartItem, context: context);
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      );
    } else {
      // Show quantity controls
      return Container(
        decoration: BoxDecoration(
          color: ModernTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
          border: Border.all(
            color: ModernTheme.accentColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Decrease button
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                onTap: () {
                  if (quantity > 1) {
                    cartProvider.updateQuantity(menuItem.id, quantity - 1);
                  } else {
                    cartProvider.removeItem(menuItem.id);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    quantity > 1 ? Icons.remove : Icons.delete_outline,
                    color: ModernTheme.accentColor,
                    size: 18,
                  ),
                ),
              ),
            ),
            // Quantity display
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                '$quantity',
                style: ModernTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ModernTheme.accentColor,
                ),
              ),
            ),
            // Increase button
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                onTap: () {
                  cartProvider.updateQuantity(menuItem.id, quantity + 1);
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.add,
                    color: ModernTheme.accentColor,
                    size: 18,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
  }
}
