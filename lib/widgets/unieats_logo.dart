import 'package:flutter/material.dart';

class UniEatsLogo extends StatelessWidget {
  final double size;
  final Color? backgroundColor;
  final bool showShadow;

  const UniEatsLogo({
    Key? key,
    this.size = 120,
    this.backgroundColor,
    this.showShadow = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        shape: BoxShape.circle,
        boxShadow: showShadow ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 10),
          ),
        ] : null,
      ),
      child: ClipOval(
        child: _buildLogoContent(),
      ),
    );
  }

  Widget _buildLogoContent() {
    // Use the provided UniEats logo image
    return Container(
      decoration: const BoxDecoration(
        color: Colors.white, // White background for the logo
      ),
      child: Padding(
        padding: EdgeInsets.all(size * 0.1), // Add some padding
        child: Image.asset(
          'assets/images/logo.jpg',
          width: size,
          height: size,
          fit: BoxFit.contain,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to a simple icon if image fails to load
            return Container(
              decoration: const BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Color(0xFFFFC220), // Yellow background
                    Color(0xFFFFD54F),
                  ],
                ),
              ),
              child: const Icon(
                Icons.restaurant_menu,
                color: Colors.white,
                size: 60,
              ),
            );
          },
        ),
      ),
    );
  }
}


