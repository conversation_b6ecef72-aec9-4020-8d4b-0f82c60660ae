import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

class UniEatsLogo extends StatelessWidget {
  final double size;
  final Color? backgroundColor;
  final bool showShadow;

  const UniEatsLogo({
    Key? key,
    this.size = 120,
    this.backgroundColor,
    this.showShadow = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: backgroundColor ?? Colors.transparent,
        shape: BoxShape.circle,
        boxShadow: showShadow ? [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 20,
            spreadRadius: 0,
            offset: const Offset(0, 10),
          ),
        ] : null,
      ),
      child: ClipOval(
        child: _buildLogoContent(),
      ),
    );
  }

  Widget _buildLogoContent() {
    // Custom UniEats logo with spoon, fork and chat bubble
    return Container(
      decoration: const BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFFFC220), // Yellow background
            Color(0xFFFFD54F),
          ],
        ),
      ),
      child: Stack(
        alignment: Alignment.center,
        children: [
          // Main utensils
          CustomPaint(
            size: Size(size * 0.6, size * 0.6),
            painter: _UniEatsLogoPainter(),
          ),
          
          // Chat bubble in bottom right
          Positioned(
            bottom: size * 0.15,
            right: size * 0.15,
            child: Container(
              width: size * 0.25,
              height: size * 0.25,
              decoration: const BoxDecoration(
                color: Colors.black,
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.chat_bubble_rounded,
                size: size * 0.12,
                color: Colors.white,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _UniEatsLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.black
      ..strokeWidth = 3.0
      ..strokeCap = StrokeCap.round;

    final center = Offset(size.width / 2, size.height / 2);
    
    // Draw spoon (left side)
    final spoonPath = Path();
    spoonPath.moveTo(center.dx - 15, center.dy - 20);
    spoonPath.quadraticBezierTo(
      center.dx - 20, center.dy - 25,
      center.dx - 15, center.dy - 30,
    );
    spoonPath.quadraticBezierTo(
      center.dx - 10, center.dy - 25,
      center.dx - 15, center.dy - 20,
    );
    spoonPath.lineTo(center.dx - 15, center.dy + 20);
    
    canvas.drawPath(spoonPath, paint..style = PaintingStyle.fill);
    
    // Draw fork (right side)
    final forkPath = Path();
    // Fork handle
    forkPath.moveTo(center.dx + 15, center.dy - 15);
    forkPath.lineTo(center.dx + 15, center.dy + 20);
    
    // Fork prongs
    forkPath.moveTo(center.dx + 10, center.dy - 15);
    forkPath.lineTo(center.dx + 10, center.dy - 25);
    forkPath.moveTo(center.dx + 15, center.dy - 15);
    forkPath.lineTo(center.dx + 15, center.dy - 30);
    forkPath.moveTo(center.dx + 20, center.dy - 15);
    forkPath.lineTo(center.dx + 20, center.dy - 25);
    
    canvas.drawPath(forkPath, paint..style = PaintingStyle.stroke);
    
    // Draw diagonal line across
    paint.strokeWidth = 4.0;
    canvas.drawLine(
      Offset(center.dx - 25, center.dy - 35),
      Offset(center.dx + 25, center.dy + 35),
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
