import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/services/cafeteria_status_service.dart';
import 'package:unieatsappv0/widgets/cafeteria_status_dialog.dart';

class CafeteriaCard extends StatelessWidget {
  final SupabaseCafeteria cafeteria;

  const CafeteriaCard({
    super.key,
    required this.cafeteria,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Hero(
      tag: 'cafeteria_${cafeteria.id}',
      child: Card(
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
          side: BorderSide(
            color: theme.colorScheme.outline.withAlpha(30),
            width: 1,
          ),
        ),
        color: theme.colorScheme.surface,
        clipBehavior: Clip.antiAlias,
        margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        child: InkWell(
          onTap: () {
            _handleCafeteriaTap(context);
          },
          child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Cafeteria Image with gradient overlay
            Stack(
              children: [
                // Image
                SizedBox(
                  height: 140,
                  width: double.infinity,
                  child: _buildCafeteriaImage(
                    context,
                    cafeteria.imageUrl,
                    theme,
                  ),
                ),

                // Gradient overlay
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Colors.transparent,
                          Colors.black.withAlpha(120),
                        ],
                        stops: const [0.7, 1.0],
                      ),
                    ),
                  ),
                ),

                // Status badge with real-time updates
                Positioned(
                  top: 12,
                  left: 12,
                  child: StreamBuilder<List<CafeteriaStatus>>(
                    stream: CafeteriaStatusService().statusStream,
                    builder: (context, snapshot) {
                      // Find current cafeteria status
                      CafeteriaStatus? currentStatus;
                      if (snapshot.hasData && snapshot.data != null) {
                        try {
                          final matchingStatuses = snapshot.data!
                              .where((status) => status.id == cafeteria.id)
                              .toList();
                          currentStatus = matchingStatuses.isNotEmpty ? matchingStatuses.first : null;
                        } catch (e) {
                          currentStatus = null;
                        }
                      }

                      // Use current status or fallback to cafeteria's actual operational status
                      final operationalStatus = currentStatus?.operationalStatus ?? cafeteria.operationalStatus;
                      final isOpen = currentStatus?.isOpen ?? cafeteria.isOpen;
                      final statusDisplay = currentStatus?.statusDisplay ??
                          CafeteriaStatusService().getStatusDisplay(operationalStatus);

                      // Debug logging
                      debugPrint('🏪 ${cafeteria.name}: currentStatus=${currentStatus?.operationalStatus}, cafeteriaStatus=${cafeteria.operationalStatus}, final=${operationalStatus}');

                      Color statusColor;
                      switch (operationalStatus) {
                        case 'open':
                          statusColor = Colors.green;
                          break;
                        case 'busy':
                          statusColor = Colors.orange;
                          break;
                        case 'temporarily_closed':
                          statusColor = Colors.yellow.shade700;
                          break;
                        case 'closed':
                        default:
                          statusColor = Colors.red;
                          break;
                      }

                      return Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
                        decoration: BoxDecoration(
                          color: statusColor.withAlpha(230),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Container(
                              width: 8,
                              height: 8,
                              decoration: const BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                              ),
                            ),
                            const SizedBox(width: 6),
                            Text(
                              statusDisplay.label,
                              style: theme.textTheme.labelSmall?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ),

                // Favorite button
                Positioned(
                  top: 12,
                  right: 12,
                  child: Consumer<FavoritesProvider>(
                    builder: (context, favoritesProvider, child) {
                      final isFavorite = favoritesProvider.isCafeteriaFavorite(cafeteria.id);
                      return Container(
                        decoration: BoxDecoration(
                          color: Colors.white.withAlpha(230),
                          shape: BoxShape.circle,
                        ),
                        child: IconButton(
                          icon: Icon(
                            isFavorite ? Icons.favorite : Icons.favorite_border,
                            color: isFavorite ? Colors.red : Colors.grey[600],
                            size: 20,
                          ),
                          onPressed: () async {
                            debugPrint('🔄 Toggling favorite for cafeteria: ${cafeteria.name}');
                            await favoritesProvider.toggleSupabaseCafeteriaFavorite(cafeteria);
                          },
                          padding: const EdgeInsets.all(8),
                          constraints: const BoxConstraints(
                            minWidth: 36,
                            minHeight: 36,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),

            // Content
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Name
                  Text(
                    cafeteria.name,
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Location
                  Row(
                    children: [
                      Icon(
                        Icons.location_on_outlined,
                        size: 16,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          cafeteria.location ?? 'Location not available',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 16),

                  // Rating and distance
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Rating
                      Consumer<CafeteriaRatingsProvider>(
                        builder: (context, ratingsProvider, child) {
                          // Get average rating from provider using cafeteria ID
                          final avgRating = ratingsProvider
                              .getAverageRatingForCafeteria(cafeteria.id);
                          final displayRating = avgRating > 0
                              ? avgRating
                              : (cafeteria.rating ?? 4.0);

                          return Row(
                            children: [
                              Icon(
                                Icons.star,
                                size: 16,
                                color: displayRating >= 4.0
                                    ? Colors.amber
                                    : theme.colorScheme.secondary,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                displayRating.toStringAsFixed(1),
                                style: theme.textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          );
                        },
                      ),

                      // Distance
                      Row(
                        children: [
                          Icon(
                            Icons.access_time,
                            size: 16,
                            color: theme.colorScheme.onSurfaceVariant,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '10-15 min',
                            style: theme.textTheme.bodySmall,
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
        ),
      ),
    ),
    );
  }

  // Handle cafeteria tap with status checking
  void _handleCafeteriaTap(BuildContext context) {
    // Get the current operational status and message from cafeteria data
    String operationalStatus = cafeteria.operationalStatus;
    String statusMessage = cafeteria.statusMessage ?? '';

    print('🎯 Cafeteria ${cafeteria.name} tapped: status=$operationalStatus, message="$statusMessage"');

    // If cafeteria is open, go directly
    if (operationalStatus == 'open') {
      print('✅ Cafeteria is open, navigating directly');
      _navigateToCafeteria(context);
      return;
    }

    // For any other status (busy, temporarily_closed, closed), show dialog
    print('🚨 Showing status dialog for ${cafeteria.name} (status: $operationalStatus)');
    showDialog(
      context: context,
      builder: (context) => CafeteriaStatusDialog(
        cafeteria: cafeteria,
        operationalStatus: operationalStatus,
        statusMessage: statusMessage,
        onContinue: () => _navigateToCafeteria(context),
      ),
    );
  }

  // Navigate to cafeteria page
  void _navigateToCafeteria(BuildContext context) {
    Navigator.pushNamed(
      context,
      '/cafeteria',
      arguments: {
        'id': cafeteria.id,
        'cafeteria': cafeteria,
      },
    );
  }

  // Helper method to build cafeteria image with proper error handling
  Widget _buildCafeteriaImage(
      BuildContext context, String? imageUrl, ThemeData theme) {
    // Default placeholder widget
    final placeholder = Container(
      height: 140,
      width: double.infinity,
      color: theme.colorScheme.surfaceContainerHighest,
      child: Icon(
        Icons.restaurant,
        size: 40,
        color: theme.colorScheme.onSurfaceVariant,
      ),
    );

    // If no image URL, return placeholder
    if (imageUrl == null || imageUrl.isEmpty) {
      return placeholder;
    }

    // If it's a local asset
    if (imageUrl.startsWith('assets/')) {
      return Image.asset(
        imageUrl,
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => placeholder,
      );
    }

    // If it's a network image
    try {
      return Image.network(
        imageUrl,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return Center(
            child: CircularProgressIndicator(
              value: loadingProgress.expectedTotalBytes != null
                  ? loadingProgress.cumulativeBytesLoaded /
                      loadingProgress.expectedTotalBytes!
                  : null,
            ),
          );
        },
        errorBuilder: (context, error, stackTrace) {
          debugPrint('Error loading image: $error');
          return placeholder;
        },
      );
    } catch (e) {
      debugPrint('Exception loading image: $e');
      return placeholder;
    }
  }
}
