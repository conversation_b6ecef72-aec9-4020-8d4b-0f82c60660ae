import 'package:flutter/material.dart';
import '../models/supabase_models.dart';
import '../services/cafeteria_status_service.dart';

class CafeteriaStatusDialog extends StatelessWidget {
  final SupabaseCafeteria cafeteria;
  final String operationalStatus;
  final String statusMessage;
  final VoidCallback? onContinue;

  const CafeteriaStatusDialog({
    super.key,
    required this.cafeteria,
    required this.operationalStatus,
    required this.statusMessage,
    this.onContinue,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final statusDisplay = CafeteriaStatusService().getStatusDisplay(operationalStatus);
    
    // Determine if user can continue to cafeteria
    final canContinue = operationalStatus == 'open' || operationalStatus == 'busy';
    
    // Get status color
    Color statusColor;
    IconData statusIcon;
    switch (operationalStatus) {
      case 'open':
        statusColor = Colors.green;
        statusIcon = Icons.check_circle;
        break;
      case 'busy':
        statusColor = Colors.orange;
        statusIcon = Icons.access_time;
        break;
      case 'temporarily_closed':
        statusColor = Colors.yellow.shade700;
        statusIcon = Icons.pause_circle;
        break;
      case 'closed':
      default:
        statusColor = Colors.red;
        statusIcon = Icons.cancel;
        break;
    }

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(20),
          color: theme.colorScheme.surface,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Status Icon
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: Icon(
                statusIcon,
                size: 40,
                color: statusColor,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Cafeteria Name
            Text(
              cafeteria.name,
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
            
            const SizedBox(height: 8),
            
            // Status Badge
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                color: statusColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: statusColor.withOpacity(0.3)),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: statusColor,
                      shape: BoxShape.circle,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    statusDisplay.label,
                    style: theme.textTheme.labelLarge?.copyWith(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Status Message
            if (statusMessage.isNotEmpty) ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerHighest,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  statusMessage,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              const SizedBox(height: 24),
            ] else ...[
              const SizedBox(height: 8),
            ],
            
            // Action Buttons
            Row(
              children: [
                // Cancel/Close Button
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.of(context).pop(),
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Close',
                      style: theme.textTheme.labelLarge,
                    ),
                  ),
                ),
                
                if (canContinue) ...[
                  const SizedBox(width: 12),
                  // Continue Button (only for open/busy)
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                        onContinue?.call();
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: statusColor,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: Text(
                        operationalStatus == 'busy' 
                            ? 'Continue Anyway' 
                            : 'Continue to Cafeteria',
                        style: theme.textTheme.labelLarge?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ],
        ),
      ),
    );
  }
}
