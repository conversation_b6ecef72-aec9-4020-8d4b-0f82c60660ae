import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/screens/cafeteria_screen.dart';
import 'package:unieatsappv0/screens/cafeterias_screen.dart';
import 'package:unieatsappv0/screens/checkout_screen.dart';
import 'package:unieatsappv0/screens/splash_screen.dart';
import 'package:unieatsappv0/screens/login_screen.dart';
import 'package:unieatsappv0/screens/signup_screen.dart';
import 'package:unieatsappv0/widgets/bottom_navigation.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/screens/account_settings_screen.dart';
import 'package:unieatsappv0/screens/help_screen.dart';
import 'package:unieatsappv0/screens/about_us_screen.dart';
import 'package:unieatsappv0/providers/auth_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/cafeteria_ratings_provider.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/providers/search_provider.dart';
import 'package:unieatsappv0/screens/order_history_screen.dart';
import 'package:unieatsappv0/screens/notifications_screen.dart';
import 'package:unieatsappv0/screens/item_details_screen_new.dart';
import 'package:unieatsappv0/screens/cafeteria_list_screen.dart';
import 'package:unieatsappv0/screens/order_confirmation_screen_new.dart';
import 'package:unieatsappv0/screens/order_tracking_screen.dart';
import 'package:unieatsappv0/screens/initialization_test_screen.dart';
import 'package:unieatsappv0/screens/popular_items_screen.dart';
import 'package:unieatsappv0/services/notification_service.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/utils/navigation_helper.dart';
import 'package:unieatsappv0/services/realtime_service.dart';
import 'package:unieatsappv0/services/order_sync_service.dart';
import 'package:unieatsappv0/services/cafeteria_status_service.dart';
import 'package:unieatsappv0/app_wrapper.dart';

import 'package:unieatsappv0/screens/supabase_test_screen.dart';
import 'package:unieatsappv0/screens/migration_screen.dart';
import 'package:unieatsappv0/screens/supabase_config_screen.dart';
import 'package:unieatsappv0/screens/debug_screen.dart';
import 'package:unieatsappv0/screens/database_check_screen.dart';
import 'package:unieatsappv0/screens/search/search_screen.dart';
import 'package:unieatsappv0/screens/search_test_screen.dart';
import 'package:unieatsappv0/screens/database_compatibility_check.dart';
import 'package:unieatsappv0/screens/rating_test_screen.dart';

import 'package:unieatsappv0/screens/order_sync_test_screen.dart';
import 'package:unieatsappv0/screens/all_previously_ordered_screen.dart';


// Supabase is our backend service
void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Supabase with the new service
  try {
    // Initialize the new SupabaseService
    final supabaseService = SupabaseService();
    await supabaseService.init();
    debugPrint('New SupabaseService initialized successfully');

    // Test the connection
    try {
      final testResponse =
          await supabaseService.client.from('cafeterias').select().limit(1);
      debugPrint(
          'Test query successful: ${testResponse.length} cafeterias found');
    } catch (e) {
      debugPrint('Test query failed: $e');
    }

    // Initialize the real-time service to sync with web app
    final realtimeService = RealtimeService();
    await realtimeService.initialize();
    debugPrint('Real-time service initialized successfully');
  } catch (e) {
    debugPrint('ERROR INITIALIZING SUPABASE: $e');
    // Show a dialog with the error message when the app starts
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // This will run after the first frame is rendered
      // so the app won't be stuck on the splash screen
    });
    // Continue app initialization even if Supabase fails
  }

  // ✅ Initialize notifications
  final notificationService = NotificationService();
  await notificationService.init();
  await notificationService.requestPermissions();

  // ✅ Initialize cafeteria status service for real-time status updates
  final cafeteriaStatusService = CafeteriaStatusService();
  await cafeteriaStatusService.initialize();
  debugPrint('CafeteriaStatusService initialized successfully');

  final prefs = await SharedPreferences.getInstance();
  final isDarkMode = prefs.getBool('isDarkMode') ?? false;

  runApp(MyApp(isDarkMode: isDarkMode));
}

class MyApp extends StatefulWidget {
  final bool isDarkMode;
  const MyApp({super.key, required this.isDarkMode});

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  late bool _isDarkMode;

  @override
  void initState() {
    super.initState();
    _isDarkMode = widget.isDarkMode;
  }

  void toggleTheme() async {
    setState(() {
      _isDarkMode = !_isDarkMode;
    });
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isDarkMode', _isDarkMode);
  }

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Initialize SupabaseProvider first since other providers may depend on it
        ChangeNotifierProvider<SupabaseProvider>(
          create: (_) {
            debugPrint('Creating SupabaseProvider');
            final provider = SupabaseProvider();
            provider.initialize().then((_) {
              debugPrint(
                  'SupabaseProvider initialized with ${provider.cafeterias.length} cafeterias');
            });
            return provider;
          },
          lazy: false, // Initialize immediately
        ),
        // Other providers
        ChangeNotifierProvider<CartProvider>(
          create: (_) {
            final provider = CartProvider();
            // Initialize cart after a short delay to avoid build conflicts
            Future.delayed(const Duration(milliseconds: 100), () {
              provider.initialize();
            });
            return provider;
          },
        ),
        ChangeNotifierProvider<OrderProvider>(create: (_) => OrderProvider()),
        ChangeNotifierProvider<FavoritesProvider>(
          create: (_) {
            final provider = FavoritesProvider();
            provider.initialize();
            return provider;
          },
        ),
        ChangeNotifierProvider<NotificationProvider>(
            create: (_) => NotificationProvider()),
        ChangeNotifierProvider<OrderHistoryProvider>(
          create: (_) {
            final provider = OrderHistoryProvider();
            // Load orders after a short delay to avoid build conflicts
            Future.delayed(const Duration(milliseconds: 100), () {
              provider.loadOrders();
            });
            return provider;
          },
        ),
        ChangeNotifierProvider<CafeteriaRatingsProvider>(
          create: (_) => CafeteriaRatingsProvider()..initialize(),
        ),
        // Keep AuthProvider for backward compatibility, but it's no longer used for login/signup
        ChangeNotifierProvider<AuthProvider>(create: (_) => AuthProvider()),

        // Add SimpleAuthProvider for simplified auth
        ChangeNotifierProvider<SimpleAuthProvider>(
            create: (_) => SimpleAuthProvider()),

        // Add SearchProvider for search functionality
        ChangeNotifierProvider<SearchProvider>(
            create: (_) => SearchProvider()),
      ],
      child: MaterialApp(
        navigatorKey: NavigationHelper.navigatorKey,
        title: 'UniEats',
        theme: ModernTheme.lightTheme,
        debugShowCheckedModeBanner: false,
        themeMode: _isDarkMode ? ThemeMode.dark : ThemeMode.light,
        // Start with the splash screen for normal app flow
        home: const SplashScreen(),
        onGenerateRoute: (settings) {
          final routeSettings =
              RouteSettings(name: settings.name, arguments: settings.arguments);

          switch (settings.name) {
            case '/login':
              return PageRouteBuilder(
                settings: routeSettings,
                pageBuilder: (_, animation, __) => const LoginScreen(),
                transitionsBuilder: (_, animation, __, child) =>
                    FadeTransition(opacity: animation, child: child),
              );
            case '/signup':
              return PageRouteBuilder(
                settings: routeSettings,
                pageBuilder: (_, animation, __) => const SignupScreen(),
                transitionsBuilder: (_, animation, __, child) =>
                    SlideTransition(
                        position: Tween(
                                begin: const Offset(1.0, 0.0), end: Offset.zero)
                            .animate(animation),
                        child: child),
              );
            case '/dashboard':
              return _fadeRoute(
                  const AppWrapper(child: BottomNavigation(currentIndex: 0)),
                  routeSettings);
            case '/favorites':
              return _fadeRoute(
                  const AppWrapper(child: BottomNavigation(currentIndex: 1)),
                  routeSettings);
            case '/cart':
              return _slideRoute(
                  const AppWrapper(child: BottomNavigation(currentIndex: 2)),
                  routeSettings);
            case '/profile':
              return _fadeRoute(
                  const AppWrapper(child: BottomNavigation(currentIndex: 3)),
                  routeSettings);
            case '/cafeterias':
              return _fadeRoute(
                  const AppWrapper(child: CafeteriasScreen()), routeSettings);
            case '/checkout':
              return _slideRoute(
                  const AppWrapper(child: CheckoutScreen()), routeSettings,
                  vertical: true);
            case '/orders':
              return _slideRoute(
                  const AppWrapper(child: OrderHistoryScreen()), routeSettings,
                  vertical: true);
            case '/profile/account-settings':
              return _slideRoute(
                  AppWrapper(
                      child:
                          AccountSettingsScreen(onThemeChanged: toggleTheme)),
                  routeSettings);
            case '/profile/help':
              return _fadeRoute(
                  const AppWrapper(child: HelpScreen()), routeSettings);
            case '/profile/about-us':
              return _fadeRoute(
                  const AppWrapper(child: AboutUsScreen()), routeSettings);
            case '/notifications':
              return _slideRoute(const AppWrapper(child: NotificationsScreen()),
                  routeSettings);
            case '/item_details':
              final menuItem = settings.arguments;
              if (menuItem == null) {
                debugPrint('❌ Error: No menu item provided to item details screen');
                return MaterialPageRoute(
                  builder: (context) => const Scaffold(
                    body: Center(child: Text('Error: No item data provided')),
                  ),
                );
              }
              return _fadeRoute(
                  AppWrapper(child: ItemDetailsScreenNew(menuItem: menuItem)),
                  routeSettings);
            case '/cafeteria':
              final args = settings.arguments as Map<String, dynamic>?;
              if (args == null) {
                return MaterialPageRoute(
                  builder: (_) => const Scaffold(
                      body: Center(child: Text('Cafeteria not found'))),
                );
              }
              return _fadeRoute(
                AppWrapper(
                  child: CafeteriaScreen(
                    cafeteriaId: args['id'] as String,
                    cafeteria: args['cafeteria'] as SupabaseCafeteria?,
                  ),
                ),
                routeSettings,
              );
            case '/cafeteria_list':
              return _fadeRoute(const AppWrapper(child: CafeteriaListScreen()),
                  routeSettings);
            case '/order_confirmation_new':
              final order = settings.arguments as dynamic;
              return _fadeRoute(
                  AppWrapper(child: OrderConfirmationScreenNew(order: order)),
                  routeSettings);
            case '/order_tracking':
              final order = settings.arguments as dynamic;
              return _fadeRoute(
                  AppWrapper(child: OrderTrackingScreen(order: order)),
                  routeSettings);
            case '/popular_items':
              return _fadeRoute(
                  const AppWrapper(child: PopularItemsScreen()), routeSettings);

            case '/supabase_test':
              return _fadeRoute(const SupabaseTestScreen(), routeSettings);
            case '/initialization_test':
              return _fadeRoute(const InitializationTestScreen(), routeSettings);
            case '/migration':
              return _fadeRoute(const MigrationScreen(), routeSettings);
            case '/supabase_config':
              return _fadeRoute(const SupabaseConfigScreen(), routeSettings);
            case '/debug':
              return _fadeRoute(const DebugScreen(), routeSettings);
            case '/database-check':
              return _fadeRoute(const DatabaseCheckScreen(), routeSettings);
            case '/search':
              return _fadeRoute(
                  const AppWrapper(child: SearchScreen()), routeSettings);
            case '/search-test':
              return _fadeRoute(const SearchTestScreen(), routeSettings);
            case '/database-compatibility':
              return _fadeRoute(const DatabaseCompatibilityCheck(), routeSettings);
            case '/rating-test':
              return _fadeRoute(const RatingTestScreen(), routeSettings);

            case '/order-sync-test':
              return _fadeRoute(const OrderSyncTestScreen(), routeSettings);
            case '/all-previously-ordered':
              return _fadeRoute(const AppWrapper(child: AllPreviouslyOrderedScreen()), routeSettings);
            default:
              return null;
          }
        },
      ),
    );
  }

  PageRouteBuilder _fadeRoute(Widget page, RouteSettings settings) {
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (_, animation, __) =>
          FadeTransition(opacity: animation, child: page),
    );
  }

  PageRouteBuilder _slideRoute(Widget page, RouteSettings settings,
      {bool vertical = false}) {
    final begin = vertical ? const Offset(0.0, 1.0) : const Offset(1.0, 0.0);
    return PageRouteBuilder(
      settings: settings,
      pageBuilder: (_, animation, __) => SlideTransition(
          position: Tween(begin: begin, end: Offset.zero).animate(animation),
          child: page),
    );
  }
}
