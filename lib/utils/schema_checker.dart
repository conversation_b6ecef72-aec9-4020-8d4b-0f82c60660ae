import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';

/// Utility to check the actual database schema
class SchemaChecker {
  static final SupabaseService _supabaseService = SupabaseService();

  /// Check the order_items table schema
  static Future<Map<String, dynamic>> checkOrderItemsSchema() async {
    try {
      debugPrint('🔍 Checking order_items table schema...');
      
      // Query to get table schema information
      final response = await _supabaseService.client
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable')
          .eq('table_name', 'order_items')
          .eq('table_schema', 'public');

      debugPrint('📋 order_items columns found: ${response.length}');
      
      for (final column in response) {
        debugPrint('   - ${column['column_name']}: ${column['data_type']} (nullable: ${column['is_nullable']})');
      }

      return {
        'success': true,
        'columns': response,
        'column_names': response.map((col) => col['column_name']).toList(),
      };
    } catch (e) {
      debugPrint('❌ Error checking schema: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Check the orders table schema
  static Future<Map<String, dynamic>> checkOrdersSchema() async {
    try {
      debugPrint('🔍 Checking orders table schema...');
      
      final response = await _supabaseService.client
          .from('information_schema.columns')
          .select('column_name, data_type, is_nullable')
          .eq('table_name', 'orders')
          .eq('table_schema', 'public');

      debugPrint('📋 orders columns found: ${response.length}');
      
      for (final column in response) {
        debugPrint('   - ${column['column_name']}: ${column['data_type']} (nullable: ${column['is_nullable']})');
      }

      return {
        'success': true,
        'columns': response,
        'column_names': response.map((col) => col['column_name']).toList(),
      };
    } catch (e) {
      debugPrint('❌ Error checking orders schema: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Test a simple query to order_items to see what works
  static Future<Map<String, dynamic>> testOrderItemsQuery() async {
    try {
      debugPrint('🔍 Testing simple order_items query...');
      
      // Try different column combinations to see what works
      final tests = [
        'id, order_id, menu_item_id, quantity, price',
        'id, order_id, menu_item_id, quantity, price, notes',
        'id, order_id, item_id, quantity, price',
        'id, order_id, item_id, quantity, price, customizations',
        '*',
      ];

      final results = <String, dynamic>{};

      for (final selectClause in tests) {
        try {
          debugPrint('   Testing: SELECT $selectClause');
          final response = await _supabaseService.client
              .from('order_items')
              .select(selectClause)
              .limit(1);
          
          results[selectClause] = {
            'success': true,
            'count': response.length,
            'sample': response.isNotEmpty ? response.first : null,
          };
          debugPrint('   ✅ Success: $selectClause');
        } catch (e) {
          results[selectClause] = {
            'success': false,
            'error': e.toString(),
          };
          debugPrint('   ❌ Failed: $selectClause - $e');
        }
      }

      return {
        'success': true,
        'test_results': results,
      };
    } catch (e) {
      debugPrint('❌ Error testing queries: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Get sample data from order_items to understand the structure
  static Future<Map<String, dynamic>> getSampleOrderItems() async {
    try {
      debugPrint('🔍 Getting sample order_items data...');
      
      // Try to get any data we can
      final response = await _supabaseService.client
          .from('order_items')
          .select('*')
          .limit(3);

      debugPrint('📋 Found ${response.length} order items');
      
      if (response.isNotEmpty) {
        debugPrint('📋 Sample order item structure:');
        final sample = response.first;
        for (final key in sample.keys) {
          debugPrint('   - $key: ${sample[key]} (${sample[key].runtimeType})');
        }
      }

      return {
        'success': true,
        'count': response.length,
        'samples': response,
        'structure': response.isNotEmpty ? response.first.keys.toList() : [],
      };
    } catch (e) {
      debugPrint('❌ Error getting sample data: $e');
      return {
        'success': false,
        'error': e.toString(),
      };
    }
  }

  /// Run all schema checks
  static Future<Map<String, dynamic>> runAllChecks() async {
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
    };

    try {
      debugPrint('🔍 Running complete schema check...');

      // Check order_items schema
      results['order_items_schema'] = await checkOrderItemsSchema();
      
      // Check orders schema
      results['orders_schema'] = await checkOrdersSchema();
      
      // Test queries
      results['query_tests'] = await testOrderItemsQuery();
      
      // Get sample data
      results['sample_data'] = await getSampleOrderItems();

      debugPrint('✅ Schema check completed');
      return results;

    } catch (e) {
      debugPrint('❌ Error during schema check: $e');
      results['error'] = e.toString();
      return results;
    }
  }
}
