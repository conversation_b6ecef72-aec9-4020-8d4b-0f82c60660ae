import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/services/supabase_order_service.dart';
import 'package:unieatsappv0/screens/order_confirmation_screen_new.dart';
import 'package:unieatsappv0/utils/order_status_utils.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/payment_methods_provider.dart';
import 'package:unieatsappv0/models/payment_method.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';

class CheckoutScreen extends StatefulWidget {
  const CheckoutScreen({super.key});

  @override
  State<CheckoutScreen> createState() => _CheckoutScreenState();
}

class _CheckoutScreenState extends State<CheckoutScreen>
    with TickerProviderStateMixin {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  // Pickup time selection
  String? _selectedPickupTime;

  // Generate all possible pickup slots
  final List<Map<String, String>> _allPickupSlots = [
    {'time': '9:30 AM - 10:00 AM', 'startHour': '9', 'startMinute': '30'},
    {'time': '10:00 AM - 10:30 AM', 'startHour': '10', 'startMinute': '0'},
    {'time': '10:30 AM - 11:00 AM', 'startHour': '10', 'startMinute': '30'},
    {'time': '11:00 AM - 11:30 AM', 'startHour': '11', 'startMinute': '0'},
    {'time': '11:30 AM - 12:00 PM', 'startHour': '11', 'startMinute': '30'},
    {'time': '12:00 PM - 12:30 PM', 'startHour': '12', 'startMinute': '0'},
    {'time': '12:30 PM - 1:00 PM', 'startHour': '12', 'startMinute': '30'},
    {'time': '1:00 PM - 1:30 PM', 'startHour': '13', 'startMinute': '0'},
    {'time': '1:30 PM - 2:00 PM', 'startHour': '13', 'startMinute': '30'},
    {'time': '2:00 PM - 2:30 PM', 'startHour': '14', 'startMinute': '0'},
    {'time': '2:30 PM - 3:00 PM', 'startHour': '14', 'startMinute': '30'},
    {'time': '3:00 PM - 3:30 PM', 'startHour': '15', 'startMinute': '0'},
  ];

  // Get available pickup slots based on current time
  List<Map<String, String>> get _availablePickupSlots {
    final now = DateTime.now();
    final currentHour = now.hour;
    final currentMinute = now.minute;

    return _allPickupSlots.where((slot) {
      final slotHour = int.parse(slot['startHour']!);
      final slotMinute = int.parse(slot['startMinute']!);

      // Add 10 minutes buffer to current time
      final bufferTime = now.add(const Duration(minutes: 10));
      final slotDateTime = DateTime(now.year, now.month, now.day, slotHour, slotMinute);

      // Only show slots that are at least 10 minutes from now
      return slotDateTime.isAfter(bufferTime);
    }).toList();
  }

  // Payment method
  String _paymentMethod = 'card';
  PaymentMethod? _selectedPaymentMethod;
  final _cardNumberController = TextEditingController();
  final _expiryController = TextEditingController();
  final _cvvController = TextEditingController();

  // User information from auth provider
  bool _useProfileInfo = true;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _loadPaymentMethods();

    // In a real app, we would populate from the auth provider
    if (_useProfileInfo) {
      _nameController.text = 'John Doe';
      _emailController.text = '<EMAIL>';
      _phoneController.text = '+20 ************';
    }
  }

  void _loadPaymentMethods() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
      final paymentProvider = Provider.of<PaymentMethodsProvider>(context, listen: false);

      if (authProvider.currentUser != null) {
        paymentProvider.loadPaymentMethods(authProvider.currentUser!.id).then((_) {
          if (mounted && paymentProvider.defaultPaymentMethod != null) {
            setState(() {
              _selectedPaymentMethod = paymentProvider.defaultPaymentMethod;
            });
          }
        });
      }
    });
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _nameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    _cardNumberController.dispose();
    _expiryController.dispose();
    _cvvController.dispose();
    super.dispose();
  }

  void _handleCheckout() async {
    // Check if there are available pickup slots
    if (_availablePickupSlots.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('No pickup slots available today. Please try again tomorrow.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    // Check if pickup time is selected
    if (_selectedPickupTime == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Please select a pickup time.'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_formKey.currentState!.validate() && _selectedPickupTime != null) {
      // We don't need to set local loading state anymore
      // The SupabaseProvider will handle the loading state

      try {
        // Generate unique order number
        final orderNumber =
            DateTime.now().millisecondsSinceEpoch.toString().substring(5) +
                (1000 + (DateTime.now().microsecond % 9000)).toString();
        final cartProvider = Provider.of<CartProvider>(context, listen: false);
        final supabaseProvider =
            Provider.of<SupabaseProvider>(context, listen: false);
        final cartItems = cartProvider.items;
        final total = cartProvider.total;

        // Get cafeteria ID directly from the menu item instead of matching by name
        final cafeteriaId = cartItems.isNotEmpty && cartItems.first.menuItem != null
            ? cartItems.first.menuItem!.cafeteriaId
            : (cartItems.isNotEmpty
                ? _getCafeteriaIdFromName(supabaseProvider, cartItems.first.cafeteriaName)
                : null);

        if (cafeteriaId == null) {
          final cafeteriaName = cartItems.isNotEmpty ? cartItems.first.cafeteriaName : 'Unknown';
          throw Exception('Could not find cafeteria ID for "$cafeteriaName". Please try again or contact support.');
        }

        // Check if user is logged in via SimpleAuthProvider
        final simpleAuthProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
        bool supabaseOrderCreated = false;
        String finalOrderNumber = orderNumber; // Default to generated order number
        String finalOrderId = 'order_${DateTime.now().millisecondsSinceEpoch}'; // Default local ID

        if (simpleAuthProvider.isAuthenticated && simpleAuthProvider.currentUser != null) {
          debugPrint('User is authenticated via SimpleAuthProvider, creating order in Supabase');
          debugPrint('User: ${simpleAuthProvider.currentUser!.fullName} (${simpleAuthProvider.currentUser!.id})');

          // Debug: Print cart items and their notes
          debugPrint('=== CHECKOUT DEBUG: Cart Items ===');
          for (int i = 0; i < cartItems.length; i++) {
            final item = cartItems[i];
            debugPrint('Cart item ${i + 1}: ${item.name} (${item.id})');
            debugPrint('  Notes: "${item.notes}"');
            debugPrint('  Notes is null: ${item.notes == null}');
            debugPrint('  Notes is empty: ${item.notes?.isEmpty}');
          }

          // Create Supabase order items
          final orderItems = cartItems
              .map((item) => SupabaseOrderItem(
                    id: '', // Will be generated by the service
                    orderId: '', // Will be set by the service
                    menuItemId: item.menuItemId, // Use the getter instead of id directly
                    quantity: item.quantity,
                    price: item.price,
                    notes: item.notes,
                  ))
              .toList();

          // Debug: Print order items and their notes
          debugPrint('=== CHECKOUT DEBUG: Order Items ===');
          for (int i = 0; i < orderItems.length; i++) {
            final item = orderItems[i];
            debugPrint('Order item ${i + 1}: ${item.menuItemId}');
            debugPrint('  Notes: "${item.notes}"');
            debugPrint('  Notes is null: ${item.notes == null}');
            debugPrint('  Notes is empty: ${item.notes?.isEmpty}');
          }

          // Create order in Supabase using the authenticated user's ID
          final orderService = SupabaseOrderService();
          final createdOrder = await orderService.createOrderWithUserId(
            userId: simpleAuthProvider.currentUser!.id,
            cafeteriaId: cafeteriaId,
            totalAmount: total,
            items: orderItems,
            orderNumber: orderNumber, // Pass the order number to Supabase
            pickupTime: _selectedPickupTime, // Pass pickup time to Supabase
            paymentMethod: _paymentMethod, // Pass payment method to Supabase
          );

          if (createdOrder != null) {
            supabaseOrderCreated = true;
            // Use the Supabase order ID and number for consistency
            finalOrderId = createdOrder.id;
            finalOrderNumber = createdOrder.orderNumber ?? orderNumber;
            debugPrint('Order created successfully in Supabase with ID: ${createdOrder.id}, Order Number: $finalOrderNumber');
          } else {
            debugPrint(
                'Failed to create order in Supabase, continuing with local order');
          }
        } else {
          debugPrint(
              'User is not authenticated, redirecting to login');

          // Redirect to login screen if user is not authenticated
          if (mounted) {
            Navigator.of(context).pushReplacementNamed('/login');
            return;
          }
        }

        // Create order in local providers (always do this for backward compatibility)
        // Use the same ID and order number as Supabase for consistency
        final order = Order(
          id: finalOrderId, // Use Supabase order ID if available
          orderNumber: finalOrderNumber, // Use Supabase order number if available
          userId: simpleAuthProvider.currentUser?.id ?? 'user_1', // Use actual user ID from auth provider
          orderDate: DateTime.now(),
          items: List.from(cartItems),
          totalPrice: total,
          pickupTime: _selectedPickupTime!,
          status: OrderStatusUtils.statusPending, // Use pending status to match database
          paymentMethod: _paymentMethod, // Include payment method
        );

        // Save order to current orders
        if (mounted) {
          Provider.of<OrderProvider>(context, listen: false).addOrder(order);
          // Save order to order history
          Provider.of<OrderHistoryProvider>(context, listen: false)
              .addOrder(order);

          // Create a fresh notification for the order
          final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
          await notificationProvider.addOrderStatusNotification(
            finalOrderNumber,
            'Order Received',
            cafeteriaName: cartItems.isNotEmpty ? cartItems.first.cafeteriaName : null,
            orderId: order.id, // Pass the actual order ID (UUID)
          );

          // Clear cart after successful checkout
          cartProvider.clearCart();
        }

        // Navigate to confirmation screen
        if (mounted) {
          Navigator.of(context).pushReplacement(
            MaterialPageRoute(
              builder: (_) => OrderConfirmationScreenNew(
                order: order,
                supabaseOrderCreated: supabaseOrderCreated,
              ),
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          SnackBarUtils.showErrorSnackBar(
            context: context,
            message: e.toString(),
            title: 'Error',
          );
        }
      } finally {
        // We don't need to reset local loading state
        // The SupabaseProvider will handle this
      }
    }
  }

  // Helper method to get cafeteria ID from name
  String? _getCafeteriaIdFromName(
      SupabaseProvider provider, String cafeteriaName) {
    if (provider.cafeterias.isEmpty) {
      debugPrint('No cafeterias available in provider, forcing reload');
      // Force reload cafeterias
      provider.loadCafeterias();
      // Return null to force an error - we need cafeterias loaded first
      return null;
    }

    try {
      // Debug all cafeterias
      debugPrint('Available cafeterias:');
      for (var c in provider.cafeterias) {
        debugPrint('- ${c.name} (ID: ${c.id})');
      }

      // First try exact match
      final exactMatch =
          provider.cafeterias.where((c) => c.name == cafeteriaName).toList();
      if (exactMatch.isNotEmpty) {
        debugPrint(
            'Found exact match cafeteria: ${exactMatch.first.name}, ID: ${exactMatch.first.id}');
        return exactMatch.first.id;
      }

      // If no exact match, try contains match
      final containsMatch = provider.cafeterias
          .where((c) =>
              c.name.toLowerCase().contains(cafeteriaName.toLowerCase()) ||
              cafeteriaName.toLowerCase().contains(c.name.toLowerCase()))
          .toList();

      if (containsMatch.isNotEmpty) {
        debugPrint(
            'Found partial match cafeteria: ${containsMatch.first.name}, ID: ${containsMatch.first.id}');
        return containsMatch.first.id;
      }

      // If still no match, DO NOT use a random cafeteria - this causes wrong orders!
      debugPrint('ERROR: Could not find cafeteria "$cafeteriaName" in available cafeterias');
      debugPrint('This will cause orders to be assigned to wrong cafeteria!');

      // Return null to force an error rather than assigning to wrong cafeteria
      return null;
    } catch (e) {
      debugPrint('Error finding cafeteria by name: $e');
      // Return null to force an error rather than assigning to wrong cafeteria
      return null;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final cartProvider = Provider.of<CartProvider>(context);
    final cartItems = cartProvider.items;
    final subtotal = cartProvider.subtotal;
    final serviceFee = cartProvider.serviceFee;
    final total = cartProvider.total;

    // Get cafeteria info from arguments or cart
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    final cafeteriaName = args?['cafeteriaName'] ??
        (cartItems.isNotEmpty ? cartItems.first.cafeteriaName : 'Cafeteria');
    final cafeteriaLocation = args?['cafeteriaLocation'] ??
        (cartItems.isNotEmpty ? cartItems.first.buildingName : '');

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: cartItems.isEmpty
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.shopping_cart_outlined,
                    size: 80,
                    color: theme.textTheme.bodySmall?.color,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Your cart is empty',
                    style: theme.textTheme.displaySmall,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Add items to your cart to checkout',
                    style: theme.textTheme.bodyLarge?.copyWith(
                      color: theme.textTheme.bodySmall?.color,
                    ),
                  ),
                  const SizedBox(height: 24),
                  ElevatedButton(
                    onPressed: () {
                      Navigator.of(context).pushReplacementNamed('/dashboard');
                    },
                    child: const Text('Browse Menu'),
                  ),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Form(
                key: _formKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Pickup Policy Warning
                    Card(
                      color: theme.colorScheme.surface,
                      margin: const EdgeInsets.only(bottom: 16),
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Icon(Icons.warning_amber_rounded,
                                color: Colors.amber[800]),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    'Pickup & Penalty Policy',
                                    style: theme.textTheme.titleMedium
                                        ?.copyWith(fontWeight: FontWeight.bold),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    'Orders must be picked up within 30 minutes after they are marked as "Ready for Pickup" to avoid penalties.',
                                    style: theme.textTheme.bodyMedium,
                                  ),
                                  TextButton(
                                    onPressed: () {
                                      // Show detailed penalty policy dialog
                                      showDialog(
                                        context: context,
                                        builder: (context) => AlertDialog(
                                          title: const Text(
                                              'Pickup & Penalty Policy'),
                                          content: const SingleChildScrollView(
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Text(
                                                  'Late Pickup Penalty',
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'If a student does not pick up their order within a specified time (30 minutes after notification), they may receive a warning. After repeated offenses (3 times), they could face a temporary suspension from ordering.',
                                                ),
                                                SizedBox(height: 16),
                                                Text(
                                                  'Order Cancellation Penalty',
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'Users canceling orders after a vendor has already prepared the food could be charged a small fee or receive a strike. After multiple strikes, their account could be restricted.',
                                                ),
                                                SizedBox(height: 16),
                                                Text(
                                                  'No-show Penalty',
                                                  style: TextStyle(
                                                      fontWeight:
                                                          FontWeight.bold),
                                                ),
                                                SizedBox(height: 8),
                                                Text(
                                                  'If a user doesn\'t pick up their order at all, they could be automatically charged and flagged for a no-show. Repeated no-shows could lead to limited access or account suspension.',
                                                ),
                                              ],
                                            ),
                                          ),
                                          actions: [
                                            TextButton(
                                              onPressed: () =>
                                                  Navigator.of(context).pop(),
                                              child: const Text('I Understand'),
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                    style: TextButton.styleFrom(
                                        padding: EdgeInsets.zero),
                                    child: const Text(
                                        'Learn more about our pickup policy'),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                    // Pickup Location
                    Text('Pickup Location',
                        style: theme.textTheme.displaySmall),
                    const SizedBox(height: 8),
                    Card(
                      child: ListTile(
                        leading: Icon(Icons.store, color: theme.primaryColor),
                        title: Text(cafeteriaName),
                        subtitle: Text(cafeteriaLocation),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Pickup Time
                    Text('Pickup Time', style: theme.textTheme.displaySmall),
                    const SizedBox(height: 8),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.access_time,
                                    color: theme.colorScheme.primary),
                                const SizedBox(width: 8),
                                Text('Select Pickup Time',
                                    style: theme.textTheme.titleMedium),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                                'Choose when you\'d like to pick up your order',
                                style: theme.textTheme.bodySmall),
                            const SizedBox(height: 16),

                            // Time slots in a horizontal scrollable list
                            SizedBox(
                              height: 80, // Reduced height to prevent overflow
                              child: _availablePickupSlots.isEmpty
                                  ? Center(
                                      child: Container(
                                        padding: const EdgeInsets.all(16),
                                        decoration: BoxDecoration(
                                          color: Colors.orange.shade50,
                                          borderRadius: BorderRadius.circular(12),
                                          border: Border.all(
                                            color: Colors.orange.shade200,
                                          ),
                                        ),
                                        child: Text(
                                          'No pickup slots available today.\nPlease try again tomorrow.',
                                          style: theme.textTheme.bodyMedium?.copyWith(
                                            color: Colors.orange.shade700,
                                          ),
                                          textAlign: TextAlign.center,
                                        ),
                                      ),
                                    )
                                  : ListView.builder(
                                      scrollDirection: Axis.horizontal,
                                      padding: const EdgeInsets.symmetric(horizontal: 4),
                                      itemCount: _availablePickupSlots.length,
                                      itemBuilder: (context, index) {
                                        final slot = _availablePickupSlots[index];
                                        final isSelected =
                                            _selectedPickupTime == slot['time'];

                                        return Padding(
                                          padding: const EdgeInsets.only(right: 8),
                                          child: InkWell(
                                            onTap: () {
                                              setState(() {
                                                _selectedPickupTime = slot['time'];
                                              });
                                            },
                                            borderRadius: BorderRadius.circular(12),
                                            child: Container(
                                              width: 120, // Increased width to accommodate longer text
                                              padding: const EdgeInsets.symmetric(
                                                horizontal: 8,
                                                vertical: 12,
                                              ),
                                              decoration: BoxDecoration(
                                                color: isSelected
                                                    ? theme.colorScheme.primary
                                                        .withAlpha(30)
                                                    : theme.colorScheme
                                                        .surfaceContainerHighest,
                                                borderRadius:
                                                    BorderRadius.circular(12),
                                                border: Border.all(
                                                  color: isSelected
                                                      ? theme.colorScheme.primary
                                                      : theme.colorScheme.outline
                                                          .withAlpha(50),
                                                  width: isSelected ? 2 : 1,
                                                ),
                                              ),
                                              child: Center(
                                                child: Text(
                                                  slot['time']!,
                                                  style: theme.textTheme.bodyMedium
                                                      ?.copyWith(
                                                    color: isSelected
                                                        ? theme.colorScheme.primary
                                                        : null,
                                                    fontWeight: isSelected
                                                        ? FontWeight.bold
                                                        : FontWeight.normal,
                                                    fontSize: 12, // Smaller font to fit better
                                                  ),
                                                  textAlign: TextAlign.center,
                                                  maxLines: 2, // Allow text to wrap
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ),
                                          ),
                                  );
                                },
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Order Summary
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Order Summary',
                              style: theme.textTheme.displaySmall,
                            ),
                            const SizedBox(height: 16),
                            ...cartItems.map((item) => Padding(
                                  padding: const EdgeInsets.only(bottom: 8),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      Expanded(
                                        child: Text(
                                          '${item.quantity}x ${item.name}',
                                          style: theme.textTheme.bodyMedium,
                                        ),
                                      ),
                                      Text(
                                        '${item.calculateItemTotal().toStringAsFixed(2)} EGP',
                                        style: theme.textTheme.bodyMedium,
                                      ),
                                    ],
                                  ),
                                )),
                            const Divider(height: 24),
                            _buildSummaryRow('Subtotal',
                                '${subtotal.toStringAsFixed(2)} EGP'),
                            const SizedBox(height: 8),
                            _buildSummaryRow('Service Fee (4%)',
                                '${serviceFee.toStringAsFixed(2)} EGP'),
                            const Divider(height: 24),
                            _buildSummaryRow(
                              'Total',
                              '${total.toStringAsFixed(2)} EGP',
                              isBold: true,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Contact Information
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.person,
                                    color: theme.colorScheme.primary),
                                const SizedBox(width: 8),
                                Text(
                                  'Contact Information',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 16),

                            // Use profile info checkbox
                            CheckboxListTile(
                              value: _useProfileInfo,
                              onChanged: (value) {
                                setState(() {
                                  _useProfileInfo = value ?? true;
                                  if (_useProfileInfo) {
                                    // In a real app, we would populate from the auth provider
                                    _nameController.text = 'John Doe';
                                    _emailController.text =
                                        '<EMAIL>';
                                    _phoneController.text = '+20 ************';
                                  }
                                });
                              },
                              title: Text(
                                'Use my profile information',
                                style: theme.textTheme.bodyLarge,
                              ),
                              subtitle: Text(
                                'Your name, email and phone number will be used for this order',
                                style: theme.textTheme.bodySmall,
                              ),
                              controlAffinity: ListTileControlAffinity.leading,
                              contentPadding: EdgeInsets.zero,
                            ),

                            if (!_useProfileInfo) ...[
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _nameController,
                                decoration: const InputDecoration(
                                  labelText: 'Full Name',
                                  prefixIcon: Icon(Icons.person),
                                ),
                                validator: (value) {
                                  if (!_useProfileInfo &&
                                      (value == null || value.isEmpty)) {
                                    return 'Please enter your name';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _emailController,
                                keyboardType: TextInputType.emailAddress,
                                decoration: const InputDecoration(
                                  labelText: 'Student Email',
                                  prefixIcon: Icon(Icons.email),
                                ),
                                validator: (value) {
                                  if (!_useProfileInfo &&
                                      (value == null || value.isEmpty)) {
                                    return 'Please enter your email';
                                  }
                                  if (!_useProfileInfo &&
                                      !value!
                                          .endsWith('@students.eui.edu.eg')) {
                                    return 'Please use your student email';
                                  }
                                  return null;
                                },
                              ),
                              const SizedBox(height: 16),
                              TextFormField(
                                controller: _phoneController,
                                keyboardType: TextInputType.phone,
                                decoration: const InputDecoration(
                                  labelText: 'Phone Number',
                                  prefixIcon: Icon(Icons.phone),
                                ),
                                validator: (value) {
                                  if (!_useProfileInfo &&
                                      (value == null || value.isEmpty)) {
                                    return 'Please enter your phone number';
                                  }
                                  return null;
                                },
                              ),
                            ],
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Payment Method
                    Text(
                      'Payment Method',
                      style: theme.textTheme.displaySmall,
                    ),
                    const SizedBox(height: 16),
                    _buildPaymentMethodSection(theme),


                    const SizedBox(height: 24),

                    // Place Order Button
                    SizedBox(
                      width: double.infinity,
                      height: 56,
                      child: Consumer<SupabaseProvider>(
                        builder: (context, supabaseProvider, _) {
                          final isProcessing =
                              supabaseProvider.isProcessingCheckout;

                          return ElevatedButton(
                            onPressed: isProcessing ? null : _handleCheckout,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: theme.colorScheme.primary,
                              foregroundColor: theme.colorScheme.onPrimary,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(28),
                              ),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              elevation: 2,
                            ),
                            child: isProcessing
                                ? const SizedBox(
                                    height: 24,
                                    width: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                          Colors.white),
                                    ),
                                  )
                                : Row(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    children: [
                                      Text(
                                        'Place Order',
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          color: theme.colorScheme.onPrimary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(width: 8),
                                      Text(
                                        '• ${total.toStringAsFixed(2)} EGP',
                                        style: theme.textTheme.titleMedium
                                            ?.copyWith(
                                          color: theme.colorScheme.onPrimary,
                                        ),
                                      ),
                                    ],
                                  ),
                          );
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ),
      ),
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isBold = false}) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                color: isBold ? theme.primaryColor : null,
              ),
            ),
          ],
        );
      },
    );
  }

  PreferredSizeWidget _buildModernAppBar() {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: FadeTransition(
        opacity: _fadeAnimation,
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: ModernTheme.primaryGradient,
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.payment,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: ModernTheme.spaceM),
            Text(
              'Checkout',
              style: ModernTheme.headingMedium,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPaymentMethodSection(ThemeData theme) {
    return Consumer<PaymentMethodsProvider>(
      builder: (context, paymentProvider, child) {
        if (paymentProvider.isLoading) {
          return const Card(
            child: Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          );
        }

        if (paymentProvider.error != null) {
          return Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  Icon(Icons.error_outline, color: Colors.red[300], size: 48),
                  const SizedBox(height: 8),
                  Text(
                    'Error loading payment methods',
                    style: theme.textTheme.titleMedium,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    paymentProvider.error!,
                    style: theme.textTheme.bodySmall,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => _loadPaymentMethods(),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          );
        }

        return Card(
          child: Column(
            children: [
              // Apple Pay Option
              RadioListTile<String>(
                value: 'apple_pay',
                groupValue: _paymentMethod,
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                    _selectedPaymentMethod = null;
                  });
                },
                title: Row(
                  children: [
                    Text('Apple Pay', style: theme.textTheme.titleMedium),
                    const SizedBox(width: 8),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: Text(
                        'RECOMMENDED',
                        style: theme.textTheme.labelSmall?.copyWith(
                          color: theme.colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                subtitle: Text('Fast, secure checkout with Apple Pay', style: theme.textTheme.bodySmall),
                secondary: Icon(Icons.apple, color: theme.colorScheme.primary),
              ),

              // Saved Cards
              if (paymentProvider.hasPaymentMethods) ...[
                const Divider(height: 0),
                ...paymentProvider.paymentMethods.map((method) => RadioListTile<String>(
                  value: 'saved_card_${method.id}',
                  groupValue: _paymentMethod,
                  onChanged: (value) {
                    setState(() {
                      _paymentMethod = value!;
                      _selectedPaymentMethod = method;
                    });
                  },
                  title: Row(
                    children: [
                      Container(
                        width: 40,
                        height: 24,
                        decoration: BoxDecoration(
                          color: _getCardColor(method.provider),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Center(
                          child: Text(
                            _getCardBrand(method.provider),
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text('•••• ${method.lastFour}', style: theme.textTheme.titleMedium),
                      if (method.isDefault) ...[
                        const SizedBox(width: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                          decoration: BoxDecoration(
                            color: Colors.orange[100],
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Default',
                            style: TextStyle(
                              color: Colors.orange[800],
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                  subtitle: method.cardholderName.isNotEmpty
                    ? Text(method.cardholderName, style: theme.textTheme.bodySmall)
                    : null,
                )).toList(),
              ],

              // Add New Card Option
              const Divider(height: 0),
              RadioListTile<String>(
                value: 'new_card',
                groupValue: _paymentMethod,
                onChanged: (value) {
                  setState(() {
                    _paymentMethod = value!;
                    _selectedPaymentMethod = null;
                  });
                },
                title: Text('Add new card', style: theme.textTheme.titleMedium),
                subtitle: Text('Enter card details manually', style: theme.textTheme.bodySmall),
                secondary: Icon(Icons.add_card, color: theme.colorScheme.primary),
              ),

              // New Card Form
              if (_paymentMethod == 'new_card')
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      TextFormField(
                        controller: _cardNumberController,
                        keyboardType: TextInputType.number,
                        decoration: const InputDecoration(
                          labelText: 'Card Number',
                          prefixIcon: Icon(Icons.credit_card),
                          hintText: '1234 5678 9012 3456',
                        ),
                        validator: (value) {
                          if (_paymentMethod == 'new_card' && (value == null || value.isEmpty)) {
                            return 'Please enter your card number';
                          }
                          return null;
                        },
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: TextFormField(
                              controller: _expiryController,
                              keyboardType: TextInputType.datetime,
                              decoration: const InputDecoration(
                                labelText: 'MM/YY',
                                hintText: '01/28',
                              ),
                              validator: (value) {
                                if (_paymentMethod == 'new_card' && (value == null || value.isEmpty)) {
                                  return 'Required';
                                }
                                return null;
                              },
                            ),
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: TextFormField(
                              controller: _cvvController,
                              keyboardType: TextInputType.number,
                              decoration: const InputDecoration(
                                labelText: 'CVV',
                                hintText: '123',
                              ),
                              validator: (value) {
                                if (_paymentMethod == 'new_card' && (value == null || value.isEmpty)) {
                                  return 'Required';
                                }
                                return null;
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

              // Apple Pay Preview
              if (_paymentMethod == 'apple_pay')
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    children: [
                      Container(
                        width: double.infinity,
                        height: 50,
                        decoration: BoxDecoration(
                          color: Colors.black,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Icon(Icons.apple, color: Colors.white),
                            const SizedBox(width: 8),
                            Text(
                              'Pay',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'You\'ll be prompted to complete the payment with Apple Pay when you place your order.',
                        style: theme.textTheme.bodySmall,
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Color _getCardColor(String provider) {
    switch (provider.toLowerCase()) {
      case 'visa':
        return const Color(0xFF1A1F71);
      case 'mastercard':
        return const Color(0xFFEB001B);
      case 'amex':
      case 'american express':
        return const Color(0xFF006FCF);
      case 'discover':
        return const Color(0xFFFF6000);
      default:
        return Colors.grey[600]!;
    }
  }

  String _getCardBrand(String provider) {
    switch (provider.toLowerCase()) {
      case 'visa':
        return 'VISA';
      case 'mastercard':
        return 'MC';
      case 'amex':
      case 'american express':
        return 'AMEX';
      case 'discover':
        return 'DISC';
      default:
        return 'CARD';
    }
  }
}
