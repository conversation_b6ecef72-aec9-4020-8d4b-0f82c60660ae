import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/favorites_provider.dart';
import 'package:unieatsappv0/widgets/cafeteria_card.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/providers/supabase_provider.dart';
import 'package:unieatsappv0/services/favorites_service.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/utils/initialization_helper.dart';

class FavoritesScreen extends StatefulWidget {
  const FavoritesScreen({super.key});

  @override
  State<FavoritesScreen> createState() => _FavoritesScreenState();
}

class _FavoritesScreenState extends State<FavoritesScreen> {
  final FavoritesService _favoritesService = FavoritesService();
  List<Map<String, dynamic>> _favoriteItems = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeAndLoadData();
  }

  Future<void> _initializeAndLoadData() async {
    // Use initialization helper to ensure all required providers are ready
    await InitializationHelper.initializeForScreen(
      context,
      needsSupabase: true,
      needsFavorites: true,
    );

    // Load favorites after ensuring data is available
    await _loadFavorites();
  }

  Future<void> _loadFavorites() async {
    setState(() => _isLoading = true);
    try {
      final favorites = await _favoritesService.getUserFavoritesWithDetails();
      setState(() {
        _favoriteItems = favorites;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      debugPrint('Error loading favorites: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return DefaultTabController(
      length: 2,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Favorites'),
          actions: [
            IconButton(
              icon: const Icon(Icons.refresh),
              onPressed: _loadFavorites,
            ),
          ],
          bottom: const TabBar(
            tabs: [
              Tab(text: 'Cafeterias'),
              Tab(text: 'Menu Items'),
            ],
          ),
        ),
        body: TabBarView(
          children: [
            // Cafeterias tab
            _buildFavoriteCafeterias(),
            // Menu Items tab
            _buildFavoriteMenuItems(),
          ],
        ),
      ),
    );
  }

  Widget _buildFavoriteCafeterias() {
    return Consumer2<FavoritesProvider, SupabaseProvider>(
      builder: (context, favorites, supabaseProvider, _) {
        // Get cafeterias from Supabase
        final cafeterias = supabaseProvider.cafeterias;

        // Filter to favorites
        final favCafeterias = cafeterias
            .where((caf) => favorites.isCafeteriaFavorite(caf.id))
            .toList();

        if (favCafeterias.isEmpty) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.favorite_border, size: 64, color: Colors.grey[400]),
                const SizedBox(height: 16),
                const Text('No favorite cafeterias yet.',
                    style: TextStyle(fontSize: 18)),
                const SizedBox(height: 8),
                const Text('Add cafeterias to your favorites to see them here.',
                    style: TextStyle(fontSize: 14, color: Colors.grey)),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: favCafeterias.length,
          itemBuilder: (context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 16),
              child: CafeteriaCard(cafeteria: favCafeterias[index]),
            );
          },
        );
      },
    );
  }

  Widget _buildFavoriteMenuItems() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_favoriteItems.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.favorite_border, size: 64, color: Colors.grey[400]),
            const SizedBox(height: 16),
            const Text('No favorite menu items yet.',
                style: TextStyle(fontSize: 18)),
            const SizedBox(height: 8),
            const Text('Add menu items to your favorites to see them here.',
                style: TextStyle(fontSize: 14, color: Colors.grey)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/dashboard');
              },
              child: const Text('Browse Menu Items'),
            ),
          ],
        ),
      );
    }

    return Consumer<CartProvider>(
      builder: (context, cartProvider, _) {
        return ListView.builder(
          padding: const EdgeInsets.all(16),
          itemCount: _favoriteItems.length,
          itemBuilder: (context, index) {
            final favoriteItem = _favoriteItems[index];
            final menuItem = favoriteItem['menu_items'];
            final cafeteria = menuItem['cafeterias'];

            return Container(
              margin: const EdgeInsets.only(bottom: ModernTheme.spaceM),
              child: _buildModernFavoriteCard(menuItem, cafeteria, cartProvider),
            );
          },
        );
      },
    );
  }

  Widget _buildModernFavoriteCard(Map<String, dynamic> menuItem, Map<String, dynamic> cafeteria, CartProvider cartProvider) {
    final quantity = cartProvider.getQuantity(menuItem['id']);
    final isAvailable = menuItem['is_available'] == true;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        boxShadow: ModernTheme.mediumShadow,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
          onTap: () {
            // Navigate to item details if needed
          },
          child: Padding(
            padding: const EdgeInsets.all(ModernTheme.spaceM),
            child: Row(
              children: [
                // Item Image
                Container(
                  width: 80,
                  height: 80,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                    boxShadow: ModernTheme.lightShadow,
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                    child: RobustImage(
                      imageUrl: menuItem['image_url'] ?? 'assets/images/food-placeholder.png',
                      width: 80,
                      height: 80,
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                const SizedBox(width: ModernTheme.spaceM),

                // Item Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item Name and Favorite Button
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              menuItem['name'] ?? 'Unknown Item',
                              style: ModernTheme.headingSmall,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          Consumer<FavoritesProvider>(
                            builder: (context, favProvider, _) {
                              return Container(
                                decoration: BoxDecoration(
                                  color: Colors.red.withOpacity(0.1),
                                  borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                                ),
                                child: IconButton(
                                  icon: Icon(
                                    favProvider.isMenuItemFavorite(menuItem['id'])
                                        ? Icons.favorite
                                        : Icons.favorite_border,
                                    color: Colors.red,
                                    size: 20,
                                  ),
                                  onPressed: () async {
                                    await favProvider.toggleMenuItemFavorite(menuItem['id']);
                                    _loadFavorites();
                                  },
                                  style: IconButton.styleFrom(
                                    padding: const EdgeInsets.all(8),
                                    minimumSize: const Size(36, 36),
                                  ),
                                ),
                              );
                            },
                          ),
                        ],
                      ),

                      // Cafeteria Name
                      Text(
                        cafeteria['name'] ?? 'Unknown Cafeteria',
                        style: ModernTheme.bodySmall.copyWith(
                          color: ModernTheme.textSecondary,
                        ),
                      ),

                      // Description (if available)
                      if (menuItem['description'] != null && menuItem['description'].toString().isNotEmpty) ...[
                        const SizedBox(height: 4),
                        Text(
                          menuItem['description'],
                          style: ModernTheme.bodySmall.copyWith(
                            color: ModernTheme.textSecondary,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],

                      const SizedBox(height: ModernTheme.spaceS),

                      // Price and Add Button
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          // Price
                          Text(
                            '${(menuItem['price'] ?? 0).toStringAsFixed(2)} EGP',
                            style: ModernTheme.headingSmall.copyWith(
                              color: ModernTheme.accentColor,
                              fontWeight: FontWeight.w700,
                            ),
                          ),

                          // Quantity Controls
                          _buildQuantityControls(menuItem, quantity, cartProvider, cafeteria, isAvailable),
                        ],
                      ),

                      // Unavailable indicator
                      if (!isAvailable) ...[
                        const SizedBox(height: ModernTheme.spaceS),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.red.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
                          ),
                          child: Text(
                            'Currently unavailable',
                            style: ModernTheme.bodySmall.copyWith(
                              color: Colors.red,
                              fontStyle: FontStyle.italic,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildQuantityControls(Map<String, dynamic> menuItem, int quantity, CartProvider cartProvider, Map<String, dynamic> cafeteria, bool isAvailable) {
    if (!isAvailable) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        decoration: BoxDecoration(
          color: Colors.grey.shade200,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
        ),
        child: Text(
          'Unavailable',
          style: ModernTheme.bodySmall.copyWith(
            color: Colors.grey.shade600,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    if (quantity == 0) {
      // Show add button
      return Container(
        decoration: BoxDecoration(
          gradient: ModernTheme.accentGradient,
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
          boxShadow: [
            BoxShadow(
              color: ModernTheme.accentColor.withOpacity(0.3),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
            onTap: () async {
              final cartItem = CartItem(
                id: menuItem['id'],
                name: menuItem['name'] ?? 'Unknown Item',
                price: (menuItem['price'] ?? 0).toDouble(),
                image: menuItem['image_url'] ?? 'assets/images/food-placeholder.png',
                quantity: 1,
                cafeteriaName: cafeteria['name'] ?? 'Unknown Cafeteria',
                buildingName: cafeteria['location'] ?? 'Unknown Location',
                customizations: {},
              );

              final success = await cartProvider.addItem(cartItem, context: context);
              if (success) {
                SnackBarUtils.showSuccessSnackBar(
                  context: context,
                  message: '${menuItem['name']} added to cart!',
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              child: const Icon(
                Icons.add,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ),
      );
    } else {
      // Show quantity controls
      return Container(
        decoration: BoxDecoration(
          color: ModernTheme.accentColor.withOpacity(0.1),
          borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
          border: Border.all(
            color: ModernTheme.accentColor.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Decrease button
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                onTap: () {
                  if (quantity > 1) {
                    cartProvider.updateQuantity(menuItem['id'], quantity - 1);
                  } else {
                    cartProvider.removeItem(menuItem['id']);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    quantity > 1 ? Icons.remove : Icons.delete_outline,
                    color: ModernTheme.accentColor,
                    size: 18,
                  ),
                ),
              ),
            ),
            // Quantity display
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              child: Text(
                '$quantity',
                style: ModernTheme.bodyMedium.copyWith(
                  fontWeight: FontWeight.w600,
                  color: ModernTheme.accentColor,
                ),
              ),
            ),
            // Increase button
            Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                onTap: () {
                  cartProvider.updateQuantity(menuItem['id'], quantity + 1);
                },
                child: Container(
                  padding: const EdgeInsets.all(8),
                  child: Icon(
                    Icons.add,
                    color: ModernTheme.accentColor,
                    size: 18,
                  ),
                ),
              ),
            ),
          ],
        ),
      );
    }
  }
}
