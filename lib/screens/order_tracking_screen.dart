import 'dart:async';
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';

import 'package:unieatsappv0/providers/supabase_provider.dart';

import 'package:unieatsappv0/services/rating_service.dart';
import 'package:unieatsappv0/services/supabase_cafeteria_service.dart';
import 'package:unieatsappv0/services/supabase_order_service.dart';
import 'package:unieatsappv0/services/order_sync_service.dart';
import 'package:unieatsappv0/services/order_realtime_service.dart';
import 'package:unieatsappv0/services/order_cancellation_service.dart';
import 'package:unieatsappv0/utils/order_status_utils.dart';
import 'package:intl/intl.dart';
import 'package:unieatsappv0/theme/app_theme.dart';
import 'package:unieatsappv0/widgets/order_rating_dialog.dart';
import 'package:unieatsappv0/widgets/multi_item_rating_dialog.dart';
import 'package:unieatsappv0/widgets/order_cancellation_dialog.dart';
import 'package:unieatsappv0/screens/support_chat_screen.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/providers/simple_auth_provider.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';

class OrderTrackingScreen extends StatefulWidget {
  final Order order;

  const OrderTrackingScreen({
    super.key,
    required this.order,
  });

  @override
  State<OrderTrackingScreen> createState() => _OrderTrackingScreenState();
}

class _OrderTrackingScreenState extends State<OrderTrackingScreen>
    with TickerProviderStateMixin {
  Timer? _refreshTimer;
  final SupabaseCafeteriaService _cafeteriaService = SupabaseCafeteriaService();
  final OrderCancellationService _cancellationService = OrderCancellationService();
  Order? _currentOrder; // Track the current order state
  bool _hasShownRatingDialog = false; // Track if rating dialog has been shown
  StreamSubscription? _orderSubscription;
  final OrderRealtimeService _realtimeService = OrderRealtimeService();
  bool _isDisposed = false; // Track if widget is disposed
  bool _isRefreshing = false; // Prevent concurrent refreshes
  DateTime? _lastRefreshTime; // Track last refresh time

  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late AnimationController _pulseController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
    _currentOrder = widget.order; // Initialize with the passed order

    // Ensure the order is in the provider
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final existingOrder = orderProvider.getOrderByNumber(widget.order.orderNumber);
      if (existingOrder == null) {
        debugPrint('🔄 Adding order to provider: ${widget.order.orderNumber}');
        orderProvider.addOrder(widget.order);
      }
    });

    // Check if order is already completed when screen loads
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowRatingDialog(widget.order);
    });

    // Set up real-time subscription for order updates
    _setupRealtimeSubscription();

    // Set up periodic refresh as backup (reduced frequency since we have real-time)
    _refreshTimer = Timer.periodic(const Duration(minutes: 3), (timer) {
      // Check if widget is still mounted and not disposed before refreshing
      if (mounted && !_isDisposed) {
        _refreshOrderStatus();
      } else {
        // Cancel timer if widget is disposed
        timer.cancel();
        debugPrint('🔄 Cancelled refresh timer due to widget disposal');
      }
    });
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat(reverse: true);

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  // Set up real-time subscription for order updates
  void _setupRealtimeSubscription() async {
    try {
      debugPrint('🔔 Setting up real-time subscription for order: ${widget.order.id}');

      await _realtimeService.init();

      _orderSubscription = _realtimeService.subscribeToOrder(widget.order.id).listen(
        (SupabaseOrder updatedOrder) {
          if (!mounted || _isDisposed) {
            debugPrint('⚠️ Widget unmounted or disposed, ignoring real-time update');
            return;
          }

          debugPrint('🔔 Real-time order update received: ${updatedOrder.status}');

          // Convert Supabase order to local Order model
          _handleRealtimeOrderUpdate(updatedOrder);
        },
        onError: (error) {
          debugPrint('❌ Real-time subscription error: $error');
        },
      );

      debugPrint('✅ Real-time subscription established');
    } catch (e) {
      debugPrint('❌ Error setting up real-time subscription: $e');
    }
  }

  // Handle real-time order updates
  void _handleRealtimeOrderUpdate(SupabaseOrder updatedOrder) async {
    try {
      if (!mounted || _isDisposed) return;

      debugPrint('🔄 Processing real-time order update: ${updatedOrder.status}');

      // Convert the Supabase order to local Order model
      final orderSyncService = OrderSyncService();
      final convertedOrder = await orderSyncService.convertSupabaseOrderToLocal({
        'id': updatedOrder.id,
        'status': updatedOrder.status,
        'student_id': updatedOrder.userId,
        'user_id': updatedOrder.userId,
        'cafeteria_id': updatedOrder.cafeteriaId,
        'total_amount': updatedOrder.totalAmount,
        'created_at': updatedOrder.createdAt.toIso8601String(),
        'updated_at': updatedOrder.updatedAt?.toIso8601String(),
        'order_number': updatedOrder.orderNumber,
        'pickup_time': updatedOrder.pickupTime,
        'payment_method': updatedOrder.paymentMethod,
      });

      if (convertedOrder != null && mounted && !_isDisposed) {
        debugPrint('✅ Real-time order converted successfully: ${convertedOrder.status}');

        // Safely update providers
        _safelyUpdateProviders(convertedOrder);

        // Check for rating dialog
        _checkAndShowRatingDialog(convertedOrder);
      }
    } catch (e) {
      debugPrint('❌ Error handling real-time order update: $e');
    }
  }

  // Safely update providers with additional checks
  void _safelyUpdateProviders(Order order) {
    try {
      if (!mounted || _isDisposed) {
        debugPrint('⚠️ Cannot update providers - widget unmounted or disposed');
        return;
      }

      debugPrint('🔄 Safely updating providers with order: ${order.status}');

      // Update providers with additional error handling
      final orderProvider = Provider.of<OrderProvider>(context, listen: false);
      final orderHistoryProvider = Provider.of<OrderHistoryProvider>(context, listen: false);

      orderProvider.updateOrder(order, context: context);
      orderHistoryProvider.updateOrder(order);

      debugPrint('✅ Providers updated successfully');
    } catch (e) {
      debugPrint('❌ Error updating providers: $e');
    }
  }

  @override
  void dispose() {
    debugPrint('🔄 Disposing order tracking screen for order: ${widget.order.orderNumber}');

    // Mark as disposed first to prevent any further state updates
    _isDisposed = true;

    // Clean up animations
    _fadeController.dispose();
    _slideController.dispose();
    _pulseController.dispose();

    // Clean up timer
    _refreshTimer?.cancel();
    _refreshTimer = null;

    // Clean up real-time subscription
    _orderSubscription?.cancel();
    _orderSubscription = null;
    _realtimeService.unsubscribeFromOrder(widget.order.id);

    super.dispose();
  }

  // Refresh order status from Supabase
  void _refreshOrderStatus() async {
    // Check if widget is still mounted and not disposed before proceeding
    if (!mounted || _isDisposed) {
      debugPrint('⚠️ Widget unmounted or disposed, skipping order status refresh');
      return;
    }

    // Prevent concurrent refreshes
    if (_isRefreshing) {
      debugPrint('⚠️ Refresh already in progress, skipping');
      return;
    }

    // Debounce: Don't refresh if we just refreshed recently
    final now = DateTime.now();
    if (_lastRefreshTime != null &&
        now.difference(_lastRefreshTime!).inSeconds < 10) {
      debugPrint('⚠️ Refresh too recent, skipping (debounced)');
      return;
    }

    _isRefreshing = true;
    _lastRefreshTime = now;

    try {
      debugPrint('🔄 Refreshing order status for order: ${widget.order.orderNumber} (ID: ${widget.order.id})');

      // Safely access providers with error handling
      OrderProvider? orderProvider;
      SupabaseProvider? supabaseProvider;
      OrderHistoryProvider? orderHistoryProvider;

      try {
        orderProvider = Provider.of<OrderProvider>(context, listen: false);
        supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
        orderHistoryProvider = Provider.of<OrderHistoryProvider>(context, listen: false);
      } catch (e) {
        debugPrint('❌ Error accessing providers: $e');
        return;
      }

      // First, try to get the order directly from Supabase by ID
      final supabaseOrderService = SupabaseOrderService();
      final directOrder = await supabaseOrderService.getOrderById(widget.order.id);
      debugPrint('🔍 Direct order from Supabase: ${directOrder?.status}');

      // Check if widget is still mounted and not disposed before continuing
      if (!mounted || _isDisposed) {
        debugPrint('⚠️ Widget unmounted or disposed during refresh, stopping');
        return;
      }

      // Force reload user orders to get the latest status from database
      debugPrint('🔄 Force reloading user orders from database...');
      if (supabaseProvider != null) {
        await supabaseProvider.loadUserOrders();
      }

      // Check if widget is still mounted and not disposed before continuing
      if (!mounted || _isDisposed) {
        debugPrint('⚠️ Widget unmounted or disposed during provider reload, stopping');
        return;
      }

      // Also refresh order history provider
      debugPrint('🔄 Refreshing order history provider...');
      if (orderHistoryProvider != null) {
        await orderHistoryProvider.loadOrders();
      }

      // Check if widget is still mounted and not disposed before accessing providers
      if (!mounted || _isDisposed) {
        debugPrint('⚠️ Widget unmounted or disposed during history reload, stopping');
        return;
      }

      // Get the updated order from both providers
      final updatedOrderFromProvider = orderProvider?.getOrderByNumber(widget.order.orderNumber);
      final updatedOrderFromHistory = orderHistoryProvider?.orders
          .where((o) => o.orderNumber == widget.order.orderNumber)
          .firstOrNull;

      debugPrint('🔍 Updated order from provider: ${updatedOrderFromProvider?.status}');
      debugPrint('🔍 Updated order from history: ${updatedOrderFromHistory?.status}');

      // Check if we have a direct order with different status
      if (directOrder != null) {
        final directOrderDisplayStatus = OrderStatusUtils.getDisplayStatus(directOrder.status);
        debugPrint('🔍 Direct order display status: $directOrderDisplayStatus');

        if (directOrderDisplayStatus != widget.order.status) {
          debugPrint('✅ Status mismatch detected! Database: $directOrderDisplayStatus, Current: ${widget.order.status}');

          // Convert the Supabase order to local Order model
          final orderSyncService = OrderSyncService();
          final convertedOrder = await orderSyncService.convertSupabaseOrderToLocal({
            'id': directOrder.id,
            'status': directOrder.status,
            'student_id': directOrder.userId,
            'user_id': directOrder.userId,
            'cafeteria_id': directOrder.cafeteriaId,
            'total_amount': directOrder.totalAmount,
            'created_at': directOrder.createdAt.toIso8601String(),
            'updated_at': directOrder.updatedAt?.toIso8601String(),
            'order_number': directOrder.orderNumber,
            'pickup_time': directOrder.pickupTime,
            'payment_method': directOrder.paymentMethod,
            'cancellation_reason': directOrder.cancellationReason,
            'cancelled_by': directOrder.cancelledBy,
            'cancelled_at': directOrder.cancelledAt?.toIso8601String(),
          });

          if (convertedOrder != null) {
            debugPrint('🔄 Updating providers with converted order...');
            orderProvider.updateOrder(convertedOrder, context: context);
            orderHistoryProvider.updateOrder(convertedOrder);
            _checkAndShowRatingDialog(convertedOrder);
          }
        }
      }

      // Final mounted and disposed check before updating UI
      if (!mounted || _isDisposed) {
        debugPrint('⚠️ Widget unmounted or disposed before updating UI, stopping');
        return;
      }

      if (updatedOrderFromProvider != null && updatedOrderFromProvider.status != widget.order.status) {
        debugPrint('✅ Order status updated: ${widget.order.status} -> ${updatedOrderFromProvider.status}');

        // Check if order became completed and we haven't shown rating dialog yet
        if (mounted && !_isDisposed) {
          _checkAndShowRatingDialog(updatedOrderFromProvider);
        }

        // The Consumer will automatically rebuild with the new status
      } else if (updatedOrderFromHistory != null && updatedOrderFromHistory.status != widget.order.status) {
        debugPrint('✅ Order status updated from history: ${widget.order.status} -> ${updatedOrderFromHistory.status}');

        // Safely update providers with the history order
        if (mounted && !_isDisposed) {
          _safelyUpdateProviders(updatedOrderFromHistory);
        }

        // Check if order became completed and we haven't shown rating dialog yet
        _checkAndShowRatingDialog(updatedOrderFromHistory);
      } else {
        debugPrint('ℹ️ No status change detected');
      }
    } catch (e) {
      debugPrint('❌ Error refreshing order status: $e');
      // Only show error to user if widget is still mounted and not disposed
      if (mounted && !_isDisposed) {
        // Optionally show a snackbar or handle the error
        debugPrint('🔄 Will retry order status refresh in next cycle');
      }
    } finally {
      // Always reset the refresh flag
      _isRefreshing = false;
    }
  }

  // Check if order became completed and show rating dialog if needed
  void _checkAndShowRatingDialog(Order updatedOrder) {
    final isCompleted = updatedOrder.status.toLowerCase() == 'completed' ||
                       updatedOrder.status == OrderStatusUtils.statusCompletedDisplay;

    // Show rating dialog if:
    // 1. Order is now completed
    // 2. We haven't shown the rating dialog yet
    // 3. Order doesn't already have a rating
    if (isCompleted && !_hasShownRatingDialog && (updatedOrder.rating == null || updatedOrder.rating == 0)) {
      debugPrint('🌟 Order completed automatically, showing rating dialog');
      _hasShownRatingDialog = true;

      // Show rating dialog after a short delay to ensure UI is ready
      Future.delayed(const Duration(milliseconds: 500), () {
        if (mounted) {
          _showRatingDialog();
        }
      });
    }
  }

  // Show the rating dialog (extracted from _confirmPickup for reuse)
  void _showRatingDialog() {
    // Get providers outside the dialog
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final orderHistoryProvider =
        Provider.of<OrderHistoryProvider>(context, listen: false);
    final navigator = Navigator.of(context);

    // Check if order has multiple different items
    final uniqueItems = widget.order.items.toSet().toList();

    if (uniqueItems.length > 1) {
      // Show multi-item rating dialog for orders with multiple different items
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => MultiItemRatingDialog(
          items: uniqueItems,
          onSubmit: (itemRatings, overallRating, overallComment) async {
            // Close the rating dialog
            Navigator.of(dialogContext).pop();

            // Update order status with overall rating and comment locally
            final updatedOrder = widget.order.copyWith(
              status: OrderStatusUtils.statusCompletedDisplay,
              rating: overallRating,
              comment: overallComment,
            );

            // Update the order in the providers
            if (mounted) {
              orderProvider.updateOrder(updatedOrder, context: context);
              orderHistoryProvider.updateOrder(updatedOrder);
            }

            // Save ratings to Supabase with individual item ratings
            await _submitMultiItemRatings(itemRatings, overallRating, overallComment);

            // Show thank you dialog
            if (mounted) {
              _showThankYouDialog(navigator);
            }
          },
        ),
      );
    } else {
      // Show simple rating dialog for single item orders
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => OrderRatingDialog(
          onSubmit: (rating, comment) async {
            // Close the rating dialog
            Navigator.of(dialogContext).pop();

            // Update order status with rating and comment locally
            final updatedOrder = widget.order.copyWith(
              status: OrderStatusUtils.statusCompletedDisplay,
              rating: rating,
              comment: comment,
            );

            // Update the order in the providers
            if (mounted) {
              orderProvider.updateOrder(updatedOrder, context: context);
              orderHistoryProvider.updateOrder(updatedOrder);
            }

            // Save ratings to Supabase (existing logic)
            await _submitRatings(rating, comment);

            // Force refresh the order from database to get updated rating
            if (mounted) {
              try {
                final orderProvider = Provider.of<OrderProvider>(context, listen: false);
                final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);

                // Refresh order from database
                await supabaseProvider.loadUserOrders();

                // Update local order state
                final updatedOrder = orderProvider.getOrderByNumber(widget.order.orderNumber);
                if (updatedOrder != null) {
                  _currentOrder = updatedOrder;
                  debugPrint('✅ Order rating updated locally: ${updatedOrder.rating}');
                  setState(() {}); // Force UI rebuild
                }
              } catch (e) {
                debugPrint('❌ Error refreshing order after rating: $e');
              }
            }

            // Show thank you dialog
            if (mounted) {
              _showThankYouDialog(navigator);
            }
          },
        ),
      );
    }
  }

  // Submit multi-item ratings to Supabase
  Future<void> _submitMultiItemRatings(List<ItemRating> itemRatings, int overallRating, String? overallComment) async {
    // Save order rating to Supabase if overall rating is provided
    if (mounted && overallRating > 0) {
      try {
        await RatingService.submitOrderRating(
          orderId: widget.order.id,
          rating: overallRating,
          comment: overallComment,
          context: context,
        );
        debugPrint('Order rating submitted successfully');
      } catch (e) {
        debugPrint('Error submitting order rating: $e');
      }
    }

    // Save cafeteria rating using overall rating if provided, otherwise use average of item ratings
    int cafeteriaRating = overallRating;
    String? cafeteriaComment = overallComment;

    if (cafeteriaRating == 0 && itemRatings.isNotEmpty) {
      // Calculate average rating from individual items
      final validRatings = itemRatings.where((r) => r.rating > 0).toList();
      if (validRatings.isNotEmpty) {
        cafeteriaRating = (validRatings.map((r) => r.rating).reduce((a, b) => a + b) / validRatings.length).round();
        cafeteriaComment = 'Average rating from individual items';
      }
    }

    if (cafeteriaRating > 0 && widget.order.items.isNotEmpty) {
      // Get the cafeteria ID by looking up the cafeteria name
      final cafeteriaName = widget.order.items.first.cafeteriaName;
      final cafeteriaId = await _getCafeteriaIdByName(cafeteriaName);

      // Add cafeteria rating using RatingService directly
      if (mounted && cafeteriaId != null) {
        try {
          await RatingService.submitCafeteriaRating(
            cafeteriaId: cafeteriaId,
            rating: cafeteriaRating.toDouble(),
            comment: cafeteriaComment,
            orderId: widget.order.id,
            context: context,
          );
          debugPrint('Cafeteria rating submitted successfully');
        } catch (e) {
          debugPrint('Error submitting cafeteria rating: $e');
        }
      }
    }

    // Rate individual menu items with their specific ratings and comments
    for (final itemRating in itemRatings) {
      if (itemRating.rating > 0) {
        if (mounted) {
          try {
            await RatingService.submitMenuItemRating(
              menuItemId: itemRating.itemId,
              rating: itemRating.rating.toDouble(),
              comment: itemRating.comment,
              orderId: widget.order.id,
              context: context,
            );
            debugPrint('Menu item rating submitted for ${itemRating.itemName}: ${itemRating.rating} stars');
          } catch (e) {
            debugPrint('Error submitting menu item rating for ${itemRating.itemName}: $e');
          }
        }
      }
    }

    // Refresh menu items to update ratings display
    if (mounted) {
      final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
      await supabaseProvider.refreshMenuItemsAfterRating();
    }
  }

  // Submit ratings to Supabase (extracted from _confirmPickup for reuse)
  Future<void> _submitRatings(int rating, String? comment) async {
    // Save order rating to Supabase using the correct order ID
    if (mounted && rating > 0) {
      try {
        debugPrint('🔍 [RATING] Submitting rating for order ID: "${widget.order.id}"');
        debugPrint('🔍 [RATING] Order ID type: ${widget.order.id.runtimeType}');
        debugPrint('🔍 [RATING] Order ID length: ${widget.order.id.length}');
        debugPrint('🔍 [RATING] Order number: "${widget.order.orderNumber}"');

        await RatingService.submitOrderRating(
          orderId: widget.order.id, // Use actual order ID, not order number
          rating: rating,
          comment: comment,
          context: context,
        );
        debugPrint('✅ [RATING] Order rating submitted successfully');
      } catch (e) {
        debugPrint('❌ [RATING] Error submitting order rating: $e');
      }
    }

    // Save rating to cafeteria if rating is provided
    if (rating > 0 && widget.order.items.isNotEmpty) {
      // Get the cafeteria ID by looking up the cafeteria name
      final cafeteriaName = widget.order.items.first.cafeteriaName;
      final cafeteriaId = await _getCafeteriaIdByName(cafeteriaName);

      // Add cafeteria rating using RatingService directly
      if (mounted && cafeteriaId != null) {
        try {
          await RatingService.submitCafeteriaRating(
            cafeteriaId: cafeteriaId,
            rating: rating.toDouble(),
            comment: comment,
            orderId: widget.order.id, // Use actual order ID
            context: context,
          );
          debugPrint('Cafeteria rating submitted successfully');
        } catch (e) {
          debugPrint('Error submitting cafeteria rating: $e');
        }
      }

      // Also rate individual menu items
      for (final item in widget.order.items) {
        final itemId = item.id;
        if (itemId.isNotEmpty) {
          if (mounted) {
            try {
              await RatingService.submitMenuItemRating(
                menuItemId: itemId,
                rating: rating.toDouble(),
                comment: comment,
                orderId: widget.order.id, // Use actual order ID
                context: context,
              );
              debugPrint('Menu item rating submitted for item: $itemId');
            } catch (e) {
              debugPrint('Error submitting menu item rating for $itemId: $e');
            }
          }
        }
      }
    }
  }

  // Show thank you dialog (extracted from _confirmPickup for reuse)
  void _showThankYouDialog(NavigatorState navigator) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text('Thank You!'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              Icons.check_circle,
              color: Colors.green,
              size: 64,
            ),
            SizedBox(height: 16),
            Text(
              'Thank you for your feedback! Your order has been marked as completed.',
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();

              // Navigate to dashboard immediately
              navigator.pushReplacementNamed('/dashboard');
            },
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  // Helper method to get cafeteria ID by name
  Future<String?> _getCafeteriaIdByName(String cafeteriaName) async {
    try {
      final cafeterias = await _cafeteriaService.searchCafeterias(cafeteriaName);
      // Find exact match
      for (final cafeteria in cafeterias) {
        if (cafeteria.name.toLowerCase() == cafeteriaName.toLowerCase()) {
          return cafeteria.id;
        }
      }
      // If no exact match, return the first result if any
      if (cafeterias.isNotEmpty) {
        return cafeterias.first.id;
      }
      return null;
    } catch (e) {
      debugPrint('Error getting cafeteria ID by name: $e');
      return null;
    }
  }

  void _confirmPickup() {
    // Get providers outside the dialog
    final orderProvider = Provider.of<OrderProvider>(context, listen: false);
    final orderHistoryProvider =
        Provider.of<OrderHistoryProvider>(context, listen: false);
    final navigator = Navigator.of(context);

    // Mark that we've shown the rating dialog to prevent duplicate dialogs
    _hasShownRatingDialog = true;

    // Check if order has multiple different items
    final uniqueItems = widget.order.items.toSet().toList();

    if (uniqueItems.length > 1) {
      // Show multi-item rating dialog for orders with multiple different items
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => MultiItemRatingDialog(
          items: uniqueItems,
          onSubmit: (itemRatings, overallRating, overallComment) async {
            // Close the rating dialog
            Navigator.of(dialogContext).pop();

            // Update order status to completed in Supabase first
            final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
            try {
              // Use the actual order ID, not order number
              debugPrint('Updating order status for order ID: ${widget.order.id}');
              await supabaseProvider.updateOrderStatus(widget.order.id, 'completed');
              debugPrint('Order status updated to completed in Supabase');
            } catch (e) {
              debugPrint('Error updating order status in Supabase: $e');
              // Show error message to user
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error updating order status: $e')),
                );
              }
              return; // Don't proceed if status update failed
            }

            // Update order status with overall rating and comment locally
            final updatedOrder = widget.order.copyWith(
              status: OrderStatusUtils.statusCompletedDisplay,
              rating: overallRating,
              comment: overallComment,
            );

            // Update the order in the providers
            if (mounted) {
              orderProvider.updateOrder(updatedOrder, context: context);
              orderHistoryProvider.updateOrder(updatedOrder);
            }

            // Save ratings to Supabase with individual item ratings
            await _submitMultiItemRatings(itemRatings, overallRating, overallComment);

            // Show thank you dialog
            if (mounted) {
              _showThankYouDialog(navigator);
            }
          },
        ),
      );
    } else {
      // Show simple rating dialog for single item orders
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (dialogContext) => OrderRatingDialog(
          onSubmit: (rating, comment) async {
            // Close the rating dialog
            Navigator.of(dialogContext).pop();

            // Update order status to completed in Supabase first
            final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);
            try {
              // Use the actual order ID, not order number
              debugPrint('Updating order status for order ID: ${widget.order.id}');
              await supabaseProvider.updateOrderStatus(widget.order.id, 'completed');
              debugPrint('Order status updated to completed in Supabase');
            } catch (e) {
              debugPrint('Error updating order status in Supabase: $e');
              // Show error message to user
              if (mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Error updating order status: $e')),
                );
              }
              return; // Don't proceed if status update failed
            }

            // Update order status with rating and comment locally
            final updatedOrder = widget.order.copyWith(
              status: OrderStatusUtils.statusCompletedDisplay,
              rating: rating,
              comment: comment,
            );

            // Update the order in the providers
            if (mounted) {
              orderProvider.updateOrder(updatedOrder, context: context);
              orderHistoryProvider.updateOrder(updatedOrder);
            }

            // Save ratings to Supabase using extracted method
            await _submitRatings(rating, comment);

            // Force refresh the order from database to get updated rating
            if (mounted) {
              try {
                final orderProvider = Provider.of<OrderProvider>(context, listen: false);
                final supabaseProvider = Provider.of<SupabaseProvider>(context, listen: false);

                // Refresh order from database
                await supabaseProvider.loadUserOrders();

                // Update local order state
                final updatedOrder = orderProvider.getOrderByNumber(widget.order.orderNumber);
                if (updatedOrder != null) {
                  _currentOrder = updatedOrder;
                  debugPrint('✅ Order rating updated locally: ${updatedOrder.rating}');
                  setState(() {}); // Force UI rebuild
                }
              } catch (e) {
                debugPrint('❌ Error refreshing order after rating: $e');
              }
            }

            // Show thank you dialog
            if (mounted) {
              _showThankYouDialog(navigator);
            }
          },
        ),
      );
    }
  }

  Future<void> _showCancelOrderDialog() async {
    final authProvider = Provider.of<SimpleAuthProvider>(context, listen: false);
    final userId = authProvider.currentUser?.id;

    if (userId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('User not authenticated'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
      return;
    }

    // Check if user can cancel the order
    final canCancel = await _cancellationService.canUserCancelOrder(widget.order.id, userId);

    if (!canCancel) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.info_outline, color: Colors.white),
              SizedBox(width: 8),
              Expanded(
                child: Text(
                  'This order cannot be cancelled as it\'s already being prepared',
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
          backgroundColor: AppTheme.warningColor,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
      return;
    }

    // Show cancellation dialog
    showDialog(
      context: context,
      builder: (context) => OrderCancellationDialog(
        orderId: widget.order.id,
        orderNumber: widget.order.orderNumber,
        onCancelled: () {
          // Navigate back to order history or dashboard
          Navigator.of(context).pop(); // Close tracking screen
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final now = DateTime.now();
    final orderDate = DateFormat('MMMM d, yyyy').format(now);
    final orderTime = DateFormat('h:mm a').format(now);

    // Listen to order updates from OrderProvider
    return Consumer<OrderProvider>(
      builder: (context, orderProvider, child) {
        // Get the most up-to-date order from the provider
        final providerOrder = orderProvider.getOrderByNumber(widget.order.orderNumber);
        final currentOrder = providerOrder ?? widget.order;

        // Update our local state if we got an updated order from the provider
        if (providerOrder != null && providerOrder != _currentOrder) {
          _currentOrder = providerOrder;
          debugPrint('🔄 Updated local order state: ${providerOrder.orderNumber} - ${providerOrder.status}');
        }

        // Debug logging for order lookup and status
        debugPrint('🔍 Order tracking - Looking for order number: "${widget.order.orderNumber}"');
        debugPrint('📋 Order tracking - Provider has ${orderProvider.orders.length} orders');
        for (final order in orderProvider.orders) {
          debugPrint('   - Order: ${order.orderNumber} (Status: ${order.status})');
        }
        debugPrint('🎯 Order tracking - Found order: ${currentOrder.orderNumber} (Status: "${currentOrder.status}")');
        debugPrint('🔄 Order tracking - Using original order: ${currentOrder == widget.order ? "YES" : "NO"}');
        debugPrint('📊 Order tracking - Current order status: "${currentOrder.status}"');
        debugPrint('📊 Order tracking - Status lowercase: "${currentOrder.status.toLowerCase()}"');
        debugPrint('📊 Order tracking - StatusReadyForPickup: "${OrderStatusUtils.statusReadyForPickup}"');
        debugPrint('🔘 Order tracking - Button should show: ${currentOrder.status.toLowerCase() == 'ready' || currentOrder.status == OrderStatusUtils.statusReadyForPickup}');

        // Calculate progress based on current order status
        double progressValue = OrderStatusUtils.getProgressValue(currentOrder.status);

        return Scaffold(
      body: SafeArea(
        child: Stack(
          children: [
            // Main content - scrollable
            Padding(
              padding: const EdgeInsets.only(
                  bottom: 200), // Space for bottom bar with buttons
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Back button and refresh button
                    Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 16, vertical: 8),
                      child: Row(
                        children: [
                          IconButton(
                            icon: const Icon(
                              Icons.arrow_back,
                              color: AppTheme.textColor,
                            ),
                            onPressed: () => Navigator.of(context).pop(),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Back to Home',
                              style: theme.textTheme.titleMedium?.copyWith(
                                color: theme.primaryColor,
                              ),
                            ),
                          ),
                          IconButton(
                            icon: const Icon(
                              Icons.refresh,
                              color: AppTheme.textColor,
                            ),
                            onPressed: () {
                              debugPrint('Manual refresh triggered');
                              _refreshOrderStatus();
                            },
                            tooltip: 'Refresh order status',
                          ),
                        ],
                      ),
                    ),

                    // Order tracking header
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Order Tracking',
                            style: theme.textTheme.headlineMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Track your current order',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Order number and date
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Order #${currentOrder.orderNumber}',
                            style: theme.textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            '$orderDate, $orderTime',
                            style: theme.textTheme.bodySmall?.copyWith(
                              color: AppTheme.textSecondaryColor,
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Current status display
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: OrderStatusUtils.getStatusColor(currentOrder.status, context).withAlpha(30),
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: OrderStatusUtils.getStatusColor(currentOrder.status, context),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          'Status: ${OrderStatusUtils.getDisplayStatus(currentOrder.status)}',
                          style: theme.textTheme.bodySmall?.copyWith(
                            color: OrderStatusUtils.getStatusColor(currentOrder.status, context),
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Progress bar
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: LinearProgressIndicator(
                          value: progressValue,
                          backgroundColor:
                              theme.colorScheme.surfaceContainerHighest,
                          color: theme.primaryColor,
                          minHeight: 8,
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Status icons
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 32),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          _buildStatusIcon(
                            context,
                            OrderStatusUtils.getStatusIcon('preparing'),
                            'Preparing',
                            isActive: true, // Always active as first step
                            isCompleted: true, // Always completed as first step
                          ),
                          _buildStatusIcon(
                            context,
                            OrderStatusUtils.getStatusIcon('ready'),
                            'Ready',
                            isActive: currentOrder.status.toLowerCase() == 'ready' ||
                                      currentOrder.status == OrderStatusUtils.statusReadyForPickup ||
                                      currentOrder.status.toLowerCase() == 'completed' ||
                                      currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                            isCompleted: currentOrder.status.toLowerCase() == 'ready' ||
                                         currentOrder.status == OrderStatusUtils.statusReadyForPickup ||
                                         currentOrder.status.toLowerCase() == 'completed' ||
                                         currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                          ),
                          _buildStatusIcon(
                            context,
                            OrderStatusUtils.getStatusIcon('completed'),
                            'Completed',
                            isActive: currentOrder.status.toLowerCase() == 'completed' ||
                                      currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                            isCompleted: currentOrder.status.toLowerCase() == 'completed' ||
                                         currentOrder.status == OrderStatusUtils.statusCompletedDisplay,
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Pickup time
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest
                              .withAlpha(30),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.access_time,
                              color: theme.primaryColor,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Pick-up Time',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                                Text(
                                  currentOrder.pickupTime,
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Pickup location
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: theme.colorScheme.surfaceContainerHighest
                              .withAlpha(30),
                          borderRadius: BorderRadius.circular(16),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.location_on_outlined,
                              color: theme.primaryColor,
                            ),
                            const SizedBox(width: 16),
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Pick-up Location',
                                  style: theme.textTheme.bodySmall?.copyWith(
                                    color: AppTheme.textSecondaryColor,
                                  ),
                                ),
                                Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      currentOrder.items.isNotEmpty
                                          ? currentOrder.items.first.cafeteriaName
                                          : 'Cafeteria',
                                      style:
                                          theme.textTheme.titleMedium?.copyWith(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    if (currentOrder.items.isNotEmpty &&
                                        currentOrder.items.first.buildingName
                                            .isNotEmpty)
                                      Text(
                                        currentOrder.items.first.buildingName,
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Order details
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Text(
                        'Order Details',
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),

                    const SizedBox(height: 16),

                    // Cancellation details (only show for cancelled orders)
                    if (currentOrder.status.toLowerCase() == 'cancelled') ...[
                      Container(
                        margin: const EdgeInsets.symmetric(horizontal: 16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: AppTheme.errorColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: AppTheme.errorColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.cancel_outlined,
                                  color: AppTheme.errorColor,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Order Cancelled',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.errorColor,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            if (currentOrder.cancellationReason != null &&
                                currentOrder.cancellationReason!.isNotEmpty) ...[
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 80,
                                    child: Text(
                                      'Reason:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppTheme.errorColor.withOpacity(0.8),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      currentOrder.cancellationReason!,
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppTheme.errorColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                              const SizedBox(height: 8),
                            ],
                            if (currentOrder.cancelledBy != null) ...[
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(
                                    width: 80,
                                    child: Text(
                                      'Cancelled by:',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w500,
                                        color: AppTheme.errorColor.withOpacity(0.8),
                                      ),
                                    ),
                                  ),
                                  Expanded(
                                    child: Text(
                                      currentOrder.cancelledBy == 'user' ? 'You' : 'Cafeteria',
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.w600,
                                        color: AppTheme.errorColor,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ],
                          ],
                        ),
                      ),
                      const SizedBox(height: 24),
                    ],

                    // Order items
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      itemCount: currentOrder.items.length,
                      itemBuilder: (context, index) {
                        final item = currentOrder.items[index];
                        return Padding(
                          padding: const EdgeInsets.only(bottom: 16),
                          child: Row(
                            children: [
                              Text(
                                '${item.quantity}x',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(width: 16),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      item.name,
                                      style: theme.textTheme.bodyLarge,
                                    ),
                                    if (item.notes != null &&
                                        item.notes!.isNotEmpty)
                                      Text(
                                        'Note: ${item.notes}',
                                        style:
                                            theme.textTheme.bodySmall?.copyWith(
                                          fontStyle: FontStyle.italic,
                                          color: AppTheme.textSecondaryColor,
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                              Text(
                                'EGP${item.calculateItemTotal().toStringAsFixed(2)}',
                                style: theme.textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ),

            // Payment info - fixed at bottom
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withAlpha(10),
                      blurRadius: 8,
                      offset: const Offset(0, -2),
                    ),
                  ],
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Payment Method',
                          style: theme.textTheme.bodyMedium,
                        ),
                        Text(
                          widget.order.paymentMethod ?? 'Cash on Pickup',
                          style: theme.textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Total',
                          style: theme.textTheme.titleMedium,
                        ),
                        Text(
                          'EGP${currentOrder.totalPrice.toStringAsFixed(2)}',
                          style: theme.textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: theme.primaryColor,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),

                    // Cancel order button (only show if order can be cancelled)
                    if (currentOrder.status.toLowerCase() == 'pending' ||
                        currentOrder.status.toLowerCase() == 'new')
                      Padding(
                        padding: const EdgeInsets.only(bottom: 8.0),
                        child: SizedBox(
                          width: double.infinity,
                          child: OutlinedButton(
                            onPressed: _showCancelOrderDialog,
                            style: OutlinedButton.styleFrom(
                              foregroundColor: AppTheme.errorColor,
                              side: BorderSide(color: AppTheme.errorColor, width: 2),
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(Icons.cancel_outlined, size: 20),
                                SizedBox(width: 8),
                                Text(
                                  'Cancel Order',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),

                    // Pickup confirmation button (only show if order is ready for pickup)
                    if (currentOrder.status.toLowerCase() == 'ready' ||
                        currentOrder.status == OrderStatusUtils.statusReadyForPickup)
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: _confirmPickup,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.successColor,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: const Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.check_circle_outline),
                              SizedBox(width: 8),
                              Text(
                                'I\'ve picked up my order',
                                style: TextStyle(
                                  fontSize: 16,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),

                    // Rating button for completed orders (only show if order is completed and not rated)
                    if ((currentOrder.status.toLowerCase() == 'completed' ||
                        currentOrder.status == OrderStatusUtils.statusCompletedDisplay) &&
                        (currentOrder.rating == null || currentOrder.rating == 0))
                      Padding(
                        padding: const EdgeInsets.only(top: 8.0),
                        child: SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              // Reset the flag and show rating dialog
                              _hasShownRatingDialog = false;
                              _showRatingDialog();
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.amber,
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            icon: const Icon(Icons.star_rate),
                            label: const Text(
                              'Rate Your Order',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ),

                    const SizedBox(height: 8),
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton(
                        onPressed: () {
                          // Navigate to support chat with order context
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => SupportChatScreen(
                                orderId: currentOrder.id,
                                orderNumber: currentOrder.orderNumber,
                              ),
                            ),
                          );
                        },
                        child: const Text('Need Help with Order?'),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
      },
    );
  }

  Widget _buildStatusIcon(BuildContext context, IconData icon, String label,
      {required bool isActive, required bool isCompleted}) {
    final theme = Theme.of(context);
    final color = isActive ? theme.primaryColor : AppTheme.textSecondaryColor;

    return Column(
      children: [
        Container(
          width: 48,
          height: 48,
          decoration: BoxDecoration(
            color: isCompleted
                ? theme.primaryColor
                : theme.colorScheme.surfaceContainerHighest,
            shape: BoxShape.circle,
          ),
          child: Icon(
            icon,
            color: isCompleted ? Colors.white : AppTheme.textSecondaryColor,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          label,
          style: theme.textTheme.bodySmall?.copyWith(
            color: color,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
      ],
    );
  }
}
