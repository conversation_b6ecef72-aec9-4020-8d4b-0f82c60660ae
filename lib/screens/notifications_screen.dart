import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/providers/notification_provider.dart';
import 'package:unieatsappv0/providers/order_provider.dart';
import 'package:unieatsappv0/providers/order_history_provider.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/services/notification_service.dart';
import 'package:intl/intl.dart';
import 'package:timeago/timeago.dart' as timeago;

class NotificationsScreen extends StatefulWidget {
  const NotificationsScreen({super.key});

  @override
  State<NotificationsScreen> createState() => _NotificationsScreenState();
}

class _NotificationsScreenState extends State<NotificationsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<NotificationModel> _allNotifications = [];
  List<NotificationModel> _unreadNotifications = [];
  List<NotificationModel> _readNotifications = [];
  bool _isLoading = true;
  int _unreadCount = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadNotifications();
    _setupNotificationListener();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  void _setupNotificationListener() {
    NotificationService().notificationStream.listen((notification) {
      setState(() {
        _allNotifications.insert(0, notification);
        if (!notification.isRead) {
          _unreadNotifications.insert(0, notification);
          _unreadCount++;
        } else {
          _readNotifications.insert(0, notification);
        }
      });
    });
  }

  Future<void> _loadNotifications() async {
    setState(() => _isLoading = true);

    try {
      final notifications = await NotificationService().getNotifications();
      final unreadCount = await NotificationService().getUnreadCount();

      setState(() {
        _allNotifications = notifications;
        _unreadNotifications = notifications.where((n) => !n.isRead).toList();
        _readNotifications = notifications.where((n) => n.isRead).toList();
        _unreadCount = unreadCount;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
      _showErrorSnackBar('Failed to load notifications');
    }
  }

  Future<void> _markAsRead(NotificationModel notification) async {
    if (notification.isRead) return;

    final success = await NotificationService().markAsRead(notification.id);
    if (success) {
      setState(() {
        // Update the notification in all lists
        final index = _allNotifications.indexWhere((n) => n.id == notification.id);
        if (index != -1) {
          _allNotifications[index] = NotificationModel(
            id: notification.id,
            userId: notification.userId,
            title: notification.title,
            message: notification.message,
            type: notification.type,
            isRead: true,
            createdAt: notification.createdAt,
            relatedOrderId: notification.relatedOrderId,
          );
        }

        // Move from unread to read
        _unreadNotifications.removeWhere((n) => n.id == notification.id);
        _readNotifications.insert(0, _allNotifications[index]);
        _unreadCount = _unreadNotifications.length;
      });
    }
  }

  Future<void> _markAllAsRead() async {
    final success = await NotificationService().markAllAsRead();
    if (success) {
      setState(() {
        _allNotifications = _allNotifications.map((n) => NotificationModel(
          id: n.id,
          userId: n.userId,
          title: n.title,
          message: n.message,
          type: n.type,
          isRead: true,
          createdAt: n.createdAt,
          relatedOrderId: n.relatedOrderId,
        )).toList();

        _readNotifications = List.from(_allNotifications);
        _unreadNotifications.clear();
        _unreadCount = 0;
      });
    }
  }

  void _onNotificationTap(NotificationModel notification) {
    _markAsRead(notification);
    _navigateToNotification(notification);
  }

  void _navigateToNotification(NotificationModel notification) {
    switch (notification.type) {
      case 'order_status':
      case 'order_ready':
      case 'order_completed':
        if (notification.relatedOrderId != null) {
          Navigator.pushNamed(
            context,
            '/order-details',
            arguments: notification.relatedOrderId,
          );
        } else {
          Navigator.pushNamed(context, '/order_history');
        }
        break;
      case 'payment_success':
      case 'payment_failed':
        if (notification.relatedOrderId != null) {
          Navigator.pushNamed(
            context,
            '/order-details',
            arguments: notification.relatedOrderId,
          );
        } else {
          Navigator.pushNamed(context, '/order_history');
        }
        break;
      case 'promotion':
      case 'announcement':
        Navigator.pushNamed(context, '/home');
        break;
      default:
        // Stay on notifications screen
        break;
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    // Fallback to old provider system if new system fails
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);
    final oldNotifications = notificationProvider.notifications;

    // Use new notifications if available, otherwise fallback to old system
    final notifications = _allNotifications.isNotEmpty ? _allNotifications : oldNotifications;
    final hasNewSystem = _allNotifications.isNotEmpty || !_isLoading;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Notifications'),
        actions: [
          if (hasNewSystem && _unreadCount > 0)
            TextButton.icon(
              onPressed: _markAllAsRead,
              icon: const Icon(Icons.done_all, size: 18),
              label: const Text('Mark all read'),
              style: TextButton.styleFrom(
                foregroundColor: Theme.of(context).primaryColor,
              ),
            ),
          IconButton(
            onPressed: hasNewSystem ? _loadNotifications : null,
            icon: const Icon(Icons.refresh),
          ),
        ],
        bottom: hasNewSystem ? TabBar(
          controller: _tabController,
          tabs: [
            Tab(text: 'All (${_allNotifications.length})'),
            Tab(text: 'Unread ($_unreadCount)'),
            Tab(text: 'Read (${_readNotifications.length})'),
          ],
        ) : null,
      ),
      body: hasNewSystem
          ? (_isLoading
              ? const Center(child: CircularProgressIndicator())
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildNotificationList(_allNotifications),
                    _buildNotificationList(_unreadNotifications),
                    _buildNotificationList(_readNotifications),
                  ],
                ))
          : _buildLegacyNotificationList(oldNotifications, theme),
    );
  }

  Widget _buildNotificationIcon(AppNotification notification, ThemeData theme) {
    IconData iconData;
    Color iconColor;

    switch (notification.type) {
      case 'order':
        iconData = Icons.receipt;
        iconColor = theme.colorScheme.primary;
        break;
      case 'promo':
        iconData = Icons.local_offer;
        iconColor = theme.colorScheme.secondary;
        break;
      case 'system':
      default:
        iconData = Icons.info;
        iconColor = theme.colorScheme.tertiary;
        break;
    }

    return CircleAvatar(
      backgroundColor: iconColor.withAlpha(25), // ~0.1 opacity
      child: Icon(
        iconData,
        color: iconColor,
      ),
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();

    // Convert both to local time for accurate comparison
    final localNow = now.toLocal();
    final localTimestamp = timestamp.toLocal();

    // Calculate the difference in local time
    final difference = localNow.difference(localTimestamp);

    debugPrint('🕐 Timestamp formatting:');
    debugPrint('   Original timestamp: $timestamp');
    debugPrint('   Local timestamp: $localTimestamp');
    debugPrint('   Local now: $localNow');
    debugPrint('   Difference: ${difference.inMinutes} minutes');

    // Handle negative differences (future timestamps)
    if (difference.isNegative) {
      return 'Just now';
    }

    if (difference.inDays > 0) {
      // For dates more than a day old, show the actual date
      final localTimestamp = timestamp.toLocal();
      return DateFormat('MMM d, h:mm a').format(localTimestamp);
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ${difference.inHours == 1 ? 'hour' : 'hours'} ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} ${difference.inMinutes == 1 ? 'minute' : 'minutes'} ago';
    } else {
      return 'Just now';
    }
  }

  Widget _buildNotificationList(List<NotificationModel> notifications) {
    if (notifications.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.notifications_none,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No notifications',
              style: TextStyle(
                fontSize: 18,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      );
    }

    return RefreshIndicator(
      onRefresh: _loadNotifications,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: notifications.length,
        itemBuilder: (context, index) {
          final notification = notifications[index];
          return _buildNotificationCard(notification);
        },
      ),
    );
  }

  Widget _buildNotificationCard(NotificationModel notification) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      elevation: notification.isRead ? 1 : 3,
      color: notification.isRead ? null : Colors.blue.shade50,
      child: InkWell(
        onTap: () => _onNotificationTap(notification),
        borderRadius: BorderRadius.circular(8),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: _getNotificationColor(notification.type).withOpacity(0.1),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Center(
                  child: Text(
                    _getNotificationIcon(notification.type),
                    style: const TextStyle(fontSize: 20),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            notification.title,
                            style: TextStyle(
                              fontWeight: notification.isRead ? FontWeight.normal : FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                        ),
                        if (!notification.isRead)
                          Container(
                            width: 8,
                            height: 8,
                            decoration: BoxDecoration(
                              color: _getNotificationColor(notification.type),
                              borderRadius: BorderRadius.circular(4),
                            ),
                          ),
                      ],
                    ),
                    const SizedBox(height: 4),
                    Text(
                      notification.message,
                      style: TextStyle(
                        color: Colors.grey[600],
                        fontSize: 14,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                          decoration: BoxDecoration(
                            color: _getNotificationColor(notification.type).withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            notification.type.replaceAll('_', ' ').toUpperCase(),
                            style: TextStyle(
                              color: _getNotificationColor(notification.type),
                              fontSize: 10,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          timeago.format(notification.createdAt),
                          style: TextStyle(
                            color: Colors.grey[500],
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _getNotificationIcon(String type) {
    switch (type) {
      case 'order_status':
        return '📦';
      case 'order_ready':
        return '✅';
      case 'order_completed':
        return '🎉';
      case 'payment_success':
        return '💳';
      case 'payment_failed':
        return '❌';
      case 'promotion':
        return '🎁';
      case 'announcement':
        return '📢';
      case 'inventory_alert':
        return '⚠️';
      default:
        return '🔔';
    }
  }

  Color _getNotificationColor(String type) {
    switch (type) {
      case 'order_ready':
      case 'order_completed':
      case 'payment_success':
        return Colors.green;
      case 'payment_failed':
        return Colors.red;
      case 'promotion':
        return Colors.purple;
      case 'inventory_alert':
        return Colors.orange;
      default:
        return Colors.blue;
    }
  }

  Widget _buildLegacyNotificationList(notifications, ThemeData theme) {
    final notificationProvider = Provider.of<NotificationProvider>(context, listen: false);

    return notifications.isEmpty
        ? Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.notifications_none,
                  size: 80,
                  color: theme.colorScheme.onSurface.withAlpha(102),
                ),
                const SizedBox(height: 16),
                Text(
                  'No Notifications',
                  style: theme.textTheme.headlineMedium,
                ),
                const SizedBox(height: 8),
                Text(
                  'You don\'t have any notifications yet',
                  style: theme.textTheme.bodyLarge?.copyWith(
                    color: theme.colorScheme.onSurface.withAlpha(153),
                  ),
                ),
              ],
            ),
          )
        : ListView.builder(
            itemCount: notifications.length,
            itemBuilder: (context, index) {
              final notification = notifications[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: ListTile(
                  leading: _buildNotificationIcon(notification, theme),
                  title: Text(
                    notification.title,
                    style: TextStyle(
                      fontWeight: notification.isRead
                          ? FontWeight.normal
                          : FontWeight.bold,
                    ),
                  ),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const SizedBox(height: 4),
                      Text(notification.message),
                      const SizedBox(height: 4),
                      Text(
                        _formatTimestamp(notification.timestamp),
                        style: theme.textTheme.bodySmall,
                      ),
                    ],
                  ),
                  isThreeLine: true,
                  onTap: () {
                    if (!notification.isRead) {
                      notificationProvider.markAsRead(notification.id);
                    }
                    Navigator.of(context).pushNamed('/order_history');
                  },
                ),
              );
            },
          );
  }
}
