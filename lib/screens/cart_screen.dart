import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/providers/cart_provider.dart';
import 'package:unieatsappv0/utils/snackbar_utils.dart';
import 'package:unieatsappv0/widgets/robust_image.dart';
import 'package:unieatsappv0/theme/modern_theme.dart';
import 'package:unieatsappv0/widgets/modern_components.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.2),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final cartProvider = Provider.of<CartProvider>(context);
    final cartItems = cartProvider.items;

    return Scaffold(
      backgroundColor: ModernTheme.backgroundColor,
      appBar: _buildModernAppBar(cartItems, cartProvider),
      body: AnimatedBuilder(
        animation: Listenable.merge([_fadeController, _slideController]),
        builder: (context, child) {
          return cartItems.isEmpty
              ? _buildEmptyCart(context)
              : _buildCartItems(context, cartItems, cartProvider);
        },
      ),
    );
  }

  Widget _buildEmptyCart(BuildContext context) {
    final theme = Theme.of(context);

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 1500),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: 0.8 + (value * 0.2),
                    child: Opacity(
                      opacity: value,
                      child: Container(
                        width: 120,
                        height: 120,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: LinearGradient(
                            colors: [
                              theme.colorScheme.primary.withOpacity(0.1),
                              theme.colorScheme.primary.withOpacity(0.05),
                            ],
                          ),
                        ),
                        child: Icon(
                          Icons.shopping_cart_outlined,
                          size: 60,
                          color: theme.colorScheme.primary.withOpacity(0.6),
                        ),
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 24),
              Text(
                'Your cart is empty',
                style: theme.textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Add items from the menu to get started',
                style: theme.textTheme.bodyLarge?.copyWith(
                  color: theme.textTheme.bodySmall?.color,
                ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 32),
              TweenAnimationBuilder<double>(
                duration: const Duration(milliseconds: 800),
                tween: Tween(begin: 0.0, end: 1.0),
                builder: (context, value, child) {
                  return Transform.scale(
                    scale: value,
                    child: ElevatedButton.icon(
                      onPressed: () {
                        Navigator.pushReplacementNamed(context, '/dashboard');
                      },
                      icon: const Icon(Icons.restaurant_menu),
                      label: const Text('Browse Menu'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCartItems(
    BuildContext context,
    List<CartItem> cartItems,
    CartProvider cartProvider,
  ) {
    final theme = Theme.of(context);
    final subtotal = cartProvider.subtotal;
    final total = cartProvider.total;

    return Column(
      children: [
        // Cafeteria header
        if (cartProvider.currentCafeteriaName != null)
          Container(
            width: double.infinity,
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  theme.colorScheme.primary.withOpacity(0.1),
                  theme.colorScheme.primary.withOpacity(0.05),
                ],
                begin: Alignment.centerLeft,
                end: Alignment.centerRight,
              ),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: theme.colorScheme.primary.withOpacity(0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.restaurant,
                  color: theme.colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Items from',
                        style: theme.textTheme.bodySmall?.copyWith(
                          color: theme.colorScheme.onSurface.withOpacity(0.7),
                        ),
                      ),
                      Text(
                        cartProvider.currentCafeteriaName!,
                        style: theme.textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${cartItems.length} item${cartItems.length != 1 ? 's' : ''}',
                    style: theme.textTheme.bodySmall?.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        Expanded(
          child: ListView.builder(
            itemCount: cartItems.length,
            itemBuilder: (ctx, index) {
              final item = cartItems[index];
              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Item image
                      ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: RobustImage(
                          imageUrl: item.image,
                          width: 80,
                          height: 80,
                          fit: BoxFit.cover,
                          errorWidget: Container(
                            width: 80,
                            height: 80,
                            color: theme.colorScheme.surface,
                            child: Icon(Icons.fastfood,
                                color: theme.textTheme.bodySmall?.color),
                          ),
                          placeholder: Container(
                            width: 80,
                            height: 80,
                            color: theme.colorScheme.surface,
                            child: const Center(
                              child: CircularProgressIndicator(strokeWidth: 2),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),

                      // Item details
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              item.name,
                              style: theme.textTheme.titleMedium,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${item.price.toStringAsFixed(2)} EGP',
                              style: theme.textTheme.bodyMedium?.copyWith(
                                color: theme.textTheme.bodySmall?.color,
                              ),
                            ),
                            if (item.notes != null &&
                                item.notes!.isNotEmpty) ...[
                              const SizedBox(height: 4),
                              Text(
                                'Note: ${item.notes}',
                                style: theme.textTheme.bodySmall?.copyWith(
                                  fontStyle: FontStyle.italic,
                                ),
                              ),
                            ],
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                _buildQuantityButton(
                                  icon: Icons.remove,
                                  onPressed: () {
                                    if (item.quantity > 1) {
                                      cartProvider.updateQuantity(
                                          item.id, item.quantity - 1);
                                    } else {
                                      cartProvider.removeItem(item.id);
                                    }
                                  },
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(horizontal: 8),
                                  child: Text(
                                    '${item.quantity}',
                                    style: theme.textTheme.bodyLarge,
                                  ),
                                ),
                                _buildQuantityButton(
                                  icon: Icons.add,
                                  onPressed: () {
                                    cartProvider.updateQuantity(
                                        item.id, item.quantity + 1);
                                  },
                                ),
                                const Spacer(),
                                Text(
                                  '${item.calculateItemTotal().toStringAsFixed(2)} EGP',
                                  style: theme.textTheme.titleMedium?.copyWith(
                                    color: theme.primaryColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        ),
        // Order summary
        Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                Text(
                  'Order Summary',
                  style: theme.textTheme.displaySmall,
                ),
                const SizedBox(height: 16),
                _buildSummaryRow(
                    'Subtotal', '${subtotal.toStringAsFixed(2)} EGP'),
                const SizedBox(height: 8),
                _buildSummaryRow('Service Fee (4%)',
                    '${cartProvider.serviceFee.toStringAsFixed(2)} EGP'),
                const Divider(height: 24),
                _buildSummaryRow(
                  'Total',
                  '${total.toStringAsFixed(2)} EGP',
                  isBold: true,
                ),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    if (cartItems.isNotEmpty) {
                      Navigator.pushNamed(
                        context,
                        '/checkout',
                        arguments: {
                          'cafeteriaName': cartItems.first.cafeteriaName,
                          'cafeteriaLocation': cartItems.first.buildingName,
                        },
                      );
                    }
                  },
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                  child: const Text('Proceed to Checkout'),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildQuantityButton({
    required IconData icon,
    required VoidCallback onPressed,
  }) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);

        return Container(
          decoration: BoxDecoration(
            border: Border.all(
                color: theme.textTheme.bodySmall?.color ?? Colors.grey),
            borderRadius: BorderRadius.circular(4),
          ),
          child: InkWell(
            onTap: onPressed,
            child: Padding(
              padding: const EdgeInsets.all(4),
              child:
                  Icon(icon, size: 16, color: theme.textTheme.bodySmall?.color),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSummaryRow(String label, String value, {bool isBold = false}) {
    return Builder(
      builder: (context) {
        final theme = Theme.of(context);

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
              ),
            ),
            Text(
              value,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                color: isBold ? theme.primaryColor : null,
              ),
            ),
          ],
        );
      },
    );
  }

  void _showClearCartDialog(CartProvider cartProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cart'),
        content: const Text('Are you sure you want to remove all items from your cart?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              cartProvider.clearCart();
              Navigator.of(context).pop();
              SnackBarUtils.showInfoSnackBar(
                context: context,
                message: 'Cart cleared',
                title: 'Cart',
              );
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  PreferredSizeWidget _buildModernAppBar(List<CartItem> cartItems, CartProvider cartProvider) {
    return AppBar(
      backgroundColor: Colors.transparent,
      elevation: 0,
      title: FadeTransition(
        opacity: _fadeAnimation,
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: ModernTheme.primaryGradient,
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.shopping_cart,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: ModernTheme.spaceM),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Your Cart',
                  style: ModernTheme.headingSmall,
                ),
                if (cartItems.isNotEmpty)
                  Text(
                    '${cartItems.length} item${cartItems.length > 1 ? 's' : ''}',
                    style: ModernTheme.bodySmall.copyWith(
                      color: ModernTheme.textSecondary,
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
      actions: [
        if (cartItems.isNotEmpty)
          FadeTransition(
            opacity: _fadeAnimation,
            child: Container(
              margin: const EdgeInsets.only(right: ModernTheme.spaceM),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(ModernTheme.radiusMedium),
                boxShadow: ModernTheme.softShadow,
              ),
              child: IconButton(
                icon: Icon(
                  Icons.delete_outline,
                  color: ModernTheme.primaryColor,
                ),
                onPressed: () => _showModernClearCartDialog(cartProvider),
              ),
            ),
          ),
      ],
    );
  }

  void _showModernClearCartDialog(CartProvider cartProvider) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(ModernTheme.radiusLarge),
        ),
        title: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.red.shade400, Colors.red.shade600],
                ),
                borderRadius: BorderRadius.circular(ModernTheme.radiusSmall),
              ),
              child: const Icon(
                Icons.warning_outlined,
                color: Colors.white,
                size: 20,
              ),
            ),
            const SizedBox(width: ModernTheme.spaceM),
            Text(
              'Clear Cart',
              style: ModernTheme.headingSmall,
            ),
          ],
        ),
        content: Text(
          'Are you sure you want to remove all items from your cart? This action cannot be undone.',
          style: ModernTheme.bodyMedium,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: ModernTheme.labelLarge.copyWith(
                color: ModernTheme.textSecondary,
              ),
            ),
          ),
          ModernButton(
            text: 'Clear Cart',
            onPressed: () {
              cartProvider.clearCart();
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Row(
                    children: [
                      const Icon(Icons.check_circle, color: Colors.white),
                      const SizedBox(width: 8),
                      const Text('Cart cleared successfully'),
                    ],
                  ),
                  backgroundColor: ModernTheme.accentColor,
                  behavior: SnackBarBehavior.floating,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  margin: const EdgeInsets.all(16),
                ),
              );
            },
            isSecondary: false,
          ),
        ],
      ),
    );
  }
}
