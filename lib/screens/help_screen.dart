import 'package:flutter/material.dart';
import 'package:unieatsappv0/screens/support_chat_screen.dart';
import 'package:url_launcher/url_launcher.dart';

class HelpScreen extends StatefulWidget {
  const HelpScreen({super.key});

  @override
  State<HelpScreen> createState() => _HelpScreenState();
}

class _HelpScreenState extends State<HelpScreen>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startAnimations();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );
  }

  void _startAnimations() {
    _fadeController.forward();
    Future.delayed(const Duration(milliseconds: 200), () {
      _slideController.forward();
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  Future<void> _launchURL(String url) async {
    final Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not launch $url')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: FadeTransition(
          opacity: _fadeAnimation,
          child: const Text('Help & Support'),
        ),
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: ListView(
            padding: const EdgeInsets.all(24),
            children: [
              const Text(
            'Get help and support for your UniEats experience',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          const Text(
            'Choose from the options below to get the assistance you need.',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
          const SizedBox(height: 32),

          // Live Chat Support
          _buildAnimatedCard(
            delay: 0,
            child: Card(
              elevation: 4,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(16),
                  gradient: LinearGradient(
                    colors: [
                      Colors.blue.withOpacity(0.1),
                      Colors.blue.withOpacity(0.05),
                    ],
                  ),
                ),
                child: ListTile(
                  contentPadding: const EdgeInsets.all(16),
                  leading: Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: const LinearGradient(
                        colors: [Colors.blue, Colors.blueAccent],
                      ),
                      borderRadius: BorderRadius.circular(25),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.blue.withOpacity(0.3),
                          blurRadius: 8,
                          spreadRadius: 1,
                        ),
                      ],
                    ),
                    child: const Icon(Icons.chat, color: Colors.white),
                  ),
                  title: const Text(
                    'Live Chat Support',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  subtitle: const Text(
                    'Chat with our support team in real-time',
                    style: TextStyle(fontSize: 14),
                  ),
                  trailing: Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      color: Colors.blue.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(15),
                    ),
                    child: const Icon(
                      Icons.arrow_forward_ios,
                      size: 16,
                      color: Colors.blue,
                    ),
                  ),
                  onTap: () {
                    Navigator.push(
                      context,
                      _createSlideTransition(const SupportChatScreen()),
                    );
                  },
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),

          // FAQs
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.green,
                child: Icon(Icons.help_outline, color: Colors.white),
              ),
              title: const Text(
                'FAQs',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('Find answers to frequently asked questions'),
              trailing: const Icon(Icons.arrow_forward_ios),
              onTap: () {
                _showFAQs(context);
              },
            ),
          ),
          const SizedBox(height: 16),

          // Email Support
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.orange,
                child: Icon(Icons.email, color: Colors.white),
              ),
              title: const Text(
                'Email Support',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('<EMAIL>'),
              trailing: const Icon(Icons.copy),
              onTap: () {
                _copyToClipboard(context, '<EMAIL>');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Phone Support
          Card(
            elevation: 2,
            child: ListTile(
              leading: const CircleAvatar(
                backgroundColor: Colors.red,
                child: Icon(Icons.phone, color: Colors.white),
              ),
              title: const Text(
                'Call Us',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              subtitle: const Text('01225958284'),
              trailing: const Icon(Icons.copy),
              onTap: () {
                _copyToClipboard(context, '01225958284');
              },
            ),
          ),
          const SizedBox(height: 16),

          // Social Media Section
          Card(
            elevation: 2,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Follow Us',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            _launchURL('https://www.instagram.com/unieats0?igsh=cmFuYXJldzdxM2ti&utm_source=qr');
                          },
                          icon: const Icon(Icons.camera_alt, color: Colors.white),
                          label: const Text('Instagram'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.purple,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: ElevatedButton.icon(
                          onPressed: () {
                            _launchURL('https://www.tiktok.com/@unieats0?_t=ZS-8xVWBnVTlJI&_r=1');
                          },
                          icon: const Icon(Icons.music_note, color: Colors.white),
                          label: const Text('TikTok'),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.black,
                            foregroundColor: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 32),
          const SizedBox(height: 32),

          // Support Hours
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Support Hours',
                  style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                ),
                SizedBox(height: 8),
                Text('Monday - Friday: 8:00 AM - 8:00 PM'),
                Text('Saturday: 9:00 AM - 6:00 PM'),
                Text('Sunday: 10:00 AM - 4:00 PM'),
                SizedBox(height: 8),
                Text(
                  'Live chat is available 24/7 for urgent issues',
                  style: TextStyle(fontStyle: FontStyle.italic, color: Colors.blue),
                ),
              ],
            ),
          ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAnimatedCard({required int delay, required Widget child}) {
    return TweenAnimationBuilder<double>(
      duration: Duration(milliseconds: 800 + (delay * 200)),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, _) {
        return Transform.translate(
          offset: Offset(0, 20 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: child,
          ),
        );
      },
    );
  }

  PageRouteBuilder _createSlideTransition(Widget page) {
    return PageRouteBuilder(
      pageBuilder: (context, animation, secondaryAnimation) => page,
      transitionsBuilder: (context, animation, secondaryAnimation, child) {
        const begin = Offset(1.0, 0.0);
        const end = Offset.zero;
        const curve = Curves.easeInOutCubic;

        var tween = Tween(begin: begin, end: end).chain(
          CurveTween(curve: curve),
        );

        return SlideTransition(
          position: animation.drive(tween),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 600),
    );
  }

  void _copyToClipboard(BuildContext context, String text) {
    // In a real app, you'd use the clipboard package
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('$text copied to clipboard'),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  void _showFAQs(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Frequently Asked Questions'),
        content: SizedBox(
          width: 400,
          height: 400,
          child: ListView(
            children: const [
              ExpansionTile(
                title: Text('How do I place an order?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Browse cafeterias, select items, add to cart, choose pickup time, and pay with Apple Pay or credit card.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('How do I track my order?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Go to Order History to see real-time status updates. You will receive notifications when your order is ready for pickup.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('Can I cancel my order?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Orders can be cancelled within 5 minutes of placement. After that, please contact the cafeteria directly.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('What payment methods are accepted?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('We accept Apple Pay and credit/debit cards. No cash payments or delivery service available.'),
                  ),
                ],
              ),
              ExpansionTile(
                title: Text('How does pickup work?'),
                children: [
                  Padding(
                    padding: EdgeInsets.all(16),
                    child: Text('Select your preferred pickup time during checkout. You will receive a notification when your order is ready. Show your order confirmation at the cafeteria to collect your food.'),
                  ),
                ],
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}