import 'package:flutter/foundation.dart';
import 'package:unieatsappv0/models/menu_item.dart';

/// Base model class for Supabase entities
abstract class SupabaseModel {
  Map<String, dynamic> toJson();

  // Factory method to be implemented by subclasses
  static SupabaseModel fromJson(Map<String, dynamic> json) {
    throw UnimplementedError('Subclasses must implement fromJson');
  }
}

/// User profile model
class UserProfile extends SupabaseModel {
  final String id;
  final String? email;
  final String? fullName;
  final String? avatarUrl;
  final String? roleId; // This is the UUID of the role
  final String? phone; // Added phone field
  final String? theme;
  final bool? notificationEnabled;
  final bool? isSuspended; // Added suspension status field
  final DateTime? createdAt;
  final DateTime? updatedAt;

  UserProfile({
    required this.id,
    this.email,
    this.fullName,
    this.avatarUrl,
    this.roleId,
    this.phone,
    this.theme,
    this.notificationEnabled,
    this.isSuspended,
    this.createdAt,
    this.updatedAt,
  });

  factory UserProfile.fromJson(Map<String, dynamic> json) {
    return UserProfile(
      id: json['id'],
      email: json['email'],
      fullName: json['full_name'],
      avatarUrl: json['avatar_url'],
      roleId: json['role'],
      phone: json['phone'],
      theme: json['theme'],
      notificationEnabled: json['notification_enabled'],
      isSuspended: json['is_suspended'] ?? false,
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'full_name': fullName,
      'avatar_url': avatarUrl,
      'role': roleId,
      'phone': phone,
      'theme': theme,
      'notification_enabled': notificationEnabled,
      'is_suspended': isSuspended,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }
}

// Add more model classes based on your Supabase tables
// For example:

/// Cafeteria model for Supabase
class SupabaseCafeteria extends SupabaseModel {
  final String id;
  final String name;
  final String? description;
  final String? imageUrl;
  final String? location;
  final double? rating;
  final DateTime? createdAt;
  final bool isActive;
  final String operationalStatus;
  final String? statusMessage;
  final DateTime? statusUpdatedAt;
  final bool isOpen;

  SupabaseCafeteria({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    this.location,
    this.rating,
    this.createdAt,
    this.isActive = true,
    this.operationalStatus = 'open',
    this.statusMessage,
    this.statusUpdatedAt,
    this.isOpen = true,
  });

  factory SupabaseCafeteria.fromJson(Map<String, dynamic> json) {
    return SupabaseCafeteria(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      imageUrl: json['image_url'],
      location: json['location'],
      rating: json['rating']?.toDouble(),
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      isActive: json['is_active'] ?? true,
      operationalStatus: json['operational_status'] ?? 'open',
      statusMessage: json['status_message'],
      statusUpdatedAt: json['status_updated_at'] != null
          ? DateTime.parse(json['status_updated_at'])
          : null,
      isOpen: json['is_open'] ?? true,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'image_url': imageUrl,
      'location': location,
      'rating': rating,
      'created_at': createdAt?.toIso8601String(),
      'is_active': isActive,
      'operational_status': operationalStatus,
      'status_message': statusMessage,
      'status_updated_at': statusUpdatedAt?.toIso8601String(),
      'is_open': isOpen,
    };
  }
}

/// Menu item model for Supabase
class SupabaseMenuItem extends SupabaseModel {
  final String id;
  final String cafeteriaId;
  final String name;
  final String? description;
  final double price;
  final String? imageUrl;
  final bool isAvailable;
  final String? category;
  final Map<String, dynamic>? nutritionInfo;
  final List<String>? ingredients;
  final Map<String, dynamic>? customizationOptions;
  final List<String>? allergens;
  final double? rating;
  final int? totalRatings;
  final int? preparationTime;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  SupabaseMenuItem({
    required this.id,
    required this.cafeteriaId,
    required this.name,
    this.description,
    required this.price,
    this.imageUrl,
    this.isAvailable = true,
    this.category,
    this.nutritionInfo,
    this.ingredients,
    this.customizationOptions,
    this.allergens,
    this.rating,
    this.totalRatings,
    this.preparationTime,
    this.createdAt,
    this.updatedAt,
  });

  factory SupabaseMenuItem.fromJson(Map<String, dynamic> json) {
    // Helper function to safely parse customization options
    Map<String, dynamic>? parseCustomizationOptions(dynamic value) {
      if (value == null) return null;
      if (value is Map<String, dynamic>) return value;
      if (value is List && value.isEmpty) return null;
      // If it's a non-empty list, try to convert it to a map
      if (value is List) {
        try {
          return Map<String, dynamic>.fromEntries(
            value.asMap().entries.map((e) => MapEntry(e.key.toString(), e.value))
          );
        } catch (e) {
          debugPrint('Warning: Could not parse customization_options: $value');
          return null;
        }
      }
      return null;
    }

    return SupabaseMenuItem(
      id: json['id'],
      cafeteriaId: json['cafeteria_id'],
      name: json['name'],
      description: json['description'],
      price: json['price'].toDouble(),
      imageUrl: json['image_url'],
      isAvailable: json['is_available'] ?? true,
      category: json['category'],
      nutritionInfo: json['nutrition_info'] is Map<String, dynamic>
          ? json['nutrition_info']
          : null,
      ingredients: json['ingredients'] != null
          ? List<String>.from(json['ingredients'])
          : null,
      customizationOptions: parseCustomizationOptions(json['customization_options']),
      allergens: json['allergens'] != null
          ? List<String>.from(json['allergens'])
          : null,
      rating: json['rating']?.toDouble(),
      totalRatings: json['total_ratings'],
      preparationTime: json['preparation_time'],
      createdAt: json['created_at'] != null
          ? DateTime.parse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'cafeteria_id': cafeteriaId,
      'name': name,
      'description': description,
      'price': price,
      'image_url': imageUrl,
      'is_available': isAvailable,
      'category': category,
      'nutrition_info': nutritionInfo,
      'ingredients': ingredients,
      'customization_options': customizationOptions,
      'allergens': allergens,
      'rating': rating,
      'total_ratings': totalRatings,
      'preparation_time': preparationTime,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  /// Convert SupabaseMenuItem to MenuItem for UI compatibility
  MenuItem toMenuItem() {
    return MenuItem(
      id: id,
      name: name,
      price: price,
      description: description ?? '',
      image: imageUrl ?? 'assets/images/placeholder.png',
      cafeteriaId: cafeteriaId,
      category: category ?? 'Other',
      rating: rating ?? 0.0, // Use double? directly
      isAvailable: isAvailable,
      customizationOptions: customizationOptions,
      nutritionInfo: nutritionInfo,
      ingredients: ingredients,
    );
  }
}

/// Order model for Supabase
class SupabaseOrder extends SupabaseModel {
  final String id;
  final String userId;
  final String cafeteriaId;
  final double totalAmount;
  final String status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final List<SupabaseOrderItem>? items;
  final String? orderNumber;
  final String? pickupTime;
  final String? paymentMethod;
  final int? rating;
  final String? reviewComment;
  final String? cancellationReason;
  final String? cancelledBy;
  final DateTime? cancelledAt;

  SupabaseOrder({
    required this.id,
    required this.userId,
    required this.cafeteriaId,
    required this.totalAmount,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    this.items,
    this.orderNumber,
    this.pickupTime,
    this.paymentMethod,
    this.rating,
    this.reviewComment,
    this.cancellationReason,
    this.cancelledBy,
    this.cancelledAt,
  });

  factory SupabaseOrder.fromJson(Map<String, dynamic> json) {
    // Calculate total amount from items if not provided
    double totalAmount = 0.0;
    if (json['total_amount'] != null) {
      totalAmount = json['total_amount'].toDouble();
    }

    return SupabaseOrder(
      id: json['id'],
      userId: json['student_id'] ?? json['user_id'], // Support both field names
      cafeteriaId: json['cafeteria_id'],
      totalAmount: totalAmount,
      status: json['status'],
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'])
          : null,
      items: json['items'] != null
          ? (json['items'] as List)
              .map((item) => SupabaseOrderItem.fromJson(item))
              .toList()
          : null,
      orderNumber: json['order_number'],
      pickupTime: json['pickup_time'],
      paymentMethod: json['payment_method'],
      rating: json['rating'],
      reviewComment: json['review_comment'],
      cancellationReason: json['cancellation_reason'],
      cancelledBy: json['cancelled_by'],
      cancelledAt: json['cancelled_at'] != null
          ? DateTime.parse(json['cancelled_at'])
          : null,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'cafeteria_id': cafeteriaId,
      'total_amount': totalAmount,
      'status': status,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'order_number': orderNumber,
      'pickup_time': pickupTime,
      'payment_method': paymentMethod,
      // Don't include items in the main order JSON
    };
  }
}

/// Order item model for Supabase
class SupabaseOrderItem extends SupabaseModel {
  final String id;
  final String orderId;
  final String menuItemId;
  final int quantity;
  final double price;
  final String? notes;
  // Additional fields for menu item and cafeteria information
  final String? menuItemName;
  final String? menuItemImageUrl;
  final String? cafeteriaName;
  final String? cafeteriaLocation;

  SupabaseOrderItem({
    required this.id,
    required this.orderId,
    required this.menuItemId,
    required this.quantity,
    required this.price,
    this.notes,
    this.menuItemName,
    this.menuItemImageUrl,
    this.cafeteriaName,
    this.cafeteriaLocation,
  });

  factory SupabaseOrderItem.fromJson(Map<String, dynamic> json) {
    // Handle price which might not be in the database
    double price = 0.0;
    if (json['price'] != null) {
      price = json['price'].toDouble();
    }

    // Extract menu item information if available
    String? menuItemName;
    String? menuItemImageUrl;
    String? cafeteriaName;
    String? cafeteriaLocation;

    if (json['menu_items'] != null) {
      final menuItem = json['menu_items'];
      menuItemName = menuItem['name'];
      menuItemImageUrl = menuItem['image_url'];

      // Extract cafeteria information if available
      if (menuItem['cafeterias'] != null) {
        final cafeteria = menuItem['cafeterias'];
        cafeteriaName = cafeteria['name'];
        cafeteriaLocation = cafeteria['location'];
      }
    }

    return SupabaseOrderItem(
      id: json['id'] ?? '',
      orderId: json['order_id'] ?? '',
      menuItemId: json['item_id'] ?? json['menu_item_id'] ?? '', // Database uses 'item_id'
      quantity: json['quantity'] ?? 1,
      price: price,
      notes: json['selected_variant'] ?? json['notes'] ?? '', // Database uses 'selected_variant'
      menuItemName: menuItemName,
      menuItemImageUrl: menuItemImageUrl,
      cafeteriaName: cafeteriaName,
      cafeteriaLocation: cafeteriaLocation,
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'order_id': orderId,
      'menu_item_id': menuItemId,
      'quantity': quantity,
      'price': price,
      'notes': notes,
    };
  }
}

/// Menu item rating model for Supabase
class SupabaseMenuItemRating extends SupabaseModel {
  final String id;
  final String userId;
  final String menuItemId;
  final String? orderId;
  final int rating;
  final String? reviewComment;
  final DateTime createdAt;

  SupabaseMenuItemRating({
    required this.id,
    required this.userId,
    required this.menuItemId,
    this.orderId,
    required this.rating,
    this.reviewComment,
    required this.createdAt,
  });

  factory SupabaseMenuItemRating.fromJson(Map<String, dynamic> json) {
    return SupabaseMenuItemRating(
      id: json['id'],
      userId: json['user_id'],
      menuItemId: json['menu_item_id'],
      orderId: json['order_id'],
      rating: json['rating'],
      reviewComment: json['review_comment'],
      createdAt: DateTime.parse(json['created_at']),
    );
  }

  @override
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'menu_item_id': menuItemId,
      'order_id': orderId,
      'rating': rating,
      'review_comment': reviewComment,
      'created_at': createdAt.toIso8601String(),
    };
  }
}