class PaymentMethod {
  final String id;
  final String userId;
  final String methodType; // 'card', 'apple_pay', etc.
  final String provider; // 'visa', 'mastercard', 'amex', etc.
  final String? lastFour;
  final bool isDefault;
  final bool isActive;
  final Map<String, dynamic>? metadata; // For storing additional card info like expiry, cardholder name
  final DateTime createdAt;
  final DateTime updatedAt;

  PaymentMethod({
    required this.id,
    required this.userId,
    required this.methodType,
    required this.provider,
    this.lastFour,
    required this.isDefault,
    required this.isActive,
    this.metadata,
    required this.createdAt,
    required this.updatedAt,
  });

  factory PaymentMethod.fromJson(Map<String, dynamic> json) {
    return PaymentMethod(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      methodType: json['method_type'] as String,
      provider: json['provider'] as String,
      lastFour: json['last_four'] as String?,
      isDefault: json['is_default'] as bool? ?? false,
      isActive: json['is_active'] as bool? ?? true,
      metadata: json['metadata'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'method_type': methodType,
      'provider': provider,
      'last_four': lastFour,
      'is_default': isDefault,
      'is_active': isActive,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  // Helper getters for UI display
  String get displayName {
    if (methodType == 'apple_pay') {
      return 'Apple Pay';
    }
    return '${provider.toUpperCase()} •••• $lastFour';
  }

  String get cardholderName {
    return metadata?['cardholder_name'] as String? ?? '';
  }

  String get expiryMonth {
    return metadata?['expiry_month']?.toString().padLeft(2, '0') ?? '';
  }

  String get expiryYear {
    return metadata?['expiry_year']?.toString() ?? '';
  }

  String get expiryDisplay {
    if (expiryMonth.isNotEmpty && expiryYear.isNotEmpty) {
      return '$expiryMonth/${expiryYear.substring(2)}';
    }
    return '';
  }

  // Helper method to get card brand icon
  String get cardIcon {
    switch (provider.toLowerCase()) {
      case 'visa':
        return '💳'; // In a real app, you'd use proper card brand icons
      case 'mastercard':
        return '💳';
      case 'amex':
      case 'american express':
        return '💳';
      case 'discover':
        return '💳';
      default:
        return '💳';
    }
  }

  PaymentMethod copyWith({
    String? id,
    String? userId,
    String? methodType,
    String? provider,
    String? lastFour,
    bool? isDefault,
    bool? isActive,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentMethod(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      methodType: methodType ?? this.methodType,
      provider: provider ?? this.provider,
      lastFour: lastFour ?? this.lastFour,
      isDefault: isDefault ?? this.isDefault,
      isActive: isActive ?? this.isActive,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PaymentMethod && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;

  @override
  String toString() {
    return 'PaymentMethod(id: $id, provider: $provider, lastFour: $lastFour, isDefault: $isDefault)';
  }
}

// Helper class for creating new payment methods
class CreatePaymentMethodRequest {
  final String methodType;
  final String provider;
  final String lastFour;
  final String cardholderName;
  final int expiryMonth;
  final int expiryYear;
  final bool isDefault;

  CreatePaymentMethodRequest({
    required this.methodType,
    required this.provider,
    required this.lastFour,
    required this.cardholderName,
    required this.expiryMonth,
    required this.expiryYear,
    this.isDefault = false,
  });

  Map<String, dynamic> toJson(String userId) {
    return {
      'user_id': userId,
      'method_type': methodType,
      'provider': provider,
      'last_four': lastFour,
      'is_default': isDefault,
      'is_active': true,
      'metadata': {
        'cardholder_name': cardholderName,
        'expiry_month': expiryMonth,
        'expiry_year': expiryYear,
      },
    };
  }
}
