import 'package:flutter/foundation.dart';
import '../models/payment_method.dart';
import '../services/payment_methods_service.dart';

class PaymentMethodsProvider with ChangeNotifier {
  final PaymentMethodsService _service = PaymentMethodsService();
  
  List<PaymentMethod> _paymentMethods = [];
  PaymentMethod? _defaultPaymentMethod;
  bool _isLoading = false;
  String? _error;

  // Getters
  List<PaymentMethod> get paymentMethods => _paymentMethods;
  PaymentMethod? get defaultPaymentMethod => _defaultPaymentMethod;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get hasPaymentMethods => _paymentMethods.isNotEmpty;

  /// Load payment methods for a user
  Future<void> loadPaymentMethods(String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('🔄 Loading payment methods for user: $userId');
      
      _paymentMethods = await _service.getUserPaymentMethods(userId);
      _defaultPaymentMethod = _paymentMethods.firstWhere(
        (method) => method.isDefault,
        orElse: () => _paymentMethods.isNotEmpty ? _paymentMethods.first : null as PaymentMethod,
      );
      
      debugPrint('✅ Loaded ${_paymentMethods.length} payment methods');
    } catch (e) {
      _error = 'Failed to load payment methods: $e';
      debugPrint('❌ Error loading payment methods: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Add a new payment method
  Future<bool> addPaymentMethod(CreatePaymentMethodRequest request, String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('➕ Adding new payment method');
      
      final newMethod = await _service.addPaymentMethod(request, userId);
      _paymentMethods.add(newMethod);
      
      // If this is the first payment method or set as default, update default
      if (request.isDefault || _paymentMethods.length == 1) {
        _defaultPaymentMethod = newMethod;
        // Update other methods to not be default
        for (int i = 0; i < _paymentMethods.length; i++) {
          if (_paymentMethods[i].id != newMethod.id) {
            _paymentMethods[i] = _paymentMethods[i].copyWith(isDefault: false);
          }
        }
      }
      
      debugPrint('✅ Payment method added successfully');
      return true;
    } catch (e) {
      _error = 'Failed to add payment method: $e';
      debugPrint('❌ Error adding payment method: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Set a payment method as default
  Future<bool> setDefaultPaymentMethod(String paymentMethodId, String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('🎯 Setting payment method as default: $paymentMethodId');
      
      await _service.setDefaultPaymentMethod(paymentMethodId, userId);
      
      // Update local state
      for (int i = 0; i < _paymentMethods.length; i++) {
        if (_paymentMethods[i].id == paymentMethodId) {
          _paymentMethods[i] = _paymentMethods[i].copyWith(isDefault: true);
          _defaultPaymentMethod = _paymentMethods[i];
        } else {
          _paymentMethods[i] = _paymentMethods[i].copyWith(isDefault: false);
        }
      }
      
      debugPrint('✅ Default payment method updated');
      return true;
    } catch (e) {
      _error = 'Failed to set default payment method: $e';
      debugPrint('❌ Error setting default payment method: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Delete a payment method
  Future<bool> deletePaymentMethod(String paymentMethodId, String userId) async {
    _isLoading = true;
    _error = null;
    notifyListeners();

    try {
      debugPrint('🗑️ Deleting payment method: $paymentMethodId');
      
      await _service.deletePaymentMethod(paymentMethodId, userId);
      
      // Remove from local state
      final deletedMethod = _paymentMethods.firstWhere((method) => method.id == paymentMethodId);
      _paymentMethods.removeWhere((method) => method.id == paymentMethodId);
      
      // If the deleted method was default, set another as default
      if (deletedMethod.isDefault && _paymentMethods.isNotEmpty) {
        final newDefault = _paymentMethods.first;
        await setDefaultPaymentMethod(newDefault.id, userId);
      } else if (_paymentMethods.isEmpty) {
        _defaultPaymentMethod = null;
      }
      
      debugPrint('✅ Payment method deleted successfully');
      return true;
    } catch (e) {
      _error = 'Failed to delete payment method: $e';
      debugPrint('❌ Error deleting payment method: $e');
      return false;
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear all data (for logout)
  void clear() {
    _paymentMethods.clear();
    _defaultPaymentMethod = null;
    _isLoading = false;
    _error = null;
    notifyListeners();
  }

  /// Refresh payment methods
  Future<void> refresh(String userId) async {
    await loadPaymentMethods(userId);
  }

  /// Clear error
  void clearError() {
    _error = null;
    notifyListeners();
  }

  /// Get payment method by ID
  PaymentMethod? getPaymentMethodById(String id) {
    try {
      return _paymentMethods.firstWhere((method) => method.id == id);
    } catch (e) {
      return null;
    }
  }

  /// Check if a payment method is the default
  bool isDefaultPaymentMethod(String id) {
    return _defaultPaymentMethod?.id == id;
  }
}
