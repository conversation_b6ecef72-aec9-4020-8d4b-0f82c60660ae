import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:unieatsappv0/models/order.dart';
import 'package:unieatsappv0/services/supabase_order_service.dart';
import 'package:unieatsappv0/services/supabase_service_new.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';

import 'package:unieatsappv0/models/supabase_models.dart';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/utils/order_status_utils.dart';

class OrderHistoryProvider with ChangeNotifier {
  List<Order> _orders = [];
  bool _isLoading = false;
  static const String _storageKey = 'order_history';
  final SupabaseOrderService _orderService = SupabaseOrderService();

  List<Order> get orders => [..._orders];
  bool get isLoading => _isLoading;

  /// Clear all order history data
  void _clearOrderData() {
    _orders.clear();
    debugPrint('OrderHistoryProvider: Cleared all order data');
  }

  // Initialize the provider by loading orders directly from Supabase
  Future<void> loadOrders() async {
    _isLoading = true;
    notifyListeners();

    // Clear existing data first
    _clearOrderData();

    try {
      // Clear any problematic local storage first
      await _clearProblematicStorage();

      debugPrint('🔄 Loading order history directly from Supabase...');

      // Debug: Check current user authentication
      final authService = SimpleAuthService();
      final currentUser = authService.currentUser;
      if (currentUser != null) {
        debugPrint('🔍 Current authenticated user: ${currentUser.id} (${currentUser.email})');
      } else {
        debugPrint('❌ No user authenticated - cannot load orders');
        _orders = [];
        _isLoading = false;
        notifyListeners();
        return;
      }

      // Load orders directly from Supabase (no local storage fallback)
      final supabaseOrders = await _orderService.getUserOrders();
      debugPrint('✅ Loaded ${supabaseOrders.length} orders from Supabase for current user');

      if (supabaseOrders.isNotEmpty) {
        // Debug: Check if orders have items
        for (final order in supabaseOrders.take(3)) {
          debugPrint('🔍 Order ${order.id}: has ${order.items?.length ?? 0} items');
          if (order.items != null) {
            for (final item in order.items!.take(2)) {
              debugPrint('   - Item: ${item.menuItemId} (price: ${item.price}, qty: ${item.quantity})');
            }
          }
        }

        // Convert Supabase orders to local Order model
        _orders = await _convertSupabaseOrdersToLocal(supabaseOrders);

        // Sort orders by date (newest first)
        _orders.sort((a, b) => b.orderDate.compareTo(a.orderDate));

        debugPrint('✅ Successfully converted ${_orders.length} orders');
      } else {
        debugPrint('📱 No orders found in Supabase for current user - showing empty state');
        _orders = [];
      }

      debugPrint('📋 Total orders loaded: ${_orders.length}');
    } catch (e) {
      debugPrint('❌ Error loading orders from Supabase: $e');
      _orders = [];
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Clear problematic storage keys that cause quota issues
  Future<void> _clearProblematicStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Clear the specific key that's causing quota issues
      await prefs.remove('flutter.order_history');
      await prefs.remove(_storageKey);

      debugPrint('🧹 Cleared problematic storage keys');
    } catch (e) {
      debugPrint('⚠️ Error clearing storage: $e');
    }
  }

  // Add a new order to history (refresh from Supabase instead of local storage)
  Future<void> addOrder(Order order) async {
    // Instead of adding locally, refresh from Supabase to get the latest data
    await loadOrders();
  }

  // Note: We no longer save orders to local storage to avoid quota issues
  // Orders are always fetched fresh from Supabase for real-time accuracy

  // Get orders for a specific date
  List<Order> getOrdersForDate(DateTime date) {
    return _orders.where((order) {
      return order.dateTime.year == date.year &&
          order.dateTime.month == date.month &&
          order.dateTime.day == date.day;
    }).toList();
  }

  // Get orders by status
  List<Order> getOrdersByStatus(String status) {
    return _orders.where((order) => order.status == status).toList();
  }

  // Get recent orders (last 30 days)
  List<Order> getRecentOrders() {
    final DateTime thirtyDaysAgo =
        DateTime.now().subtract(const Duration(days: 30));
    return _orders
        .where((order) => order.dateTime.isAfter(thirtyDaysAgo))
        .toList();
  }

  // Get previously ordered menu items for reorder section (last 6 items)
  List<CartItem> getRecentlyOrderedItems({int limit = 6}) {
    try {
      debugPrint('🔍 Getting last $limit ordered items from ${_orders.length} orders...');

      if (_orders.isEmpty) {
        debugPrint('📱 No orders found for reorder items');
        return [];
      }

      // Get items from recent orders in chronological order (most recent first)
      final List<CartItem> recentItems = [];
      final Set<String> seenItemIds = {};

      // Sort orders by date (newest first)
      final sortedOrders = List<Order>.from(_orders)
        ..sort((a, b) => b.orderDate.compareTo(a.orderDate));

      debugPrint('📋 Processing ${sortedOrders.length} orders for recent items');

      // Go through orders from newest to oldest
      for (final order in sortedOrders) {
        if (recentItems.length >= limit) break;

        // Only include completed orders
        if (order.status.toLowerCase() != 'completed') continue;

        for (final item in order.items) {
          if (recentItems.length >= limit) break;

          // Add item if we haven't seen it before (avoid duplicates)
          if (!seenItemIds.contains(item.id)) {
            seenItemIds.add(item.id);
            recentItems.add(item);
            debugPrint('   ✅ Added recent item: ${item.name} from ${item.cafeteriaName}');
          }
        }
      }

      debugPrint('🎯 Returning ${recentItems.length} recent ordered items');
      return recentItems;
    } catch (e) {
      debugPrint('❌ Error getting recent ordered items: $e');
      return [];
    }
  }

  // Get all previously ordered menu items (for "See All" screen)
  List<CartItem> getAllPreviouslyOrderedItems() {
    try {
      debugPrint('🔍 Getting all previously ordered items from ${_orders.length} orders...');

      if (_orders.isEmpty) {
        debugPrint('📱 No orders found for all ordered items');
        return [];
      }

      // Get all unique items with their most recent order date
      final Map<String, CartItem> uniqueItems = {};
      final Map<String, DateTime> itemLastOrderDate = {};

      // Sort orders by date (newest first)
      final sortedOrders = List<Order>.from(_orders)
        ..sort((a, b) => b.orderDate.compareTo(a.orderDate));

      debugPrint('📋 Processing ${sortedOrders.length} orders for all items');

      for (final order in sortedOrders) {
        // Only include completed orders
        if (order.status.toLowerCase() != 'completed') continue;

        for (final item in order.items) {
          // If we haven't seen this item before, or this order is more recent
          if (!uniqueItems.containsKey(item.id) ||
              order.orderDate.isAfter(itemLastOrderDate[item.id]!)) {
            uniqueItems[item.id] = item;
            itemLastOrderDate[item.id] = order.orderDate;
          }
        }
      }

      // Sort items by their last order date (most recent first)
      final sortedItems = uniqueItems.values.toList()
        ..sort((a, b) => itemLastOrderDate[b.id]!.compareTo(itemLastOrderDate[a.id]!));

      debugPrint('🎯 Returning ${sortedItems.length} total previously ordered items');
      return sortedItems;
    } catch (e) {
      debugPrint('❌ Error getting all previously ordered items: $e');
      return [];
    }
  }

  // Get favorite items (most frequently ordered)
  List<CartItem> getFavoriteItems({int limit = 5}) {
    try {
      if (_orders.isEmpty) return [];

      // Count frequency of each item
      final Map<String, int> itemFrequency = {};
      final Map<String, CartItem> itemMap = {};

      for (final order in _orders) {
        if (order.status.toLowerCase() == 'completed') {
          for (final item in order.items) {
            itemFrequency[item.id] = (itemFrequency[item.id] ?? 0) + item.quantity;
            itemMap[item.id] = item;
          }
        }
      }

      // Sort by frequency and return top items
      final sortedItems = itemFrequency.entries.toList()
        ..sort((a, b) => b.value.compareTo(a.value));

      return sortedItems
          .take(limit)
          .map((entry) => itemMap[entry.key]!)
          .toList();
    } catch (e) {
      debugPrint('❌ Error getting favorite items: $e');
      return [];
    }
  }

  // Update an existing order (refresh from Supabase for latest data)
  Future<void> updateOrder(Order updatedOrder) async {
    // Refresh from Supabase to get the most up-to-date order information
    await loadOrders();
  }

  // Clear all order history (clear local cache and refresh from Supabase)
  Future<void> clearOrderHistory() async {
    _orders = [];
    notifyListeners();

    try {
      // Clear any local storage
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_storageKey);
      await prefs.remove('flutter.order_history');

      // Refresh from Supabase
      await loadOrders();
    } catch (e) {
      debugPrint('Error clearing order history: $e');
    }
  }

  // Convert Supabase orders to local Order model
  Future<List<Order>> _convertSupabaseOrdersToLocal(List<SupabaseOrder> supabaseOrders) async {
    final List<Order> localOrders = [];

    // Get unique cafeteria IDs from orders to fetch cafeteria names
    final cafeteriaIds = supabaseOrders.map((order) => order.cafeteriaId).toSet().toList();
    final Map<String, String> cafeteriaNames = {};

    try {
      final supabaseService = SupabaseService();
      final cafeteriasResponse = await supabaseService.client
          .from('cafeterias')
          .select('id, name')
          .inFilter('id', cafeteriaIds);

      for (final cafeteria in cafeteriasResponse) {
        cafeteriaNames[cafeteria['id']] = cafeteria['name'];
      }
      debugPrint('📦 Fetched ${cafeteriaNames.length} cafeteria names for orders');
    } catch (e) {
      debugPrint('⚠️ Error fetching cafeteria names: $e');
    }

    for (final supabaseOrder in supabaseOrders) {
      try {
        // Convert order items
        final List<CartItem> orderItems = [];
        if (supabaseOrder.items != null) {
          debugPrint('🔄 Converting ${supabaseOrder.items!.length} items for order ${supabaseOrder.id}');
          for (final item in supabaseOrder.items!) {
            debugPrint('   📦 Converting item: ${item.menuItemId} (${item.quantity}x ${item.price})');
            final cartItem = CartItem(
              id: item.menuItemId,
              name: item.menuItemName ?? 'Menu Item', // Use actual name from database
              price: item.price,
              image: item.menuItemImageUrl ?? 'assets/images/placeholder.png', // Use actual image URL
              quantity: item.quantity,
              cafeteriaName: item.cafeteriaName ?? cafeteriaNames[supabaseOrder.cafeteriaId] ?? 'Unknown Cafeteria',
              buildingName: item.cafeteriaLocation ?? 'Unknown Location', // Use actual location
              notes: item.notes ?? '',
              customizations: {},
            );
            orderItems.add(cartItem);
            debugPrint('   ✅ Added cart item: ${cartItem.name} from ${cartItem.cafeteriaName}');
          }
        } else {
          debugPrint('🔄 Converting 0 items for order ${supabaseOrder.id}');
        }

        // Convert to local Order model
        final localOrder = Order(
          id: supabaseOrder.id,
          orderNumber: supabaseOrder.orderNumber ?? supabaseOrder.id.substring(0, 8).toUpperCase(),
          userId: supabaseOrder.userId,
          orderDate: supabaseOrder.createdAt,
          items: orderItems,
          totalPrice: supabaseOrder.totalAmount,
          pickupTime: supabaseOrder.pickupTime ?? 'ASAP',
          status: OrderStatusUtils.getDisplayStatus(supabaseOrder.status),
          paymentMethod: supabaseOrder.paymentMethod ?? 'card',
          rating: supabaseOrder.rating,
          comment: supabaseOrder.reviewComment,
        );

        localOrders.add(localOrder);
      } catch (e) {
        debugPrint('❌ Error converting order ${supabaseOrder.id}: $e');
      }
    }

    return localOrders;
  }
}
