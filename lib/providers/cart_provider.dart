import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import 'package:unieatsappv0/models/cart_item.dart';
import 'package:unieatsappv0/services/simple_auth_service.dart';

class CartProvider with ChangeNotifier {
  final Map<String, CartItem> _items = {};
  String? _currentCafeteriaId;
  String? _currentCafeteriaName;
  bool _isLoading = false;

  List<CartItem> get items {
    return _items.values.toList();
  }

  int get itemCount {
    return _items.length;
  }

  String? get currentCafeteriaId => _currentCafeteriaId;
  String? get currentCafeteriaName => _currentCafeteriaName;
  bool get isLoading => _isLoading;

  double get subtotal {
    double total = 0.0;
    _items.forEach((key, cartItem) {
      total += cartItem.calculateItemTotal();
    });
    return total;
  }

  double get serviceFee {
    final fee = subtotal * 0.04;
    return fee > 20.0 ? 20.0 : fee;
  }

  double get total {
    return subtotal + serviceFee;
  }

  /// Initialize cart provider and load saved cart
  Future<void> initialize() async {
    await loadCartFromStorage();
  }

  /// Get storage key for current user's cart
  String _getCartStorageKey() {
    final currentUser = SimpleAuthService.getCurrentUser();
    if (currentUser != null) {
      return 'cart_${currentUser.id}';
    }
    return 'cart_guest'; // Fallback for guest users
  }

  /// Load cart from local storage
  Future<void> loadCartFromStorage() async {
    _isLoading = true;
    notifyListeners();

    try {
      final prefs = await SharedPreferences.getInstance();
      final cartKey = _getCartStorageKey();
      final cartData = prefs.getString(cartKey);

      if (cartData != null) {
        final Map<String, dynamic> cartJson = json.decode(cartData);

        // Load cafeteria info
        _currentCafeteriaId = cartJson['cafeteriaId'];
        _currentCafeteriaName = cartJson['cafeteriaName'];

        // Load items
        final List<dynamic> itemsJson = cartJson['items'] ?? [];
        _items.clear();

        for (final itemJson in itemsJson) {
          final cartItem = CartItem.fromJson(itemJson);
          _items[cartItem.id] = cartItem;
        }

        debugPrint('🛒 Loaded ${_items.length} items from storage for user');
      }
    } catch (e) {
      debugPrint('Error loading cart from storage: $e');
    } finally {
      _isLoading = false;
      notifyListeners();
    }
  }

  /// Save cart to local storage
  Future<void> saveCartToStorage() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final cartKey = _getCartStorageKey();

      final cartData = {
        'cafeteriaId': _currentCafeteriaId,
        'cafeteriaName': _currentCafeteriaName,
        'items': _items.values.map((item) => item.toJson()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      await prefs.setString(cartKey, json.encode(cartData));
      debugPrint('🛒 Saved cart to storage: ${_items.length} items');
    } catch (e) {
      debugPrint('Error saving cart to storage: $e');
    }
  }

  // Formatted currency strings
  String get formattedSubtotal => '${subtotal.toStringAsFixed(2)} EGP';
  String get formattedServiceFee => '${serviceFee.toStringAsFixed(2)} EGP';
  String get formattedTotal => '${total.toStringAsFixed(2)} EGP';

  /// Add item to cart with cafeteria restriction
  Future<bool> addItem(CartItem cartItem, {BuildContext? context}) async {
    // Check if this is from a different cafeteria
    if (_currentCafeteriaId != null &&
        _currentCafeteriaId != cartItem.menuItem?.cafeteriaId) {

      if (context != null) {
        // Show dialog asking user if they want to clear cart
        final shouldClear = await _showCafeteriaChangeDialog(context, cartItem.cafeteriaName);
        if (!shouldClear) {
          return false; // User cancelled
        }

        // Clear current cart and start fresh
        await clearCart();
      } else {
        // If no context provided, don't allow adding from different cafeteria
        debugPrint('🚫 Cannot add item from different cafeteria without user confirmation');
        return false;
      }
    }

    // Set current cafeteria if this is the first item
    if (_items.isEmpty) {
      _currentCafeteriaId = cartItem.menuItem?.cafeteriaId ?? cartItem.cafeteriaName;
      _currentCafeteriaName = cartItem.cafeteriaName;
    }

    if (_items.containsKey(cartItem.id)) {
      // Update existing item - preserve the existing notes if new item has no notes
      final existingItem = _items[cartItem.id]!;
      final notesToUse = cartItem.notes?.isNotEmpty == true ? cartItem.notes : existingItem.notes;

      _items.update(
        cartItem.id,
        (existingCartItem) => CartItem(
          id: existingCartItem.id,
          menuItem: existingCartItem.menuItem,
          quantity: existingCartItem.quantity + cartItem.quantity,
          customizations: cartItem.customizations,
          notes: notesToUse, // Use new notes if provided, otherwise keep existing
          name: existingCartItem.name,
          price: existingCartItem.price,
          image: existingCartItem.image,
          cafeteriaName: existingCartItem.cafeteriaName,
          buildingName: existingCartItem.buildingName,
        ),
      );
    } else {
      // Add new item
      _items.putIfAbsent(cartItem.id, () => cartItem);
    }

    // Save to storage
    await saveCartToStorage();
    notifyListeners();
    return true;
  }

  /// Show dialog when user tries to add item from different cafeteria
  Future<bool> _showCafeteriaChangeDialog(BuildContext context, String newCafeteriaName) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Icon(
                Icons.warning_amber_rounded,
                color: Colors.orange,
                size: 28,
              ),
              const SizedBox(width: 12),
              const Expanded(
                child: Text(
                  'Different Cafeteria',
                  style: TextStyle(fontSize: 18),
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Your cart contains items from $_currentCafeteriaName.',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.shade50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.orange.shade200),
                ),
                child: Text(
                  'Do you want to clear your current cart and start a new one from $newCafeteriaName?',
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.orange.shade700,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              const SizedBox(height: 12),
              const Text(
                'Note: All current items will be removed from your cart.',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.of(context).pop(true),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
              ),
              child: const Text('Clear & Continue'),
            ),
          ],
        );
      },
    ) ?? false;
  }

  void removeItem(String id) {
    _items.remove(id);
    saveCartToStorage(); // Save after removing
    notifyListeners();
  }

  void updateQuantity(String id, int quantity) {
    if (_items.containsKey(id)) {
      if (quantity <= 0) {
        removeItem(id);
      } else {
        _items.update(
          id,
          (existingCartItem) => CartItem(
            id: existingCartItem.id,
            menuItem: existingCartItem.menuItem,
            quantity: quantity,
            customizations: existingCartItem.customizations,
            notes: existingCartItem.notes, // Preserve the notes
            name: existingCartItem.name,
            price: existingCartItem.price,
            image: existingCartItem.image,
            cafeteriaName: existingCartItem.cafeteriaName,
            buildingName: existingCartItem.buildingName,
          ),
        );
        saveCartToStorage(); // Save after updating
        notifyListeners();
      }
    }
  }

  int getQuantity(String id) {
    if (_items.containsKey(id)) {
      return _items[id]!.quantity;
    }
    return 0;
  }

  Future<void> clearCart() async {
    _items.clear();
    _currentCafeteriaId = null;
    _currentCafeteriaName = null;
    await saveCartToStorage(); // Save empty cart
    notifyListeners();
  }

  /// Clear cart for specific user (when user logs out)
  Future<void> clearUserCart(String userId) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove('cart_$userId');
      debugPrint('🛒 Cleared cart for user: $userId');
    } catch (e) {
      debugPrint('Error clearing user cart: $e');
    }
  }
}
