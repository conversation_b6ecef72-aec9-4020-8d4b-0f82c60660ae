CREATE OR <PERSON><PERSON>LACE FUNCTION public.process_order_inventory()
<PERSON><PERSON><PERSON><PERSON> trigger
LANGUAGE plpgsql
AS $function$
DECLARE
  should_process_inventory BOOLEAN := FALSE;
  deduction_count INTEGER := 0;
  order_user_id UUID;
  inventory_item_record RECORD;
  current_quantity NUMERIC;
  new_quantity NUMERIC;
BEGIN
  -- Skip if inventory already processed
  IF NEW.inventory_processed = TRUE THEN
    RAISE NOTICE 'Skipping inventory processing for order: % - already processed', NEW.id;
    RETURN NEW;
  END IF;
  
  -- Determine if we should process inventory deduction
  IF TG_OP = 'UPDATE' AND OLD.status IN ('new', 'pending', 'preparing', 'ready') AND NEW.status = 'completed' THEN
    should_process_inventory := TRUE;
    RAISE NOTICE 'Processing inventory deduction for completed order: % (% -> %)', NEW.id, OLD.status, NEW.status;
  END IF;
  
  -- Process inventory deduction if needed
  IF should_process_inventory THEN
    
    -- Check if order has items before processing
    SELECT COUNT(*) INTO deduction_count FROM order_items WHERE order_id = NEW.id;
    
    IF deduction_count > 0 THEN
      
      RAISE NOTICE 'Found % order items for order %', deduction_count, NEW.id;
      
      -- Get the user who placed the order
      SELECT COALESCE(student_id, user_id) INTO order_user_id FROM orders WHERE id = NEW.id;
      
      -- Process each inventory item that needs to be deducted
      FOR inventory_item_record IN (
        SELECT 
          mii.inventory_item_id,
          SUM(mii.quantity_needed * oi.quantity) as total_quantity_needed
        FROM order_items oi
        JOIN menu_item_ingredients mii ON oi.item_id = mii.menu_item_id
        WHERE oi.order_id = NEW.id
        GROUP BY mii.inventory_item_id
      ) LOOP
        
        -- Get current quantity
        SELECT quantity INTO current_quantity 
        FROM inventory_items 
        WHERE id = inventory_item_record.inventory_item_id;
        
        -- Calculate new quantity
        new_quantity := GREATEST(0, current_quantity - inventory_item_record.total_quantity_needed);
        
        -- Update inventory quantity
        UPDATE inventory_items 
        SET 
          quantity = new_quantity,
          updated_at = NOW()
        WHERE id = inventory_item_record.inventory_item_id;
        
        -- Log the transaction with proper order reference
        INSERT INTO inventory_transactions (
          inventory_item_id,
          transaction_type,
          quantity_change,
          quantity_before,
          quantity_after,
          reference_type,
          reference_id,
          notes,
          created_by
        ) VALUES (
          inventory_item_record.inventory_item_id,
          'usage',
          -inventory_item_record.total_quantity_needed,
          current_quantity,
          new_quantity,
          'order',
          NEW.id,
          'Inventory deducted for order completion',
          order_user_id
        );
        
        RAISE NOTICE 'Deducted % from inventory item %', inventory_item_record.total_quantity_needed, inventory_item_record.inventory_item_id;
        
      END LOOP;
      
      -- Mark order as processed
      NEW.inventory_processed := TRUE;
      RAISE NOTICE 'Marked order % as inventory_processed', NEW.id;
      
    ELSE
      RAISE NOTICE 'No order items found for order: %', NEW.id;
    END IF;
    
  END IF;
  
  RETURN NEW;
END;
$function$;
