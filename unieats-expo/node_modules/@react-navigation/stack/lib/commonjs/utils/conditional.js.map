{"version": 3, "names": ["add", "multiply", "Animated", "conditional", "condition", "main", "fallback", "interpolate", "inputRange", "outputRange"], "sourceRoot": "../../../src", "sources": ["utils/conditional.tsx"], "mappings": ";;;;;;AAAA;AAEA,MAAM;EAAEA,GAAG;EAAEC;AAAS,CAAC,GAAGC,qBAAQ;;AAElC;AACA;AACA;AACA;AACA;AACA;AACA;AACe,SAASC,WAAW,CACjCC,SAAgD,EAChDC,IAA4C,EAC5CC,QAAgD,EAChD;EACA;EACA;EACA;EACA;EACA;EACA;EACA,OAAON,GAAG,CACRC,QAAQ,CAACG,SAAS,EAAEC,IAAI,CAAC,EACzBJ,QAAQ,CACNG,SAAS,CAACG,WAAW,CAAC;IACpBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAClBC,WAAW,EAAE,CAAC,CAAC,EAAE,CAAC;EACpB,CAAC,CAAC,EACFH,QAAQ,CACT,CACF;AACH"}