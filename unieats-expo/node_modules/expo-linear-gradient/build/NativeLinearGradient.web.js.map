{"version": 3, "file": "NativeLinearGradient.web.js", "sourceRoot": "", "sources": ["../src/NativeLinearGradient.web.tsx"], "names": [], "mappings": "AAAA,YAAY,CAAC;AACb,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAc,IAAI,EAAE,MAAM,cAAc,CAAC;AAGhD,OAAO,EAAE,cAAc,EAAE,MAAM,kBAAkB,CAAC;AAElD,MAAM,CAAC,OAAO,UAAU,oBAAoB,CAAC,EAC3C,MAAM,EACN,SAAS,EACT,UAAU,EACV,QAAQ,EACR,GAAG,KAAK,EACkB;IAC1B,MAAM,CAAC,EAAE,MAAM,EAAE,KAAK,EAAE,EAAE,SAAS,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC;QACpD,MAAM,EAAE,CAAC;QACT,KAAK,EAAE,CAAC;KACT,CAAC,CAAC;IAEH,gGAAgG;IAChG,mBAAmB;IACnB,MAAM,6BAA6B,GAAG,KAAK,CAAC,OAAO,CAAC,GAAG,EAAE;QACvD,OAAO,gCAAgC,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC;IAClG,CAAC,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;IAE7D,OAAO,CACL,CAAC,IAAI,CACH,IAAI,KAAK,CAAC,CACV,KAAK,CAAC,CAAC;YACL,KAAK,CAAC,KAAK;YACX,kFAAkF;YAClF,EAAE,eAAe,EAAE,6BAA6B,EAAE;SACnD,CAAC,CACF,QAAQ,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;YAClB,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,KAAK,CAAC,WAAW,CAAC,MAAM,CAAC;YAEnD,SAAS,CAAC,CAAC,SAAS,EAAE,EAAE;gBACtB,oEAAoE;gBACpE,IAAI,KAAK,KAAK,SAAS,CAAC,KAAK,IAAI,MAAM,KAAK,SAAS,CAAC,MAAM,EAAE,CAAC;oBAC7D,OAAO,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC;gBAC3B,CAAC;gBAED,OAAO,SAAS,CAAC;YACnB,CAAC,CAAC,CAAC;YAEH,IAAI,KAAK,CAAC,QAAQ,EAAE,CAAC;gBACnB,KAAK,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;YACxB,CAAC;QACH,CAAC,CAAC,EACF,CACH,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,MAAM,UAAU,gCAAgC,CAC9C,MAA6B,EAC7B,SAAoC,EACpC,UAA6C,EAC7C,QAA2C,EAC3C,QAAgB,CAAC,EACjB,SAAiB,CAAC;IAElB,MAAM,cAAc,GAAG,uBAAuB,CAAC,MAAM,EAAE,SAAS,CAAC,CAAC;IAClE,MAAM,KAAK,GAAG,oBAAoB,CAAC,KAAK,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC;IACxE,OAAO,mBAAmB,KAAK,QAAQ,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAAC;AACtE,CAAC;AAED,SAAS,oBAAoB,CAC3B,KAAa,EACb,MAAc,EACd,UAA6C,EAC7C,QAA2C;IAE3C,MAAM,gBAAgB,GAAG,GAAgC,EAAE;QACzD,IAAI,mBAAmB,GAA8B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC5D,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE,CAAC;YAC9B,mBAAmB,GAAG;gBACpB,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;gBAC3C,UAAU,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;aAC5C,CAAC;QACJ,CAAC;QACD,IAAI,iBAAiB,GAA8B,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC;QAC9D,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,EAAE,CAAC;YAC5B,iBAAiB,GAAG;gBAClB,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;gBACvC,QAAQ,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG;aACxC,CAAC;QACJ,CAAC;QACD,OAAO,CAAC,mBAAmB,EAAE,iBAAiB,CAAC,CAAC;IAClD,CAAC,CAAC;IAEF,MAAM,CAAC,KAAK,EAAE,GAAG,CAAC,GAAG,gBAAgB,EAAE,CAAC;IACxC,KAAK,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;IAClB,GAAG,CAAC,CAAC,CAAC,IAAI,KAAK,CAAC;IAChB,KAAK,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;IACnB,GAAG,CAAC,CAAC,CAAC,IAAI,MAAM,CAAC;IACjB,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAC7B,MAAM,EAAE,GAAG,GAAG,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;IAE7B,OAAO,EAAE,GAAG,CAAC,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,EAAE,CAAC,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC;AACnD,CAAC;AAED,SAAS,uBAAuB,CAC9B,MAA6B,EAC7B,SAAoC;IAEpC,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,KAAiB,EAAE,KAAa,EAAiB,EAAE;QACpE,MAAM,MAAM,GAAG,cAAc,CAAC,KAAK,CAAC,CAAC;QACrC,IAAI,SAAS,IAAI,SAAS,CAAC,KAAK,CAAC,EAAE,CAAC;YAClC,MAAM,QAAQ,GAAG,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,SAAS,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;YAC5D,2BAA2B;YAC3B,MAAM,UAAU,GAAG,QAAQ,GAAG,GAAG,CAAC;YAClC,OAAO,GAAG,MAAM,IAAI,UAAU,GAAG,CAAC;QACpC,CAAC;QACD,OAAO,MAAM,CAAC;IAChB,CAAC,CAAC,CAAC;AACL,CAAC", "sourcesContent": ["'use client';\nimport * as React from 'react';\nimport { ColorValue, View } from 'react-native';\n\nimport { NativeLinearGradientPoint, NativeLinearGradientProps } from './NativeLinearGradient.types';\nimport { normalizeColor } from './normalizeColor';\n\nexport default function NativeLinearGradient({\n  colors,\n  locations,\n  startPoint,\n  endPoint,\n  ...props\n}: NativeLinearGradientProps): React.ReactElement {\n  const [{ height, width }, setLayout] = React.useState({\n    height: 1,\n    width: 1,\n  });\n\n  // TODO(Bacon): In the future we could consider adding `backgroundRepeat: \"no-repeat\"`. For more\n  // browser support.\n  const linearGradientBackgroundImage = React.useMemo(() => {\n    return getLinearGradientBackgroundImage(colors, locations, startPoint, endPoint, width, height);\n  }, [colors, locations, startPoint, endPoint, width, height]);\n\n  return (\n    <View\n      {...props}\n      style={[\n        props.style,\n        // @ts-ignore: [ts] Property 'backgroundImage' does not exist on type 'ViewStyle'.\n        { backgroundImage: linearGradientBackgroundImage },\n      ]}\n      onLayout={(event) => {\n        const { width, height } = event.nativeEvent.layout;\n\n        setLayout((oldLayout) => {\n          // don't set new layout state unless the layout has actually changed\n          if (width !== oldLayout.width || height !== oldLayout.height) {\n            return { height, width };\n          }\n\n          return oldLayout;\n        });\n\n        if (props.onLayout) {\n          props.onLayout(event);\n        }\n      }}\n    />\n  );\n}\n\n/**\n * Extracted to a separate function in order to be able to test logic independently.\n */\nexport function getLinearGradientBackgroundImage(\n  colors: readonly ColorValue[],\n  locations?: readonly number[] | null,\n  startPoint?: NativeLinearGradientPoint | null,\n  endPoint?: NativeLinearGradientPoint | null,\n  width: number = 1,\n  height: number = 1\n) {\n  const gradientColors = calculateGradientColors(colors, locations);\n  const angle = calculatePseudoAngle(width, height, startPoint, endPoint);\n  return `linear-gradient(${angle}deg, ${gradientColors.join(', ')})`;\n}\n\nfunction calculatePseudoAngle(\n  width: number,\n  height: number,\n  startPoint?: NativeLinearGradientPoint | null,\n  endPoint?: NativeLinearGradientPoint | null\n) {\n  const getControlPoints = (): NativeLinearGradientPoint[] => {\n    let correctedStartPoint: NativeLinearGradientPoint = [0, 0];\n    if (Array.isArray(startPoint)) {\n      correctedStartPoint = [\n        startPoint[0] != null ? startPoint[0] : 0.0,\n        startPoint[1] != null ? startPoint[1] : 0.0,\n      ];\n    }\n    let correctedEndPoint: NativeLinearGradientPoint = [0.0, 1.0];\n    if (Array.isArray(endPoint)) {\n      correctedEndPoint = [\n        endPoint[0] != null ? endPoint[0] : 0.0,\n        endPoint[1] != null ? endPoint[1] : 1.0,\n      ];\n    }\n    return [correctedStartPoint, correctedEndPoint];\n  };\n\n  const [start, end] = getControlPoints();\n  start[0] *= width;\n  end[0] *= width;\n  start[1] *= height;\n  end[1] *= height;\n  const py = end[1] - start[1];\n  const px = end[0] - start[0];\n\n  return 90 + (Math.atan2(py, px) * 180) / Math.PI;\n}\n\nfunction calculateGradientColors(\n  colors: readonly ColorValue[],\n  locations?: readonly number[] | null\n) {\n  return colors.map((color: ColorValue, index: number): string | void => {\n    const output = normalizeColor(color);\n    if (locations && locations[index]) {\n      const location = Math.max(0, Math.min(1, locations[index]));\n      // Convert 0...1 to 0...100\n      const percentage = location * 100;\n      return `${output} ${percentage}%`;\n    }\n    return output;\n  });\n}\n"]}