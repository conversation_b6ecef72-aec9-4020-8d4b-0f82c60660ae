/**
 * @license
 * web-streams-polyfill v4.1.0
 * Copyright 2024 <PERSON>, <PERSON><PERSON><PERSON> and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */
function _classCallCheck(r,z){if(!(r instanceof z))throw new TypeError("Cannot call a class as a function")}function _defineProperties(r,z){for(var D=0;D<z.length;D++){var te=z[D];te.enumerable=te.enumerable||!1,te.configurable=!0,"value"in te&&(te.writable=!0),Object.defineProperty(r,_toPropertyKey(te.key),te)}}function _createClass(r,z,D){return z&&_defineProperties(r.prototype,z),D&&_defineProperties(r,D),Object.defineProperty(r,"prototype",{writable:!1}),r}function _toPropertyKey(r){var z=_toPrimitive(r,"string");return"symbol"==typeof z?z:z+""}function _toPrimitive(r,z){if("object"!=typeof r||!r)return r;var D=r[Symbol.toPrimitive];if(void 0!==D){var te=D.call(r,z||"default");if("object"!=typeof te)return te;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===z?String:Number)(r)}!function(){"use strict";function e(){}function t(r){return"object"==typeof r&&null!==r||"function"==typeof r}const r=e;function o(r,z){try{Object.defineProperty(r,"name",{value:z,configurable:!0})}catch(r){}}const z=Promise,D=Promise.resolve.bind(z),te=Promise.prototype.then,re=Promise.reject.bind(z),oe=D;function u(r){return new z(r)}function c(r){return u((z=>z(r)))}function d(r){return re(r)}function f(r,z,D){return te.call(r,z,D)}function b(z,D,te){f(f(z,D,te),void 0,r)}function h(r,z){b(r,z)}function m(r,z){b(r,void 0,z)}function _(r,z,D){return f(r,z,D)}function p(z){f(z,void 0,r)}let y=r=>{if("function"==typeof queueMicrotask)y=queueMicrotask;else{const r=c(void 0);y=z=>f(r,z)}return y(r)};function S(r,z,D){if("function"!=typeof r)throw new TypeError("Argument is not a function");return Function.prototype.apply.call(r,z,D)}function g(r,z,D){try{return c(S(r,z,D))}catch(r){return d(r)}}let de=function(){return _createClass((function v(){_classCallCheck(this,v),this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}),[{key:"length",get:function(){return this._size}},{key:"push",value:function push(r){const z=this._back;let D=z;16383===z._elements.length&&(D={_elements:[],_next:void 0}),z._elements.push(r),D!==z&&(this._back=D,z._next=D),++this._size}},{key:"shift",value:function shift(){const r=this._front;let z=r;const D=this._cursor;let te=D+1;const re=r._elements,oe=re[D];return 16384===te&&(z=r._next,te=0),--this._size,this._cursor=te,r!==z&&(this._front=z),re[D]=void 0,oe}},{key:"forEach",value:function forEach(r){let z=this._cursor,D=this._front,te=D._elements;for(;!(z===te.length&&void 0===D._next||z===te.length&&(D=D._next,te=D._elements,z=0,0===te.length));)r(te[z]),++z}},{key:"peek",value:function peek(){const r=this._front,z=this._cursor;return r._elements[z]}}])}();const me=Symbol("[[AbortSteps]]"),ye=Symbol("[[ErrorSteps]]"),Dt=Symbol("[[CancelSteps]]"),nr=Symbol("[[PullSteps]]"),ar=Symbol("[[ReleaseSteps]]");function q(r,z){r._ownerReadableStream=z,z._reader=r,"readable"===z._state?O(r):"closed"===z._state?function(r){O(r),A(r)}(r):j(r,z._storedError)}function E(r,z){return Br(r._ownerReadableStream,z)}function W(r){const z=r._ownerReadableStream;"readable"===z._state?k(r,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):function(r,z){j(r,new TypeError("Reader was released and can no longer be used to monitor the stream's closedness"))}(r),z._readableStreamController[ar](),z._reader=void 0,r._ownerReadableStream=void 0}function B(r){return new TypeError("Cannot "+r+" a stream using a released reader")}function O(r){r._closedPromise=u(((z,D)=>{r._closedPromise_resolve=z,r._closedPromise_reject=D}))}function j(r,z){O(r),k(r,z)}function k(r,z){void 0!==r._closedPromise_reject&&(p(r._closedPromise),r._closedPromise_reject(z),r._closedPromise_resolve=void 0,r._closedPromise_reject=void 0)}function A(r){void 0!==r._closedPromise_resolve&&(r._closedPromise_resolve(void 0),r._closedPromise_resolve=void 0,r._closedPromise_reject=void 0)}const no=Number.isFinite||function(r){return"number"==typeof r&&isFinite(r)},io=Math.trunc||function(r){return r<0?Math.ceil(r):Math.floor(r)};function L(r,z){if(void 0!==r&&"object"!=typeof(D=r)&&"function"!=typeof D)throw new TypeError(`${z} is not an object.`);var D}function F(r,z){if("function"!=typeof r)throw new TypeError(`${z} is not a function.`)}function I(r,z){if(!function(r){return"object"==typeof r&&null!==r||"function"==typeof r}(r))throw new TypeError(`${z} is not an object.`)}function $(r,z,D){if(void 0===r)throw new TypeError(`Parameter ${z} is required in '${D}'.`)}function M(r,z,D){if(void 0===r)throw new TypeError(`${z} is required in '${D}'.`)}function Y(r){return Number(r)}function Q(r){return 0===r?0:r}function x(r,z){const D=Number.MAX_SAFE_INTEGER;let te=Number(r);if(te=Q(te),!no(te))throw new TypeError(`${z} is not a finite number`);if(te=function(r){return Q(io(r))}(te),te<0||te>D)throw new TypeError(`${z} is outside the accepted range of 0 to ${D}, inclusive`);return no(te)&&0!==te?te:0}function N(r,z){if(!Er(r))throw new TypeError(`${z} is not a ReadableStream.`)}function H(r){return new ao(r)}function V(r,z){r._reader._readRequests.push(z)}function U(r,z,D){const te=r._reader._readRequests.shift();D?te._closeSteps():te._chunkSteps(z)}function G(r){return r._reader._readRequests.length}function X(r){const z=r._reader;return void 0!==z&&!!J(z)}let ao=function(){return _createClass((function ReadableStreamDefaultReader(r){if(_classCallCheck(this,ReadableStreamDefaultReader),$(r,1,"ReadableStreamDefaultReader"),N(r,"First parameter"),Wr(r))throw new TypeError("This stream has already been locked for exclusive reading by another reader");q(this,r),this._readRequests=new de}),[{key:"closed",get:function(){return J(this)?this._closedPromise:d(ee("closed"))}},{key:"cancel",value:function cancel(r=void 0){return J(this)?void 0===this._ownerReadableStream?d(B("cancel")):E(this,r):d(ee("cancel"))}},{key:"read",value:function read(){if(!J(this))return d(ee("read"));if(void 0===this._ownerReadableStream)return d(B("read from"));let r,z;const D=u(((D,te)=>{r=D,z=te}));return K(this,{_chunkSteps:z=>r({value:z,done:!1}),_closeSteps:()=>r({value:void 0,done:!0}),_errorSteps:r=>z(r)}),D}},{key:"releaseLock",value:function releaseLock(){if(!J(this))throw ee("releaseLock");void 0!==this._ownerReadableStream&&function(r){W(r);Z(r,new TypeError("Reader was released"))}(this)}}])}();function J(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_readRequests")&&r instanceof ao}function K(r,z){const D=r._ownerReadableStream;D._disturbed=!0,"closed"===D._state?z._closeSteps():"errored"===D._state?z._errorSteps(D._storedError):D._readableStreamController[nr](z)}function Z(r,z){const D=r._readRequests;r._readRequests=new de,D.forEach((r=>{r._errorSteps(z)}))}function ee(r){return new TypeError(`ReadableStreamDefaultReader.prototype.${r} can only be used on a ReadableStreamDefaultReader`)}var lo,so,uo;function ne(r){return r.slice()}function ae(r,z,D,te,re){new Uint8Array(r).set(new Uint8Array(D,te,re),z)}Object.defineProperties(ao.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(ao.prototype.cancel,"cancel"),o(ao.prototype.read,"read"),o(ao.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ao.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let ie=r=>(ie="function"==typeof r.transfer?r=>r.transfer():"function"==typeof structuredClone?r=>structuredClone(r,{transfer:[r]}):r=>r,ie(r)),le=r=>(le="boolean"==typeof r.detached?r=>r.detached:r=>0===r.byteLength,le(r));function se(r,z,D){if(r.slice)return r.slice(z,D);const te=D-z,re=new ArrayBuffer(te);return ae(re,0,r,z,te),re}function ue(r,z){const D=r[z];if(null!=D){if("function"!=typeof D)throw new TypeError(`${String(z)} is not a function`);return D}}function ce(r){try{const z=r.done,D=r.value;return f(oe(D),(r=>({done:z,value:r})))}catch(r){return d(r)}}const co=null!==(uo=null!==(lo=Symbol.asyncIterator)&&void 0!==lo?lo:null===(so=Symbol.for)||void 0===so?void 0:so.call(Symbol,"Symbol.asyncIterator"))&&void 0!==uo?uo:"@@asyncIterator";function fe(r,z="sync",D){if(void 0===D)if("async"===z){if(void 0===(D=ue(r,co)))return function(r){const z={next(){let z;try{z=be(r)}catch(r){return d(r)}return ce(z)},return(z){let D;try{const te=ue(r.iterator,"return");if(void 0===te)return c({done:!0,value:z});D=S(te,r.iterator,[z])}catch(r){return d(r)}return t(D)?ce(D):d(new TypeError("The iterator.return() method must return an object"))}};return{iterator:z,nextMethod:z.next,done:!1}}(fe(r,"sync",ue(r,Symbol.iterator)))}else D=ue(r,Symbol.iterator);if(void 0===D)throw new TypeError("The object is not iterable");const te=S(D,r,[]);if(!t(te))throw new TypeError("The iterator method must return an object");return{iterator:te,nextMethod:te.next,done:!1}}function be(r){const z=S(r.nextMethod,r.iterator,[]);if(!t(z))throw new TypeError("The iterator.next() method must return an object");return z}let fo=function(){return _createClass((function he(r,z){_classCallCheck(this,he),this._ongoingPromise=void 0,this._isFinished=!1,this._reader=r,this._preventCancel=z}),[{key:"next",value:function next(){const e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?_(this._ongoingPromise,e,e):e(),this._ongoingPromise}},{key:"return",value:function _return(r){const t=()=>this._returnSteps(r);return this._ongoingPromise=this._ongoingPromise?_(this._ongoingPromise,t,t):t(),this._ongoingPromise}},{key:"_nextSteps",value:function _nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});const r=this._reader;let z,D;const te=u(((r,te)=>{z=r,D=te}));return K(r,{_chunkSteps:r=>{this._ongoingPromise=void 0,y((()=>z({value:r,done:!1})))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,W(r),z({value:void 0,done:!0})},_errorSteps:z=>{this._ongoingPromise=void 0,this._isFinished=!0,W(r),D(z)}}),te}},{key:"_returnSteps",value:function _returnSteps(r){if(this._isFinished)return Promise.resolve({value:r,done:!0});this._isFinished=!0;const z=this._reader;if(!this._preventCancel){const D=E(z,r);return W(z),_(D,(()=>({value:r,done:!0})))}return W(z),c({value:r,done:!0})}}])}();const ho={next(){return _e(this)?this._asyncIteratorImpl.next():d(pe("next"))},return(r){return _e(this)?this._asyncIteratorImpl.return(r):d(pe("return"))},[co](){return this}};function _e(r){if(!t(r))return!1;if(!Object.prototype.hasOwnProperty.call(r,"_asyncIteratorImpl"))return!1;try{return r._asyncIteratorImpl instanceof fo}catch(r){return!1}}function pe(r){return new TypeError(`ReadableStreamAsyncIterator.${r} can only be used on a ReadableSteamAsyncIterator`)}Object.defineProperty(ho,co,{enumerable:!1});const bo=Number.isNaN||function(r){return r!=r};function Se(r){const z=se(r.buffer,r.byteOffset,r.byteOffset+r.byteLength);return new Uint8Array(z)}function ge(r){const z=r._queue.shift();return r._queueTotalSize-=z.size,r._queueTotalSize<0&&(r._queueTotalSize=0),z.value}function ve(r,z,D){if("number"!=typeof(te=D)||bo(te)||te<0||D===1/0)throw new RangeError("Size must be a finite, non-NaN, non-negative number.");var te;r._queue.push({value:z,size:D}),r._queueTotalSize+=D}function we(r){r._queue=new de,r._queueTotalSize=0}function Re(r){return r===DataView}let _o=function(){return _createClass((function ReadableStreamBYOBRequest(){throw _classCallCheck(this,ReadableStreamBYOBRequest),new TypeError("Illegal constructor")}),[{key:"view",get:function(){if(!Ce(this))throw Ke("view");return this._view}},{key:"respond",value:function respond(r){if(!Ce(this))throw Ke("respond");if($(r,1,"respond"),r=x(r,"First parameter"),void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(le(this._view.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");Ge(this._associatedReadableByteStreamController,r)}},{key:"respondWithNewView",value:function respondWithNewView(r){if(!Ce(this))throw Ke("respondWithNewView");if($(r,1,"respondWithNewView"),!ArrayBuffer.isView(r))throw new TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw new TypeError("This BYOB request has been invalidated");if(le(r.buffer))throw new TypeError("The given view's buffer has been detached and so cannot be used as a response");Xe(this._associatedReadableByteStreamController,r)}}])}();Object.defineProperties(_o.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),o(_o.prototype.respond,"respond"),o(_o.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(_o.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});let mo=function(){return _createClass((function ReadableByteStreamController(){throw _classCallCheck(this,ReadableByteStreamController),new TypeError("Illegal constructor")}),[{key:"byobRequest",get:function(){if(!Te(this))throw Ze("byobRequest");return Ve(this)}},{key:"desiredSize",get:function(){if(!Te(this))throw Ze("desiredSize");return Ue(this)}},{key:"close",value:function close(){if(!Te(this))throw Ze("close");if(this._closeRequested)throw new TypeError("The stream has already been closed; do not close it again!");const r=this._controlledReadableByteStream._state;if("readable"!==r)throw new TypeError(`The stream (in ${r} state) is not in the readable state and cannot be closed`);Qe(this)}},{key:"enqueue",value:function enqueue(r){if(!Te(this))throw Ze("enqueue");if($(r,1,"enqueue"),!ArrayBuffer.isView(r))throw new TypeError("chunk must be an array buffer view");if(0===r.byteLength)throw new TypeError("chunk must have non-zero byteLength");if(0===r.buffer.byteLength)throw new TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw new TypeError("stream is closed or draining");const z=this._controlledReadableByteStream._state;if("readable"!==z)throw new TypeError(`The stream (in ${z} state) is not in the readable state and cannot be enqueued to`);xe(this,r)}},{key:"error",value:function error(r=void 0){if(!Te(this))throw Ze("error");Ne(this,r)}},{key:Dt,value:function(r){qe(this),we(this);const z=this._cancelAlgorithm(r);return Ye(this),z}},{key:nr,value:function(r){const z=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void He(this,r);const D=this._autoAllocateChunkSize;if(void 0!==D){let te;try{te=new ArrayBuffer(D)}catch(z){return void r._errorSteps(z)}const re={buffer:te,bufferByteLength:D,byteOffset:0,byteLength:D,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(re)}V(z,r),Pe(this)}},{key:ar,value:function(){if(this._pendingPullIntos.length>0){const r=this._pendingPullIntos.peek();r.readerType="none",this._pendingPullIntos=new de,this._pendingPullIntos.push(r)}}}])}();function Te(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_controlledReadableByteStream")&&r instanceof mo}function Ce(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_associatedReadableByteStreamController")&&r instanceof _o}function Pe(r){const z=function(r){const z=r._controlledReadableByteStream;if("readable"!==z._state)return!1;if(r._closeRequested)return!1;if(!r._started)return!1;if(X(z)&&G(z)>0)return!0;if(nt(z)&&ot(z)>0)return!0;return Ue(r)>0}(r);z&&(r._pulling?r._pullAgain=!0:(r._pulling=!0,b(r._pullAlgorithm(),(()=>(r._pulling=!1,r._pullAgain&&(r._pullAgain=!1,Pe(r)),null)),(z=>(Ne(r,z),null)))))}function qe(r){Le(r),r._pendingPullIntos=new de}function Ee(r,z){let D=!1;"closed"===r._state&&(D=!0);const te=Be(z);"default"===z.readerType?U(r,te,D):function(r,z,D){const te=r._reader._readIntoRequests.shift();D?te._closeSteps(z):te._chunkSteps(z)}(r,te,D)}function We(r,z){for(let D=0;D<z.length;++D)Ee(r,z[D])}function Be(r){const z=r.bytesFilled,D=r.elementSize;return new r.viewConstructor(r.buffer,r.byteOffset,z/D)}function Oe(r,z,D,te){r._queue.push({buffer:z,byteOffset:D,byteLength:te}),r._queueTotalSize+=te}function je(r,z,D,te){let re;try{re=se(z,D,D+te)}catch(z){throw Ne(r,z),z}Oe(r,re,0,te)}function ke(r,z){z.bytesFilled>0&&je(r,z.buffer,z.byteOffset,z.bytesFilled),Me(r)}function Ae(r,z){const D=Math.min(r._queueTotalSize,z.byteLength-z.bytesFilled),te=z.bytesFilled+D;let re=D,oe=!1;const de=te-te%z.elementSize;de>=z.minimumFill&&(re=de-z.bytesFilled,oe=!0);const me=r._queue;for(;re>0;){const D=me.peek(),te=Math.min(re,D.byteLength),oe=z.byteOffset+z.bytesFilled;ae(z.buffer,oe,D.buffer,D.byteOffset,te),D.byteLength===te?me.shift():(D.byteOffset+=te,D.byteLength-=te),r._queueTotalSize-=te,ze(r,te,z),re-=te}return oe}function ze(r,z,D){D.bytesFilled+=z}function De(r){0===r._queueTotalSize&&r._closeRequested?(Ye(r),Or(r._controlledReadableByteStream)):Pe(r)}function Le(r){null!==r._byobRequest&&(r._byobRequest._associatedReadableByteStreamController=void 0,r._byobRequest._view=null,r._byobRequest=null)}function Fe(r){const z=[];for(;r._pendingPullIntos.length>0&&0!==r._queueTotalSize;){const D=r._pendingPullIntos.peek();Ae(r,D)&&(Me(r),z.push(D))}return z}function $e(r,z){const D=r._pendingPullIntos.peek();Le(r),"closed"===r._controlledReadableByteStream._state?function(r,z){"none"===z.readerType&&Me(r);const D=r._controlledReadableByteStream;if(nt(D)){const z=[];for(let te=0;te<ot(D);++te)z.push(Me(r));We(D,z)}}(r,D):function(r,z,D){if(ze(0,z,D),"none"===D.readerType){ke(r,D);const z=Fe(r);return void We(r._controlledReadableByteStream,z)}if(D.bytesFilled<D.minimumFill)return;Me(r);const te=D.bytesFilled%D.elementSize;if(te>0){const z=D.byteOffset+D.bytesFilled;je(r,D.buffer,z-te,te)}D.bytesFilled-=te;const re=Fe(r);Ee(r._controlledReadableByteStream,D),We(r._controlledReadableByteStream,re)}(r,z,D),Pe(r)}function Me(r){return r._pendingPullIntos.shift()}function Ye(r){r._pullAlgorithm=void 0,r._cancelAlgorithm=void 0}function Qe(r){const z=r._controlledReadableByteStream;if(!r._closeRequested&&"readable"===z._state)if(r._queueTotalSize>0)r._closeRequested=!0;else{if(r._pendingPullIntos.length>0){const z=r._pendingPullIntos.peek();if(z.bytesFilled%z.elementSize!=0){const z=new TypeError("Insufficient bytes to fill elements in the given buffer");throw Ne(r,z),z}}Ye(r),Or(z)}}function xe(r,z){const D=r._controlledReadableByteStream;if(r._closeRequested||"readable"!==D._state)return;const{buffer:te,byteOffset:re,byteLength:oe}=z;if(le(te))throw new TypeError("chunk's buffer is detached and so cannot be enqueued");const de=ie(te);if(r._pendingPullIntos.length>0){const z=r._pendingPullIntos.peek();if(le(z.buffer))throw new TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");Le(r),z.buffer=ie(z.buffer),"none"===z.readerType&&ke(r,z)}if(X(D))(function(r){const z=r._controlledReadableByteStream._reader;for(;z._readRequests.length>0;){if(0===r._queueTotalSize)return;He(r,z._readRequests.shift())}})(r),0===G(D)?Oe(r,de,re,oe):(r._pendingPullIntos.length>0&&Me(r),U(D,new Uint8Array(de,re,oe),!1));else if(nt(D)){Oe(r,de,re,oe);const z=Fe(r);We(r._controlledReadableByteStream,z)}else Oe(r,de,re,oe);Pe(r)}function Ne(r,z){const D=r._controlledReadableByteStream;"readable"===D._state&&(qe(r),we(r),Ye(r),jr(D,z))}function He(r,z){const D=r._queue.shift();r._queueTotalSize-=D.byteLength,De(r);const te=new Uint8Array(D.buffer,D.byteOffset,D.byteLength);z._chunkSteps(te)}function Ve(r){if(null===r._byobRequest&&r._pendingPullIntos.length>0){const z=r._pendingPullIntos.peek(),D=new Uint8Array(z.buffer,z.byteOffset+z.bytesFilled,z.byteLength-z.bytesFilled),te=Object.create(_o.prototype);!function(r,z,D){r._associatedReadableByteStreamController=z,r._view=D}(te,r,D),r._byobRequest=te}return r._byobRequest}function Ue(r){const z=r._controlledReadableByteStream._state;return"errored"===z?null:"closed"===z?0:r._strategyHWM-r._queueTotalSize}function Ge(r,z){const D=r._pendingPullIntos.peek();if("closed"===r._controlledReadableByteStream._state){if(0!==z)throw new TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===z)throw new TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(D.bytesFilled+z>D.byteLength)throw new RangeError("bytesWritten out of range")}D.buffer=ie(D.buffer),$e(r,z)}function Xe(r,z){const D=r._pendingPullIntos.peek();if("closed"===r._controlledReadableByteStream._state){if(0!==z.byteLength)throw new TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===z.byteLength)throw new TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(D.byteOffset+D.bytesFilled!==z.byteOffset)throw new RangeError("The region specified by view does not match byobRequest");if(D.bufferByteLength!==z.buffer.byteLength)throw new RangeError("The buffer of view has different capacity than byobRequest");if(D.bytesFilled+z.byteLength>D.byteLength)throw new RangeError("The region specified by view is larger than byobRequest");const te=z.byteLength;D.buffer=ie(z.buffer),$e(r,te)}function Je(r,z,D,te,re,oe,me){z._controlledReadableByteStream=r,z._pullAgain=!1,z._pulling=!1,z._byobRequest=null,z._queue=z._queueTotalSize=void 0,we(z),z._closeRequested=!1,z._started=!1,z._strategyHWM=oe,z._pullAlgorithm=te,z._cancelAlgorithm=re,z._autoAllocateChunkSize=me,z._pendingPullIntos=new de,r._readableStreamController=z,b(c(D()),(()=>(z._started=!0,Pe(z),null)),(r=>(Ne(z,r),null)))}function Ke(r){return new TypeError(`ReadableStreamBYOBRequest.prototype.${r} can only be used on a ReadableStreamBYOBRequest`)}function Ze(r){return new TypeError(`ReadableByteStreamController.prototype.${r} can only be used on a ReadableByteStreamController`)}function et(r,z){if("byob"!=(r=`${r}`))throw new TypeError(`${z} '${r}' is not a valid enumeration value for ReadableStreamReaderMode`);return r}function tt(r){return new po(r)}function rt(r,z){r._reader._readIntoRequests.push(z)}function ot(r){return r._reader._readIntoRequests.length}function nt(r){const z=r._reader;return void 0!==z&&!!at(z)}Object.defineProperties(mo.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),o(mo.prototype.close,"close"),o(mo.prototype.enqueue,"enqueue"),o(mo.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(mo.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});let po=function(){return _createClass((function ReadableStreamBYOBReader(r){if(_classCallCheck(this,ReadableStreamBYOBReader),$(r,1,"ReadableStreamBYOBReader"),N(r,"First parameter"),Wr(r))throw new TypeError("This stream has already been locked for exclusive reading by another reader");if(!Te(r._readableStreamController))throw new TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");q(this,r),this._readIntoRequests=new de}),[{key:"closed",get:function(){return at(this)?this._closedPromise:d(st("closed"))}},{key:"cancel",value:function cancel(r=void 0){return at(this)?void 0===this._ownerReadableStream?d(B("cancel")):E(this,r):d(st("cancel"))}},{key:"read",value:function read(r,z={}){if(!at(this))return d(st("read"));if(!ArrayBuffer.isView(r))return d(new TypeError("view must be an array buffer view"));if(0===r.byteLength)return d(new TypeError("view must have non-zero byteLength"));if(0===r.buffer.byteLength)return d(new TypeError("view's buffer must have non-zero byteLength"));if(le(r.buffer))return d(new TypeError("view's buffer has been detached"));let D;try{D=function(r,z){var D;return L(r,z),{min:x(null!==(D=null==r?void 0:r.min)&&void 0!==D?D:1,`${z} has member 'min' that`)}}(z,"options")}catch(r){return d(r)}const te=D.min;if(0===te)return d(new TypeError("options.min must be greater than 0"));if(function(r){return Re(r.constructor)}(r)){if(te>r.byteLength)return d(new RangeError("options.min must be less than or equal to view's byteLength"))}else if(te>r.length)return d(new RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return d(B("read from"));let re,oe;const de=u(((r,z)=>{re=r,oe=z}));return it(this,r,te,{_chunkSteps:r=>re({value:r,done:!1}),_closeSteps:r=>re({value:r,done:!0}),_errorSteps:r=>oe(r)}),de}},{key:"releaseLock",value:function releaseLock(){if(!at(this))throw st("releaseLock");void 0!==this._ownerReadableStream&&function(r){W(r);lt(r,new TypeError("Reader was released"))}(this)}}])}();function at(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_readIntoRequests")&&r instanceof po}function it(r,z,D,te){const re=r._ownerReadableStream;re._disturbed=!0,"errored"===re._state?te._errorSteps(re._storedError):function Ie(r,z,D,te){const re=r._controlledReadableByteStream,oe=z.constructor,de=function(r){return Re(r)?1:r.BYTES_PER_ELEMENT}(oe),{byteOffset:me,byteLength:ye}=z,Dt=D*de;let nr;try{nr=ie(z.buffer)}catch(r){return void te._errorSteps(r)}const ar={buffer:nr,bufferByteLength:nr.byteLength,byteOffset:me,byteLength:ye,bytesFilled:0,minimumFill:Dt,elementSize:de,viewConstructor:oe,readerType:"byob"};if(r._pendingPullIntos.length>0)return r._pendingPullIntos.push(ar),void rt(re,te);if("closed"!==re._state){if(r._queueTotalSize>0){if(Ae(r,ar)){const z=Be(ar);return De(r),void te._chunkSteps(z)}if(r._closeRequested){const z=new TypeError("Insufficient bytes to fill elements in the given buffer");return Ne(r,z),void te._errorSteps(z)}}r._pendingPullIntos.push(ar),rt(re,te),Pe(r)}else{const r=new oe(ar.buffer,ar.byteOffset,0);te._closeSteps(r)}}(re._readableStreamController,z,D,te)}function lt(r,z){const D=r._readIntoRequests;r._readIntoRequests=new de,D.forEach((r=>{r._errorSteps(z)}))}function st(r){return new TypeError(`ReadableStreamBYOBReader.prototype.${r} can only be used on a ReadableStreamBYOBReader`)}function ut(r,z){const{highWaterMark:D}=r;if(void 0===D)return z;if(bo(D)||D<0)throw new RangeError("Invalid highWaterMark");return D}function ct(r){const{size:z}=r;return z||(()=>1)}function dt(r,z){L(r,z);const D=null==r?void 0:r.highWaterMark,te=null==r?void 0:r.size;return{highWaterMark:void 0===D?void 0:Y(D),size:void 0===te?void 0:ft(te,`${z} has member 'size' that`)}}function ft(r,z){return F(r,z),z=>Y(r(z))}function bt(r,z,D){return F(r,D),D=>g(r,z,[D])}function ht(r,z,D){return F(r,D),()=>g(r,z,[])}function mt(r,z,D){return F(r,D),D=>S(r,z,[D])}function _t(r,z,D){return F(r,D),(D,te)=>g(r,z,[D,te])}function pt(r,z){if(!gt(r))throw new TypeError(`${z} is not a WritableStream.`)}Object.defineProperties(po.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),o(po.prototype.cancel,"cancel"),o(po.prototype.read,"read"),o(po.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(po.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let yo=function(){return _createClass((function WritableStream(r={},z={}){_classCallCheck(this,WritableStream),void 0===r?r=null:I(r,"First parameter");const D=dt(z,"Second parameter"),te=function(r,z){L(r,z);const D=null==r?void 0:r.abort,te=null==r?void 0:r.close,re=null==r?void 0:r.start,oe=null==r?void 0:r.type,de=null==r?void 0:r.write;return{abort:void 0===D?void 0:bt(D,r,`${z} has member 'abort' that`),close:void 0===te?void 0:ht(te,r,`${z} has member 'close' that`),start:void 0===re?void 0:mt(re,r,`${z} has member 'start' that`),write:void 0===de?void 0:_t(de,r,`${z} has member 'write' that`),type:oe}}(r,"First parameter");if(St(this),void 0!==te.type)throw new RangeError("Invalid type is specified");const re=ct(D);!function(r,z,D,te){const re=Object.create(So.prototype);let oe,de,me,ye;oe=void 0!==z.start?()=>z.start(re):()=>{},de=void 0!==z.write?r=>z.write(r,re):()=>c(void 0),me=void 0!==z.close?()=>z.close():()=>c(void 0),ye=void 0!==z.abort?r=>z.abort(r):()=>c(void 0),Ft(r,re,oe,de,me,ye,D,te)}(this,te,ut(D,1),re)}),[{key:"locked",get:function(){if(!gt(this))throw Nt("locked");return vt(this)}},{key:"abort",value:function abort(r=void 0){return gt(this)?vt(this)?d(new TypeError("Cannot abort a stream that already has a writer")):wt(this,r):d(Nt("abort"))}},{key:"close",value:function close(){return gt(this)?vt(this)?d(new TypeError("Cannot close a stream that already has a writer")):qt(this)?d(new TypeError("Cannot close an already-closing stream")):Rt(this):d(Nt("close"))}},{key:"getWriter",value:function getWriter(){if(!gt(this))throw Nt("getWriter");return yt(this)}}])}();function yt(r){return new vo(r)}function St(r){r._state="writable",r._storedError=void 0,r._writer=void 0,r._writableStreamController=void 0,r._writeRequests=new de,r._inFlightWriteRequest=void 0,r._closeRequest=void 0,r._inFlightCloseRequest=void 0,r._pendingAbortRequest=void 0,r._backpressure=!1}function gt(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_writableStreamController")&&r instanceof yo}function vt(r){return void 0!==r._writer}function wt(r,z){var D;if("closed"===r._state||"errored"===r._state)return c(void 0);r._writableStreamController._abortReason=z,null===(D=r._writableStreamController._abortController)||void 0===D||D.abort(z);const te=r._state;if("closed"===te||"errored"===te)return c(void 0);if(void 0!==r._pendingAbortRequest)return r._pendingAbortRequest._promise;let re=!1;"erroring"===te&&(re=!0,z=void 0);const oe=u(((D,te)=>{r._pendingAbortRequest={_promise:void 0,_resolve:D,_reject:te,_reason:z,_wasAlreadyErroring:re}}));return r._pendingAbortRequest._promise=oe,re||Ct(r,z),oe}function Rt(r){const z=r._state;if("closed"===z||"errored"===z)return d(new TypeError(`The stream (in ${z} state) is not in the writable state and cannot be closed`));const D=u(((z,D)=>{const te={_resolve:z,_reject:D};r._closeRequest=te})),te=r._writer;var re;return void 0!==te&&r._backpressure&&"writable"===z&&or(te),ve(re=r._writableStreamController,go,0),Mt(re),D}function Tt(r,z){"writable"!==r._state?Pt(r):Ct(r,z)}function Ct(r,z){const D=r._writableStreamController;r._state="erroring",r._storedError=z;const te=r._writer;void 0!==te&&kt(te,z),!function(r){return void 0!==r._inFlightWriteRequest||void 0!==r._inFlightCloseRequest}(r)&&D._started&&Pt(r)}function Pt(r){r._state="errored",r._writableStreamController[ye]();const z=r._storedError;if(r._writeRequests.forEach((r=>{r._reject(z)})),r._writeRequests=new de,void 0===r._pendingAbortRequest)return void Et(r);const D=r._pendingAbortRequest;if(r._pendingAbortRequest=void 0,D._wasAlreadyErroring)return D._reject(z),void Et(r);b(r._writableStreamController[me](D._reason),(()=>(D._resolve(),Et(r),null)),(z=>(D._reject(z),Et(r),null)))}function qt(r){return void 0!==r._closeRequest||void 0!==r._inFlightCloseRequest}function Et(r){void 0!==r._closeRequest&&(r._closeRequest._reject(r._storedError),r._closeRequest=void 0);const z=r._writer;void 0!==z&&Jt(z,r._storedError)}function Wt(r,z){const D=r._writer;void 0!==D&&z!==r._backpressure&&(z?function(r){Zt(r)}(D):or(D)),r._backpressure=z}Object.defineProperties(yo.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),o(yo.prototype.abort,"abort"),o(yo.prototype.close,"close"),o(yo.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(yo.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});let vo=function(){return _createClass((function WritableStreamDefaultWriter(r){if(_classCallCheck(this,WritableStreamDefaultWriter),$(r,1,"WritableStreamDefaultWriter"),pt(r,"First parameter"),vt(r))throw new TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=r,r._writer=this;const z=r._state;if("writable"===z)!qt(r)&&r._backpressure?Zt(this):tr(this),Gt(this);else if("erroring"===z)er(this,r._storedError),Gt(this);else if("closed"===z)tr(this),Gt(this),Kt(this);else{const z=r._storedError;er(this,z),Xt(this,z)}}),[{key:"closed",get:function(){return Bt(this)?this._closedPromise:d(Vt("closed"))}},{key:"desiredSize",get:function(){if(!Bt(this))throw Vt("desiredSize");if(void 0===this._ownerWritableStream)throw Ut("desiredSize");return function(r){const z=r._ownerWritableStream,D=z._state;return"errored"===D||"erroring"===D?null:"closed"===D?0:$t(z._writableStreamController)}(this)}},{key:"ready",get:function(){return Bt(this)?this._readyPromise:d(Vt("ready"))}},{key:"abort",value:function abort(r=void 0){return Bt(this)?void 0===this._ownerWritableStream?d(Ut("abort")):function(r,z){return wt(r._ownerWritableStream,z)}(this,r):d(Vt("abort"))}},{key:"close",value:function close(){if(!Bt(this))return d(Vt("close"));const r=this._ownerWritableStream;return void 0===r?d(Ut("close")):qt(r)?d(new TypeError("Cannot close an already-closing stream")):Ot(this)}},{key:"releaseLock",value:function releaseLock(){if(!Bt(this))throw Vt("releaseLock");void 0!==this._ownerWritableStream&&At(this)}},{key:"write",value:function write(r=void 0){return Bt(this)?void 0===this._ownerWritableStream?d(Ut("write to")):zt(this,r):d(Vt("write"))}}])}();function Bt(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_ownerWritableStream")&&r instanceof vo}function Ot(r){return Rt(r._ownerWritableStream)}function kt(r,z){"pending"===r._readyPromiseState?rr(r,z):function(r,z){er(r,z)}(r,z)}function At(r){const z=r._ownerWritableStream,D=new TypeError("Writer was released and can no longer be used to monitor the stream's closedness");kt(r,D),function jt(r,z){"pending"===r._closedPromiseState?Jt(r,z):function(r,z){Xt(r,z)}(r,z)}(r,D),z._writer=void 0,r._ownerWritableStream=void 0}function zt(r,z){const D=r._ownerWritableStream,te=D._writableStreamController,re=function(r,z){if(void 0===r._strategySizeAlgorithm)return 1;try{return r._strategySizeAlgorithm(z)}catch(z){return Yt(r,z),1}}(te,z);if(D!==r._ownerWritableStream)return d(Ut("write to"));const oe=D._state;if("errored"===oe)return d(D._storedError);if(qt(D)||"closed"===oe)return d(new TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===oe)return d(D._storedError);const de=function(r){return u(((z,D)=>{const te={_resolve:z,_reject:D};r._writeRequests.push(te)}))}(D);return function(r,z,D){try{ve(r,z,D)}catch(z){return void Yt(r,z)}const te=r._controlledWritableStream;qt(te)||"writable"!==te._state||Wt(te,Qt(r)),Mt(r)}(te,z,re),de}Object.defineProperties(vo.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),o(vo.prototype.abort,"abort"),o(vo.prototype.close,"close"),o(vo.prototype.releaseLock,"releaseLock"),o(vo.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(vo.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});const go={};let So=function(){return _createClass((function WritableStreamDefaultController(){throw _classCallCheck(this,WritableStreamDefaultController),new TypeError("Illegal constructor")}),[{key:"abortReason",get:function(){if(!Lt(this))throw Ht("abortReason");return this._abortReason}},{key:"signal",get:function(){if(!Lt(this))throw Ht("signal");if(void 0===this._abortController)throw new TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}},{key:"error",value:function error(r=void 0){if(!Lt(this))throw Ht("error");"writable"===this._controlledWritableStream._state&&xt(this,r)}},{key:me,value:function(r){const z=this._abortAlgorithm(r);return It(this),z}},{key:ye,value:function(){we(this)}}])}();function Lt(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_controlledWritableStream")&&r instanceof So}function Ft(r,z,D,te,re,oe,de,me){z._controlledWritableStream=r,r._writableStreamController=z,z._queue=void 0,z._queueTotalSize=void 0,we(z),z._abortReason=void 0,z._abortController=function(){if("function"==typeof AbortController)return new AbortController}(),z._started=!1,z._strategySizeAlgorithm=me,z._strategyHWM=de,z._writeAlgorithm=te,z._closeAlgorithm=re,z._abortAlgorithm=oe;const ye=Qt(z);Wt(r,ye),b(c(D()),(()=>(z._started=!0,Mt(z),null)),(D=>(z._started=!0,Tt(r,D),null)))}function It(r){r._writeAlgorithm=void 0,r._closeAlgorithm=void 0,r._abortAlgorithm=void 0,r._strategySizeAlgorithm=void 0}function $t(r){return r._strategyHWM-r._queueTotalSize}function Mt(r){const z=r._controlledWritableStream;if(!r._started)return;if(void 0!==z._inFlightWriteRequest)return;if("erroring"===z._state)return void Pt(z);if(0===r._queue.length)return;const D=r._queue.peek().value;D===go?function(r){const z=r._controlledWritableStream;(function(r){r._inFlightCloseRequest=r._closeRequest,r._closeRequest=void 0})(z),ge(r);const D=r._closeAlgorithm();It(r),b(D,(()=>(function(r){r._inFlightCloseRequest._resolve(void 0),r._inFlightCloseRequest=void 0,"erroring"===r._state&&(r._storedError=void 0,void 0!==r._pendingAbortRequest&&(r._pendingAbortRequest._resolve(),r._pendingAbortRequest=void 0)),r._state="closed";const z=r._writer;void 0!==z&&Kt(z)}(z),null)),(r=>(function(r,z){r._inFlightCloseRequest._reject(z),r._inFlightCloseRequest=void 0,void 0!==r._pendingAbortRequest&&(r._pendingAbortRequest._reject(z),r._pendingAbortRequest=void 0),Tt(r,z)}(z,r),null)))}(r):function(r,z){const D=r._controlledWritableStream;!function(r){r._inFlightWriteRequest=r._writeRequests.shift()}(D);b(r._writeAlgorithm(z),(()=>{!function(r){r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0}(D);const z=D._state;if(ge(r),!qt(D)&&"writable"===z){const z=Qt(r);Wt(D,z)}return Mt(r),null}),(z=>("writable"===D._state&&It(r),function(r,z){r._inFlightWriteRequest._reject(z),r._inFlightWriteRequest=void 0,Tt(r,z)}(D,z),null)))}(r,D)}function Yt(r,z){"writable"===r._controlledWritableStream._state&&xt(r,z)}function Qt(r){return $t(r)<=0}function xt(r,z){const D=r._controlledWritableStream;It(r),Ct(D,z)}function Nt(r){return new TypeError(`WritableStream.prototype.${r} can only be used on a WritableStream`)}function Ht(r){return new TypeError(`WritableStreamDefaultController.prototype.${r} can only be used on a WritableStreamDefaultController`)}function Vt(r){return new TypeError(`WritableStreamDefaultWriter.prototype.${r} can only be used on a WritableStreamDefaultWriter`)}function Ut(r){return new TypeError("Cannot "+r+" a stream using a released writer")}function Gt(r){r._closedPromise=u(((z,D)=>{r._closedPromise_resolve=z,r._closedPromise_reject=D,r._closedPromiseState="pending"}))}function Xt(r,z){Gt(r),Jt(r,z)}function Jt(r,z){void 0!==r._closedPromise_reject&&(p(r._closedPromise),r._closedPromise_reject(z),r._closedPromise_resolve=void 0,r._closedPromise_reject=void 0,r._closedPromiseState="rejected")}function Kt(r){void 0!==r._closedPromise_resolve&&(r._closedPromise_resolve(void 0),r._closedPromise_resolve=void 0,r._closedPromise_reject=void 0,r._closedPromiseState="resolved")}function Zt(r){r._readyPromise=u(((z,D)=>{r._readyPromise_resolve=z,r._readyPromise_reject=D})),r._readyPromiseState="pending"}function er(r,z){Zt(r),rr(r,z)}function tr(r){Zt(r),or(r)}function rr(r,z){void 0!==r._readyPromise_reject&&(p(r._readyPromise),r._readyPromise_reject(z),r._readyPromise_resolve=void 0,r._readyPromise_reject=void 0,r._readyPromiseState="rejected")}function or(r){void 0!==r._readyPromise_resolve&&(r._readyPromise_resolve(void 0),r._readyPromise_resolve=void 0,r._readyPromise_reject=void 0,r._readyPromiseState="fulfilled")}Object.defineProperties(So.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(So.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});const wo="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,Ro=function(){const r=null==wo?void 0:wo.DOMException;return function(r){if("function"!=typeof r&&"object"!=typeof r)return!1;if("DOMException"!==r.name)return!1;try{return new r,!0}catch(r){return!1}}(r)?r:void 0}()||function(){const e=function(r,z){this.message=r||"",this.name=z||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return o(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function ir(r,z,D,te,re,oe){const de=H(r),me=yt(z);r._disturbed=!0;let ye=!1,Dt=c(void 0);return u(((nr,ar)=>{let no;if(void 0!==oe){if(no=()=>{const D=void 0!==oe.reason?oe.reason:new Ro("Aborted","AbortError"),de=[];te||de.push((()=>"writable"===z._state?wt(z,D):c(void 0))),re||de.push((()=>"readable"===r._state?Br(r,D):c(void 0))),q((()=>Promise.all(de.map((r=>r())))),!0,D)},oe.aborted)return void no();oe.addEventListener("abort",no)}var io,ao,lo;if(P(r,de._closedPromise,(r=>(te?E(!0,r):q((()=>wt(z,r)),!0,r),null))),P(z,me._closedPromise,(z=>(re?E(!0,z):q((()=>Br(r,z)),!0,z),null))),io=r,ao=de._closedPromise,lo=()=>(D?E():q((()=>function(r){const z=r._ownerWritableStream,D=z._state;return qt(z)||"closed"===D?c(void 0):"errored"===D?d(z._storedError):Ot(r)}(me))),null),"closed"===io._state?lo():h(ao,lo),qt(z)||"closed"===z._state){const z=new TypeError("the destination writable stream closed before all data could be piped to it");re?E(!0,z):q((()=>Br(r,z)),!0,z)}function C(){const r=Dt;return f(Dt,(()=>r!==Dt?C():void 0))}function P(r,z,D){"errored"===r._state?D(r._storedError):m(z,D)}function q(r,D,te){function n(){return b(r(),(()=>B(D,te)),(r=>B(!0,r))),null}ye||(ye=!0,"writable"!==z._state||qt(z)?n():h(C(),n))}function E(r,D){ye||(ye=!0,"writable"!==z._state||qt(z)?B(r,D):h(C(),(()=>B(r,D))))}function B(r,z){return At(me),W(de),void 0!==oe&&oe.removeEventListener("abort",no),r?ar(z):nr(void 0),null}p(u(((r,z)=>{!function o(D){D?r():f(ye?c(!0):f(me._readyPromise,(()=>u(((r,z)=>{K(de,{_chunkSteps:z=>{Dt=f(zt(me,z),void 0,e),r(!1)},_closeSteps:()=>r(!0),_errorSteps:z})})))),o,z)}(!1)})))}))}let To=function(){return _createClass((function ReadableStreamDefaultController(){throw _classCallCheck(this,ReadableStreamDefaultController),new TypeError("Illegal constructor")}),[{key:"desiredSize",get:function(){if(!lr(this))throw pr("desiredSize");return hr(this)}},{key:"close",value:function close(){if(!lr(this))throw pr("close");if(!mr(this))throw new TypeError("The stream is not in a state that permits close");dr(this)}},{key:"enqueue",value:function enqueue(r=void 0){if(!lr(this))throw pr("enqueue");if(!mr(this))throw new TypeError("The stream is not in a state that permits enqueue");return fr(this,r)}},{key:"error",value:function error(r=void 0){if(!lr(this))throw pr("error");br(this,r)}},{key:Dt,value:function(r){we(this);const z=this._cancelAlgorithm(r);return cr(this),z}},{key:nr,value:function(r){const z=this._controlledReadableStream;if(this._queue.length>0){const D=ge(this);this._closeRequested&&0===this._queue.length?(cr(this),Or(z)):sr(this),r._chunkSteps(D)}else V(z,r),sr(this)}},{key:ar,value:function(){}}])}();function lr(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_controlledReadableStream")&&r instanceof To}function sr(r){ur(r)&&(r._pulling?r._pullAgain=!0:(r._pulling=!0,b(r._pullAlgorithm(),(()=>(r._pulling=!1,r._pullAgain&&(r._pullAgain=!1,sr(r)),null)),(z=>(br(r,z),null)))))}function ur(r){const z=r._controlledReadableStream;return!!mr(r)&&(!!r._started&&(!!(Wr(z)&&G(z)>0)||hr(r)>0))}function cr(r){r._pullAlgorithm=void 0,r._cancelAlgorithm=void 0,r._strategySizeAlgorithm=void 0}function dr(r){if(!mr(r))return;const z=r._controlledReadableStream;r._closeRequested=!0,0===r._queue.length&&(cr(r),Or(z))}function fr(r,z){if(!mr(r))return;const D=r._controlledReadableStream;if(Wr(D)&&G(D)>0)U(D,z,!1);else{let D;try{D=r._strategySizeAlgorithm(z)}catch(z){throw br(r,z),z}try{ve(r,z,D)}catch(z){throw br(r,z),z}}sr(r)}function br(r,z){const D=r._controlledReadableStream;"readable"===D._state&&(we(r),cr(r),jr(D,z))}function hr(r){const z=r._controlledReadableStream._state;return"errored"===z?null:"closed"===z?0:r._strategyHWM-r._queueTotalSize}function mr(r){const z=r._controlledReadableStream._state;return!r._closeRequested&&"readable"===z}function _r(r,z,D,te,re,oe,de){z._controlledReadableStream=r,z._queue=void 0,z._queueTotalSize=void 0,we(z),z._started=!1,z._closeRequested=!1,z._pullAgain=!1,z._pulling=!1,z._strategySizeAlgorithm=de,z._strategyHWM=oe,z._pullAlgorithm=te,z._cancelAlgorithm=re,r._readableStreamController=z,b(c(D()),(()=>(z._started=!0,sr(z),null)),(r=>(br(z,r),null)))}function pr(r){return new TypeError(`ReadableStreamDefaultController.prototype.${r} can only be used on a ReadableStreamDefaultController`)}function Sr(r){return t(z=r)&&void 0!==z.getReader?function(r){let z;return z=Cr(e,(function n(){let D;try{D=r.read()}catch(D){return d(D)}return _(D,(r=>{if(!t(r))throw new TypeError("The promise returned by the reader.read() method must fulfill with an object");if(r.done)dr(z._readableStreamController);else{const D=r.value;fr(z._readableStreamController,D)}}))}),(function a(z){try{return c(r.cancel(z))}catch(z){return d(z)}}),0),z}(r.getReader()):function(r){let z;const D=fe(r,"async");return z=Cr(e,(function a(){let r;try{r=be(D)}catch(r){return d(r)}return _(c(r),(r=>{if(!t(r))throw new TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(r.done)dr(z._readableStreamController);else{const D=r.value;fr(z._readableStreamController,D)}}))}),(function i(r){const z=D.iterator;let te;try{te=ue(z,"return")}catch(r){return d(r)}return void 0===te?c(void 0):_(g(te,z,[r]),(r=>{if(!t(r))throw new TypeError("The promise returned by the iterator.return() method must fulfill with an object")}))}),0),z}(r);var z}function gr(r,z,D){return F(r,D),D=>g(r,z,[D])}function vr(r,z,D){return F(r,D),D=>g(r,z,[D])}function wr(r,z,D){return F(r,D),D=>S(r,z,[D])}function Rr(r,z){if("bytes"!=(r=`${r}`))throw new TypeError(`${z} '${r}' is not a valid enumeration value for ReadableStreamType`);return r}function Tr(r,z){L(r,z);const D=null==r?void 0:r.preventAbort,te=null==r?void 0:r.preventCancel,re=null==r?void 0:r.preventClose,oe=null==r?void 0:r.signal;return void 0!==oe&&function(r,z){if(!function(r){if("object"!=typeof r||null===r)return!1;try{return"boolean"==typeof r.aborted}catch(r){return!1}}(r))throw new TypeError(`${z} is not an AbortSignal.`)}(oe,`${z} has member 'signal' that`),{preventAbort:Boolean(D),preventCancel:Boolean(te),preventClose:Boolean(re),signal:oe}}Object.defineProperties(To.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),o(To.prototype.close,"close"),o(To.prototype.enqueue,"enqueue"),o(To.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(To.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});let Co=function(){return _createClass((function ReadableStream(r={},z={}){_classCallCheck(this,ReadableStream),void 0===r?r=null:I(r,"First parameter");const D=dt(z,"Second parameter"),te=function(r,z){L(r,z);const D=r,te=null==D?void 0:D.autoAllocateChunkSize,re=null==D?void 0:D.cancel,oe=null==D?void 0:D.pull,de=null==D?void 0:D.start,me=null==D?void 0:D.type;return{autoAllocateChunkSize:void 0===te?void 0:x(te,`${z} has member 'autoAllocateChunkSize' that`),cancel:void 0===re?void 0:gr(re,D,`${z} has member 'cancel' that`),pull:void 0===oe?void 0:vr(oe,D,`${z} has member 'pull' that`),start:void 0===de?void 0:wr(de,D,`${z} has member 'start' that`),type:void 0===me?void 0:Rr(me,`${z} has member 'type' that`)}}(r,"First parameter");if(qr(this),"bytes"===te.type){if(void 0!==D.size)throw new RangeError("The strategy for a byte stream cannot have a size function");!function(r,z,D){const te=Object.create(mo.prototype);let re,oe,de;re=void 0!==z.start?()=>z.start(te):()=>{},oe=void 0!==z.pull?()=>z.pull(te):()=>c(void 0),de=void 0!==z.cancel?r=>z.cancel(r):()=>c(void 0);const me=z.autoAllocateChunkSize;if(0===me)throw new TypeError("autoAllocateChunkSize must be greater than 0");Je(r,te,re,oe,de,D,me)}(this,te,ut(D,0))}else{const r=ct(D);!function(r,z,D,te){const re=Object.create(To.prototype);let oe,de,me;oe=void 0!==z.start?()=>z.start(re):()=>{},de=void 0!==z.pull?()=>z.pull(re):()=>c(void 0),me=void 0!==z.cancel?r=>z.cancel(r):()=>c(void 0),_r(r,re,oe,de,me,D,te)}(this,te,ut(D,1),r)}}),[{key:"locked",get:function(){if(!Er(this))throw kr("locked");return Wr(this)}},{key:"cancel",value:function cancel(r=void 0){return Er(this)?Wr(this)?d(new TypeError("Cannot cancel a stream that already has a reader")):Br(this,r):d(kr("cancel"))}},{key:"getReader",value:function getReader(r=void 0){if(!Er(this))throw kr("getReader");return void 0===function(r,z){L(r,z);const D=null==r?void 0:r.mode;return{mode:void 0===D?void 0:et(D,`${z} has member 'mode' that`)}}(r,"First parameter").mode?H(this):tt(this)}},{key:"pipeThrough",value:function pipeThrough(r,z={}){if(!Er(this))throw kr("pipeThrough");$(r,1,"pipeThrough");const D=function(r,z){L(r,z);const D=null==r?void 0:r.readable;M(D,"readable","ReadableWritablePair"),N(D,`${z} has member 'readable' that`);const te=null==r?void 0:r.writable;return M(te,"writable","ReadableWritablePair"),pt(te,`${z} has member 'writable' that`),{readable:D,writable:te}}(r,"First parameter"),te=Tr(z,"Second parameter");if(Wr(this))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(vt(D.writable))throw new TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return p(ir(this,D.writable,te.preventClose,te.preventAbort,te.preventCancel,te.signal)),D.readable}},{key:"pipeTo",value:function pipeTo(r,z={}){if(!Er(this))return d(kr("pipeTo"));if(void 0===r)return d("Parameter 1 is required in 'pipeTo'.");if(!gt(r))return d(new TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));let D;try{D=Tr(z,"Second parameter")}catch(r){return d(r)}return Wr(this)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):vt(r)?d(new TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):ir(this,r,D.preventClose,D.preventAbort,D.preventCancel,D.signal)}},{key:"tee",value:function tee(){if(!Er(this))throw kr("tee");return ne(function yr(r,z){return Te(r._readableStreamController)?function(r){let z,D,te,re,oe,de=H(r),me=!1,ye=!1,Dt=!1,nr=!1,ar=!1;const no=u((r=>{oe=r}));function _(r){m(r._closedPromise,(z=>(r!==de||(Ne(te._readableStreamController,z),Ne(re._readableStreamController,z),nr&&ar||oe(void 0)),null)))}function p(){at(de)&&(W(de),de=H(r),_(de)),K(de,{_chunkSteps:z=>{y((()=>{ye=!1,Dt=!1;const D=z;let de=z;if(!nr&&!ar)try{de=Se(z)}catch(z){return Ne(te._readableStreamController,z),Ne(re._readableStreamController,z),void oe(Br(r,z))}nr||xe(te._readableStreamController,D),ar||xe(re._readableStreamController,de),me=!1,ye?g():Dt&&v()}))},_closeSteps:()=>{me=!1,nr||Qe(te._readableStreamController),ar||Qe(re._readableStreamController),te._readableStreamController._pendingPullIntos.length>0&&Ge(te._readableStreamController,0),re._readableStreamController._pendingPullIntos.length>0&&Ge(re._readableStreamController,0),nr&&ar||oe(void 0)},_errorSteps:()=>{me=!1}})}function S(z,D){J(de)&&(W(de),de=tt(r),_(de));const no=D?re:te,io=D?te:re;it(de,z,1,{_chunkSteps:z=>{y((()=>{ye=!1,Dt=!1;const te=D?ar:nr;if(D?nr:ar)te||Xe(no._readableStreamController,z);else{let D;try{D=Se(z)}catch(z){return Ne(no._readableStreamController,z),Ne(io._readableStreamController,z),void oe(Br(r,z))}te||Xe(no._readableStreamController,z),xe(io._readableStreamController,D)}me=!1,ye?g():Dt&&v()}))},_closeSteps:r=>{me=!1;const z=D?ar:nr,te=D?nr:ar;z||Qe(no._readableStreamController),te||Qe(io._readableStreamController),void 0!==r&&(z||Xe(no._readableStreamController,r),!te&&io._readableStreamController._pendingPullIntos.length>0&&Ge(io._readableStreamController,0)),z&&te||oe(void 0)},_errorSteps:()=>{me=!1}})}function g(){if(me)return ye=!0,c(void 0);me=!0;const r=Ve(te._readableStreamController);return null===r?p():S(r._view,!1),c(void 0)}function v(){if(me)return Dt=!0,c(void 0);me=!0;const r=Ve(re._readableStreamController);return null===r?p():S(r._view,!0),c(void 0)}function T(){}return te=Pr(T,g,(function w(te){if(nr=!0,z=te,ar){const te=ne([z,D]),re=Br(r,te);oe(re)}return no})),re=Pr(T,v,(function R(te){if(ar=!0,D=te,nr){const te=ne([z,D]),re=Br(r,te);oe(re)}return no})),_(de),[te,re]}(r):function(r,z){const D=H(r);let te,re,oe,de,me,ye=!1,Dt=!1,nr=!1,ar=!1;const no=u((r=>{me=r}));function _(){return ye?(Dt=!0,c(void 0)):(ye=!0,K(D,{_chunkSteps:r=>{y((()=>{Dt=!1;const z=r,D=r;nr||fr(oe._readableStreamController,z),ar||fr(de._readableStreamController,D),ye=!1,Dt&&_()}))},_closeSteps:()=>{ye=!1,nr||dr(oe._readableStreamController),ar||dr(de._readableStreamController),nr&&ar||me(void 0)},_errorSteps:()=>{ye=!1}}),c(void 0))}function g(){}return oe=Cr(g,_,(function p(z){if(nr=!0,te=z,ar){const z=ne([te,re]),D=Br(r,z);me(D)}return no})),de=Cr(g,_,(function S(z){if(ar=!0,re=z,nr){const z=ne([te,re]),D=Br(r,z);me(D)}return no})),m(D._closedPromise,(r=>(br(oe._readableStreamController,r),br(de._readableStreamController,r),nr&&ar||me(void 0),null))),[oe,de]}(r)}(this))}},{key:"values",value:function values(r=void 0){if(!Er(this))throw kr("values");return function(r,z){const D=H(r),te=new fo(D,z),re=Object.create(ho);return re._asyncIteratorImpl=te,re}(this,function(r,z){L(r,"First parameter");const D=null==r?void 0:r.preventCancel;return{preventCancel:Boolean(D)}}(r).preventCancel)}},{key:co,value:function(r){return this.values(r)}}],[{key:"from",value:function from(r){return Sr(r)}}])}();function Cr(r,z,D,te=1,re=(()=>1)){const oe=Object.create(Co.prototype);return qr(oe),_r(oe,Object.create(To.prototype),r,z,D,te,re),oe}function Pr(r,z,D){const te=Object.create(Co.prototype);return qr(te),Je(te,Object.create(mo.prototype),r,z,D,0,void 0),te}function qr(r){r._state="readable",r._reader=void 0,r._storedError=void 0,r._disturbed=!1}function Er(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_readableStreamController")&&r instanceof Co}function Wr(r){return void 0!==r._reader}function Br(r,z){if(r._disturbed=!0,"closed"===r._state)return c(void 0);if("errored"===r._state)return d(r._storedError);Or(r);const D=r._reader;if(void 0!==D&&at(D)){const r=D._readIntoRequests;D._readIntoRequests=new de,r.forEach((r=>{r._closeSteps(void 0)}))}return _(r._readableStreamController[Dt](z),e)}function Or(r){r._state="closed";const z=r._reader;if(void 0!==z&&(A(z),J(z))){const r=z._readRequests;z._readRequests=new de,r.forEach((r=>{r._closeSteps()}))}}function jr(r,z){r._state="errored",r._storedError=z;const D=r._reader;void 0!==D&&(k(D,z),J(D)?Z(D,z):lt(D,z))}function kr(r){return new TypeError(`ReadableStream.prototype.${r} can only be used on a ReadableStream`)}function Ar(r,z){L(r,z);const D=null==r?void 0:r.highWaterMark;return M(D,"highWaterMark","QueuingStrategyInit"),{highWaterMark:Y(D)}}Object.defineProperties(Co,{from:{enumerable:!0}}),Object.defineProperties(Co.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),o(Co.from,"from"),o(Co.prototype.cancel,"cancel"),o(Co.prototype.getReader,"getReader"),o(Co.prototype.pipeThrough,"pipeThrough"),o(Co.prototype.pipeTo,"pipeTo"),o(Co.prototype.tee,"tee"),o(Co.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Co.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(Co.prototype,co,{value:Co.prototype.values,writable:!0,configurable:!0});const zr=r=>r.byteLength;o(zr,"size");let Po=function(){return _createClass((function ByteLengthQueuingStrategy(r){_classCallCheck(this,ByteLengthQueuingStrategy),$(r,1,"ByteLengthQueuingStrategy"),r=Ar(r,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=r.highWaterMark}),[{key:"highWaterMark",get:function(){if(!Lr(this))throw Dr("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}},{key:"size",get:function(){if(!Lr(this))throw Dr("size");return zr}}])}();function Dr(r){return new TypeError(`ByteLengthQueuingStrategy.prototype.${r} can only be used on a ByteLengthQueuingStrategy`)}function Lr(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_byteLengthQueuingStrategyHighWaterMark")&&r instanceof Po}Object.defineProperties(Po.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Po.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});const Fr=()=>1;o(Fr,"size");let ko=function(){return _createClass((function CountQueuingStrategy(r){_classCallCheck(this,CountQueuingStrategy),$(r,1,"CountQueuingStrategy"),r=Ar(r,"First parameter"),this._countQueuingStrategyHighWaterMark=r.highWaterMark}),[{key:"highWaterMark",get:function(){if(!$r(this))throw Ir("highWaterMark");return this._countQueuingStrategyHighWaterMark}},{key:"size",get:function(){if(!$r(this))throw Ir("size");return Fr}}])}();function Ir(r){return new TypeError(`CountQueuingStrategy.prototype.${r} can only be used on a CountQueuingStrategy`)}function $r(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_countQueuingStrategyHighWaterMark")&&r instanceof ko}function Mr(r,z,D){return F(r,D),D=>g(r,z,[D])}function Yr(r,z,D){return F(r,D),D=>S(r,z,[D])}function Qr(r,z,D){return F(r,D),(D,te)=>g(r,z,[D,te])}function xr(r,z,D){return F(r,D),D=>g(r,z,[D])}Object.defineProperties(ko.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ko.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});let qo=function(){return _createClass((function TransformStream(r={},z={},D={}){_classCallCheck(this,TransformStream),void 0===r&&(r=null);const te=dt(z,"Second parameter"),re=dt(D,"Third parameter"),oe=function(r,z){L(r,z);const D=null==r?void 0:r.cancel,te=null==r?void 0:r.flush,re=null==r?void 0:r.readableType,oe=null==r?void 0:r.start,de=null==r?void 0:r.transform,me=null==r?void 0:r.writableType;return{cancel:void 0===D?void 0:xr(D,r,`${z} has member 'cancel' that`),flush:void 0===te?void 0:Mr(te,r,`${z} has member 'flush' that`),readableType:re,start:void 0===oe?void 0:Yr(oe,r,`${z} has member 'start' that`),transform:void 0===de?void 0:Qr(de,r,`${z} has member 'transform' that`),writableType:me}}(r,"First parameter");if(void 0!==oe.readableType)throw new RangeError("Invalid readableType specified");if(void 0!==oe.writableType)throw new RangeError("Invalid writableType specified");const de=ut(re,0),me=ct(re),ye=ut(te,1),Dt=ct(te);let nr;!function(r,z,D,te,re,oe){function i(){return z}r._writable=function(r,z,D,te,re=1,oe=(()=>1)){const de=Object.create(yo.prototype);return St(de),Ft(de,Object.create(So.prototype),r,z,D,te,re,oe),de}(i,(function l(z){return function(r,z){const D=r._transformStreamController;return r._backpressure?_(r._backpressureChangePromise,(()=>{const te=r._writable;if("erroring"===te._state)throw te._storedError;return Zr(D,z)})):Zr(D,z)}(r,z)}),(function c(){return function(r){const z=r._transformStreamController;if(void 0!==z._finishPromise)return z._finishPromise;const D=r._readable;z._finishPromise=u(((r,D)=>{z._finishPromise_resolve=r,z._finishPromise_reject=D}));const te=z._flushAlgorithm();return Jr(z),b(te,(()=>("errored"===D._state?ro(z,D._storedError):(dr(D._readableStreamController),to(z)),null)),(r=>(br(D._readableStreamController,r),ro(z,r),null))),z._finishPromise}(r)}),(function s(z){return function(r,z){const D=r._transformStreamController;if(void 0!==D._finishPromise)return D._finishPromise;const te=r._readable;D._finishPromise=u(((r,z)=>{D._finishPromise_resolve=r,D._finishPromise_reject=z}));const re=D._cancelAlgorithm(z);return Jr(D),b(re,(()=>("errored"===te._state?ro(D,te._storedError):(br(te._readableStreamController,z),to(D)),null)),(r=>(br(te._readableStreamController,r),ro(D,r),null))),D._finishPromise}(r,z)}),D,te),r._readable=Cr(i,(function d(){return function(r){return Gr(r,!1),r._backpressureChangePromise}(r)}),(function f(z){return function(r,z){const D=r._transformStreamController;if(void 0!==D._finishPromise)return D._finishPromise;const te=r._writable;D._finishPromise=u(((r,z)=>{D._finishPromise_resolve=r,D._finishPromise_reject=z}));const re=D._cancelAlgorithm(z);return Jr(D),b(re,(()=>("errored"===te._state?ro(D,te._storedError):(Yt(te._writableStreamController,z),Ur(r),to(D)),null)),(z=>(Yt(te._writableStreamController,z),Ur(r),ro(D,z),null))),D._finishPromise}(r,z)}),re,oe),r._backpressure=void 0,r._backpressureChangePromise=void 0,r._backpressureChangePromise_resolve=void 0,Gr(r,!0),r._transformStreamController=void 0}(this,u((r=>{nr=r})),ye,Dt,de,me),function(r,z){const D=Object.create(Eo.prototype);let te,re,oe;te=void 0!==z.transform?r=>z.transform(r,D):r=>{try{return Kr(D,r),c(void 0)}catch(r){return d(r)}},re=void 0!==z.flush?()=>z.flush(D):()=>c(void 0),oe=void 0!==z.cancel?r=>z.cancel(r):()=>c(void 0),function(r,z,D,te,re){z._controlledTransformStream=r,r._transformStreamController=z,z._transformAlgorithm=D,z._flushAlgorithm=te,z._cancelAlgorithm=re,z._finishPromise=void 0,z._finishPromise_resolve=void 0,z._finishPromise_reject=void 0}(r,D,te,re,oe)}(this,oe),void 0!==oe.start?nr(oe.start(this._transformStreamController)):nr(void 0)}),[{key:"readable",get:function(){if(!Nr(this))throw oo("readable");return this._readable}},{key:"writable",get:function(){if(!Nr(this))throw oo("writable");return this._writable}}])}();function Nr(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_transformStreamController")&&r instanceof qo}function Hr(r,z){br(r._readable._readableStreamController,z),Vr(r,z)}function Vr(r,z){Jr(r._transformStreamController),Yt(r._writable._writableStreamController,z),Ur(r)}function Ur(r){r._backpressure&&Gr(r,!1)}function Gr(r,z){void 0!==r._backpressureChangePromise&&r._backpressureChangePromise_resolve(),r._backpressureChangePromise=u((z=>{r._backpressureChangePromise_resolve=z})),r._backpressure=z}Object.defineProperties(qo.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(qo.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});let Eo=function(){return _createClass((function TransformStreamDefaultController(){throw _classCallCheck(this,TransformStreamDefaultController),new TypeError("Illegal constructor")}),[{key:"desiredSize",get:function(){if(!Xr(this))throw eo("desiredSize");return hr(this._controlledTransformStream._readable._readableStreamController)}},{key:"enqueue",value:function enqueue(r=void 0){if(!Xr(this))throw eo("enqueue");Kr(this,r)}},{key:"error",value:function error(r=void 0){if(!Xr(this))throw eo("error");var z;z=r,Hr(this._controlledTransformStream,z)}},{key:"terminate",value:function terminate(){if(!Xr(this))throw eo("terminate");!function(r){const z=r._controlledTransformStream;dr(z._readable._readableStreamController);Vr(z,new TypeError("TransformStream terminated"))}(this)}}])}();function Xr(r){return!!t(r)&&!!Object.prototype.hasOwnProperty.call(r,"_controlledTransformStream")&&r instanceof Eo}function Jr(r){r._transformAlgorithm=void 0,r._flushAlgorithm=void 0,r._cancelAlgorithm=void 0}function Kr(r,z){const D=r._controlledTransformStream,te=D._readable._readableStreamController;if(!mr(te))throw new TypeError("Readable side is not in a state that permits enqueue");try{fr(te,z)}catch(r){throw Vr(D,r),D._readable._storedError}const re=function(r){return!ur(r)}(te);re!==D._backpressure&&Gr(D,!0)}function Zr(r,z){return _(r._transformAlgorithm(z),void 0,(z=>{throw Hr(r._controlledTransformStream,z),z}))}function eo(r){return new TypeError(`TransformStreamDefaultController.prototype.${r} can only be used on a TransformStreamDefaultController`)}function to(r){void 0!==r._finishPromise_resolve&&(r._finishPromise_resolve(),r._finishPromise_resolve=void 0,r._finishPromise_reject=void 0)}function ro(r,z){void 0!==r._finishPromise_reject&&(p(r._finishPromise),r._finishPromise_reject(z),r._finishPromise_resolve=void 0,r._finishPromise_reject=void 0)}function oo(r){return new TypeError(`TransformStream.prototype.${r} can only be used on a TransformStream`)}Object.defineProperties(Eo.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),o(Eo.prototype.enqueue,"enqueue"),o(Eo.prototype.error,"error"),o(Eo.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Eo.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});const Wo={ReadableStream:Co,ReadableStreamDefaultController:To,ReadableByteStreamController:mo,ReadableStreamBYOBRequest:_o,ReadableStreamDefaultReader:ao,ReadableStreamBYOBReader:po,WritableStream:yo,WritableStreamDefaultController:So,WritableStreamDefaultWriter:vo,ByteLengthQueuingStrategy:Po,CountQueuingStrategy:ko,TransformStream:qo,TransformStreamDefaultController:Eo};for(const r in Wo)Object.prototype.hasOwnProperty.call(Wo,r)&&Object.defineProperty(wo,r,{value:Wo[r],writable:!0,configurable:!0})}();