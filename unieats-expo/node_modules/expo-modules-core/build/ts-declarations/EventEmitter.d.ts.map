{"version": 3, "file": "EventEmitter.d.ts", "sourceRoot": "", "sources": ["../../src/ts-declarations/EventEmitter.ts"], "names": [], "mappings": "AAAA;;;GAGG;AACH,MAAM,MAAM,SAAS,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,IAAI,EAAE,GAAG,EAAE,KAAK,IAAI,CAAC,CAAC;AAEjE;;GAEG;AACH,MAAM,MAAM,iBAAiB,GAAG;IAC9B;;;OAGG;IACH,MAAM,IAAI,IAAI,CAAC;CAChB,CAAC;AAEF;;;;;;GAMG;AACH,MAAM,CAAC,OAAO,OAAO,YAAY,CAAC,UAAU,SAAS,SAAS,GAAG,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC;IACnF;;;;;OAKG;IACH,uBAAuB,CAAC,EAAE,UAAU,CAAC;IAErC;;OAEG;;IAGH;;;;OAIG;gBACS,MAAM,EAAE,YAAY;IAEhC;;OAEG;IACH,WAAW,CAAC,SAAS,SAAS,MAAM,UAAU,EAC5C,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,GAC9B,iBAAiB;IAEpB;;OAEG;IACH,cAAc,CAAC,SAAS,SAAS,MAAM,UAAU,EAC/C,SAAS,EAAE,SAAS,EACpB,QAAQ,EAAE,UAAU,CAAC,SAAS,CAAC,GAC9B,IAAI;IAEP;;OAEG;IACH,kBAAkB,CAAC,SAAS,EAAE,MAAM,UAAU,GAAG,IAAI;IAErD;;;OAGG;IACH,IAAI,CAAC,SAAS,SAAS,MAAM,UAAU,EACrC,SAAS,EAAE,SAAS,EACpB,GAAG,IAAI,EAAE,UAAU,CAAC,UAAU,CAAC,SAAS,CAAC,CAAC,GACzC,IAAI;IAEP;;OAEG;IACH,aAAa,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,MAAM;IAE/E;;;OAGG;IACH,cAAc,CAAC,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI;IAE/E;;;OAGG;IACH,aAAa,CAAC,CAAC,SAAS,SAAS,MAAM,UAAU,EAAE,SAAS,EAAE,SAAS,GAAG,IAAI;CAC/E"}