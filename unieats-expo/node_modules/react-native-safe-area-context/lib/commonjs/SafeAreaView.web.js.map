{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_SafeAreaContext", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "_extends", "assign", "bind", "arguments", "length", "apply", "defaultEdges", "top", "left", "bottom", "right", "getEdgeValue", "inset", "current", "mode", "Math", "max", "SafeAreaView", "exports", "forwardRef", "style", "edges", "rest", "ref", "insets", "useSafeAreaInsets", "edgesRecord", "useMemo", "Array", "isArray", "reduce", "acc", "edge", "appliedStyle", "flatStyle", "StyleSheet", "flatten", "margin", "marginVertical", "marginHorizontal", "marginTop", "marginRight", "marginBottom", "marginLeft", "marginStyle", "padding", "paddingVertical", "paddingHorizontal", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "paddingStyle", "createElement", "View"], "sourceRoot": "../../src", "sources": ["SafeAreaView.web.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAQA,IAAAE,gBAAA,GAAAF,OAAA;AAAsD,SAAAG,yBAAAC,CAAA,6BAAAC,OAAA,mBAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAF,wBAAA,YAAAA,CAAAC,CAAA,WAAAA,CAAA,GAAAG,CAAA,GAAAD,CAAA,KAAAF,CAAA;AAAA,SAAAL,wBAAAK,CAAA,EAAAE,CAAA,SAAAA,CAAA,IAAAF,CAAA,IAAAA,CAAA,CAAAI,UAAA,SAAAJ,CAAA,eAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,WAAAK,OAAA,EAAAL,CAAA,QAAAG,CAAA,GAAAJ,wBAAA,CAAAG,CAAA,OAAAC,CAAA,IAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,UAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,OAAAQ,CAAA,KAAAC,SAAA,UAAAC,CAAA,GAAAC,MAAA,CAAAC,cAAA,IAAAD,MAAA,CAAAE,wBAAA,WAAAC,CAAA,IAAAd,CAAA,oBAAAc,CAAA,OAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,EAAAc,CAAA,SAAAG,CAAA,GAAAP,CAAA,GAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,EAAAc,CAAA,UAAAG,CAAA,KAAAA,CAAA,CAAAV,GAAA,IAAAU,CAAA,CAAAC,GAAA,IAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,EAAAM,CAAA,EAAAG,CAAA,IAAAT,CAAA,CAAAM,CAAA,IAAAd,CAAA,CAAAc,CAAA,YAAAN,CAAA,CAAAH,OAAA,GAAAL,CAAA,EAAAG,CAAA,IAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,EAAAQ,CAAA,GAAAA,CAAA;AAAA,SAAAW,SAAA,WAAAA,QAAA,GAAAR,MAAA,CAAAS,MAAA,GAAAT,MAAA,CAAAS,MAAA,CAAAC,IAAA,eAAAb,CAAA,aAAAR,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAE,CAAA,IAAAC,CAAA,OAAAY,cAAA,CAAAC,IAAA,CAAAb,CAAA,EAAAD,CAAA,MAAAM,CAAA,CAAAN,CAAA,IAAAC,CAAA,CAAAD,CAAA,aAAAM,CAAA,KAAAW,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAEtD,MAAMG,YAAoC,GAAG;EAC3CC,GAAG,EAAE,UAAU;EACfC,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,SAASC,YAAYA,CACnBC,KAAa,EACbC,OAAe,EACfC,IAA0B,EAC1B;EACA,QAAQA,IAAI;IACV,KAAK,KAAK;MACR,OAAOD,OAAO;IAChB,KAAK,SAAS;MACZ,OAAOE,IAAI,CAACC,GAAG,CAACH,OAAO,EAAED,KAAK,CAAC;IACjC,KAAK,UAAU;IACf;MACE,OAAOC,OAAO,GAAGD,KAAK;EAC1B;AACF;AAEO,MAAMK,YAAY,GAAAC,OAAA,CAAAD,YAAA,gBAAG1C,KAAK,CAAC4C,UAAU,CAG1C,CAAC;EAAEC,KAAK,GAAG,CAAC,CAAC;EAAEN,IAAI;EAAEO,KAAK;EAAE,GAAGC;AAAK,CAAC,EAAEC,GAAG,KAAK;EAC/C,MAAMC,MAAM,GAAG,IAAAC,kCAAiB,EAAC,CAAC;EAElC,MAAMC,WAAW,GAAGnD,KAAK,CAACoD,OAAO,CAAC,MAAM;IACtC,IAAIN,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOf,YAAY;IACrB;IAEA,OAAOsB,KAAK,CAACC,OAAO,CAACR,KAAK,CAAC,GACvBA,KAAK,CAACS,MAAM,CAAa,CAACC,GAAG,EAAEC,IAAU,KAAK;MAC5CD,GAAG,CAACC,IAAI,CAAC,GAAG,UAAU;MACtB,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN;IACCV,KAAoB;EAC3B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,MAAMY,YAAY,GAAG1D,KAAK,CAACoD,OAAO,CAAC,MAAM;IACvC,MAAMO,SAAS,GAAGC,uBAAU,CAACC,OAAO,CAAChB,KAAK,CAA2B;IAErE,IAAIN,IAAI,KAAK,QAAQ,EAAE;MACrB,MAAM;QACJuB,MAAM,GAAG,CAAC;QACVC,cAAc,GAAGD,MAAM;QACvBE,gBAAgB,GAAGF,MAAM;QACzBG,SAAS,GAAGF,cAAc;QAC1BG,WAAW,GAAGF,gBAAgB;QAC9BG,YAAY,GAAGJ,cAAc;QAC7BK,UAAU,GAAGJ;MACf,CAAC,GAAGL,SAAS;MAEb,MAAMU,WAAW,GAAG;QAClBJ,SAAS,EAAE7B,YAAY,CAACa,MAAM,CAACjB,GAAG,EAAEiC,SAAS,EAAEd,WAAW,CAACnB,GAAG,CAAC;QAC/DkC,WAAW,EAAE9B,YAAY,CAACa,MAAM,CAACd,KAAK,EAAE+B,WAAW,EAAEf,WAAW,CAAChB,KAAK,CAAC;QACvEgC,YAAY,EAAE/B,YAAY,CACxBa,MAAM,CAACf,MAAM,EACbiC,YAAY,EACZhB,WAAW,CAACjB,MACd,CAAC;QACDkC,UAAU,EAAEhC,YAAY,CAACa,MAAM,CAAChB,IAAI,EAAEmC,UAAU,EAAEjB,WAAW,CAAClB,IAAI;MACpE,CAAC;MAED,OAAO,CAACY,KAAK,EAAEwB,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL,MAAM;QACJC,OAAO,GAAG,CAAC;QACXC,eAAe,GAAGD,OAAO;QACzBE,iBAAiB,GAAGF,OAAO;QAC3BG,UAAU,GAAGF,eAAe;QAC5BG,YAAY,GAAGF,iBAAiB;QAChCG,aAAa,GAAGJ,eAAe;QAC/BK,WAAW,GAAGJ;MAChB,CAAC,GAAGb,SAAS;MAEb,MAAMkB,YAAY,GAAG;QACnBJ,UAAU,EAAErC,YAAY,CAACa,MAAM,CAACjB,GAAG,EAAEyC,UAAU,EAAEtB,WAAW,CAACnB,GAAG,CAAC;QACjE0C,YAAY,EAAEtC,YAAY,CACxBa,MAAM,CAACd,KAAK,EACZuC,YAAY,EACZvB,WAAW,CAAChB,KACd,CAAC;QACDwC,aAAa,EAAEvC,YAAY,CACzBa,MAAM,CAACf,MAAM,EACbyC,aAAa,EACbxB,WAAW,CAACjB,MACd,CAAC;QACD0C,WAAW,EAAExC,YAAY,CAACa,MAAM,CAAChB,IAAI,EAAE2C,WAAW,EAAEzB,WAAW,CAAClB,IAAI;MACtE,CAAC;MAED,OAAO,CAACY,KAAK,EAAEgC,YAAY,CAAC;IAC9B;EACF,CAAC,EAAE,CACD1B,WAAW,CAACjB,MAAM,EAClBiB,WAAW,CAAClB,IAAI,EAChBkB,WAAW,CAAChB,KAAK,EACjBgB,WAAW,CAACnB,GAAG,EACfiB,MAAM,CAACf,MAAM,EACbe,MAAM,CAAChB,IAAI,EACXgB,MAAM,CAACd,KAAK,EACZc,MAAM,CAACjB,GAAG,EACVO,IAAI,EACJM,KAAK,CACN,CAAC;EAEF,oBAAO7C,KAAA,CAAA8E,aAAA,CAAC3E,YAAA,CAAA4E,IAAI,EAAAtD,QAAA;IAACoB,KAAK,EAAEa;EAAa,GAAKX,IAAI;IAAEC,GAAG,EAAEA;EAAI,EAAE,CAAC;AAC1D,CAAC,CAAC", "ignoreList": []}