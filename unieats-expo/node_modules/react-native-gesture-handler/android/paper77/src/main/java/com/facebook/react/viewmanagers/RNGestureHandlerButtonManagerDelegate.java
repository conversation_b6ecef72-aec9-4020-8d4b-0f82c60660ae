/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.ColorPropConverter;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class RNGestureHandlerButtonManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & RNGestureHandlerButtonManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public RNGestureHandlerButtonManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "exclusive":
        mViewManager.setExclusive(view, value == null ? true : (boolean) value);
        break;
      case "foreground":
        mViewManager.setForeground(view, value == null ? false : (boolean) value);
        break;
      case "borderless":
        mViewManager.setBorderless(view, value == null ? false : (boolean) value);
        break;
      case "enabled":
        mViewManager.setEnabled(view, value == null ? true : (boolean) value);
        break;
      case "rippleColor":
        mViewManager.setRippleColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "rippleRadius":
        mViewManager.setRippleRadius(view, value == null ? 0 : ((Double) value).intValue());
        break;
      case "touchSoundDisabled":
        mViewManager.setTouchSoundDisabled(view, value == null ? false : (boolean) value);
        break;
      case "borderWidth":
        mViewManager.setBorderWidth(view, value == null ? 0f : ((Double) value).floatValue());
        break;
      case "borderColor":
        mViewManager.setBorderColor(view, ColorPropConverter.getColor(value, view.getContext()));
        break;
      case "borderStyle":
        mViewManager.setBorderStyle(view, value == null ? "solid" : (String) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
