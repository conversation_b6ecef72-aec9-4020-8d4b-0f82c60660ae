/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.uimanager.ViewManagerWithGeneratedInterface;

public interface RNGestureHandlerButtonManagerInterface<T extends View> extends ViewManagerWithGeneratedInterface {
  void setExclusive(T view, boolean value);
  void setForeground(T view, boolean value);
  void setBorderless(T view, boolean value);
  void setEnabled(T view, boolean value);
  void setRippleColor(T view, @Nullable Integer value);
  void setRippleRadius(T view, int value);
  void setTouchSoundDisabled(T view, boolean value);
  void setBorderWidth(T view, float value);
  void setBorderColor(T view, @Nullable Integer value);
  void setBorderStyle(T view, @Nullable String value);
}
