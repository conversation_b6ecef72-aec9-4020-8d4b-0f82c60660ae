export declare const LightSpeedInData: {
    LightSpeedInRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    skewX: string;
                }[];
                opacity: number;
            };
            70: {
                transform: {
                    skewX: string;
                }[];
            };
            85: {
                transform: {
                    skewX: string;
                }[];
            };
            100: {
                transform: {
                    skewX: string;
                }[];
            };
        };
        duration: number;
    };
    LightSpeedInLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    skewX: string;
                }[];
                opacity: number;
            };
            70: {
                transform: {
                    skewX: string;
                }[];
            };
            85: {
                transform: {
                    skewX: string;
                }[];
            };
            100: {
                transform: {
                    skewX: string;
                }[];
            };
        };
        duration: number;
    };
};
export declare const LightSpeedOutData: {
    LightSpeedOutRight: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    skewX: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    skewX: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
    LightSpeedOutLeft: {
        name: string;
        style: {
            0: {
                transform: {
                    translateX: string;
                    skew: string;
                }[];
                opacity: number;
            };
            100: {
                transform: {
                    translateX: string;
                    skew: string;
                }[];
                opacity: number;
            };
        };
        duration: number;
    };
};
export declare const LightSpeedIn: {
    LightSpeedInRight: {
        style: string;
        duration: number;
    };
    LightSpeedInLeft: {
        style: string;
        duration: number;
    };
};
export declare const LightSpeedOut: {
    LightSpeedOutRight: {
        style: string;
        duration: number;
    };
    LightSpeedOutLeft: {
        style: string;
        duration: number;
    };
};
//# sourceMappingURL=Lightspeed.web.d.ts.map