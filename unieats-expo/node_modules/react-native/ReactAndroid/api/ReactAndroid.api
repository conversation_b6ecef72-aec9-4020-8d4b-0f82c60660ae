public abstract class com/facebook/react/BaseReactPackage : com/facebook/react/ReactPackage {
	public fun <init> ()V
	public fun createNativeModules (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public fun createViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public abstract fun getModule (Ljava/lang/String;Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/bridge/NativeModule;
	public abstract fun getReactModuleInfoProvider ()Lcom/facebook/react/module/model/ReactModuleInfoProvider;
	protected fun getViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
}

public class com/facebook/react/CoreModulesPackage$$ReactModuleInfoProvider : com/facebook/react/module/model/ReactModuleInfoProvider {
	public fun <init> ()V
	public fun getReactModuleInfos ()Ljava/util/Map;
}

public class com/facebook/react/DebugCorePackage : com/facebook/react/BaseReactPackage, com/facebook/react/ViewManagerOnDemandReactPackage {
	public fun <init> ()V
	public fun createViewManager (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public fun getModule (Ljava/lang/String;Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/bridge/NativeModule;
	public fun getReactModuleInfoProvider ()Lcom/facebook/react/module/model/ReactModuleInfoProvider;
	public fun getViewManagerNames (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/Collection;
	public fun getViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
}

public class com/facebook/react/DebugCorePackage$$ReactModuleInfoProvider : com/facebook/react/module/model/ReactModuleInfoProvider {
	public fun <init> ()V
	public fun getReactModuleInfos ()Ljava/util/Map;
}

public abstract class com/facebook/react/HeadlessJsTaskService : android/app/Service, com/facebook/react/jstasks/HeadlessJsTaskEventListener {
	public static final field Companion Lcom/facebook/react/HeadlessJsTaskService$Companion;
	public fun <init> ()V
	public static final fun acquireWakeLockNow (Landroid/content/Context;)V
	protected final fun getReactContext ()Lcom/facebook/react/bridge/ReactContext;
	protected final fun getReactHost ()Lcom/facebook/react/ReactHost;
	protected final fun getReactNativeHost ()Lcom/facebook/react/ReactNativeHost;
	protected fun getTaskConfig (Landroid/content/Intent;)Lcom/facebook/react/jstasks/HeadlessJsTaskConfig;
	public fun onBind (Landroid/content/Intent;)Landroid/os/IBinder;
	public fun onDestroy ()V
	public fun onHeadlessJsTaskFinish (I)V
	public fun onHeadlessJsTaskStart (I)V
	public fun onStartCommand (Landroid/content/Intent;II)I
	protected final fun startTask (Lcom/facebook/react/jstasks/HeadlessJsTaskConfig;)V
}

public final class com/facebook/react/HeadlessJsTaskService$Companion {
	public final fun acquireWakeLockNow (Landroid/content/Context;)V
}

public final class com/facebook/react/JSEngineResolutionAlgorithm : java/lang/Enum {
	public static final field HERMES Lcom/facebook/react/JSEngineResolutionAlgorithm;
	public static final field JSC Lcom/facebook/react/JSEngineResolutionAlgorithm;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/JSEngineResolutionAlgorithm;
	public static fun values ()[Lcom/facebook/react/JSEngineResolutionAlgorithm;
}

public abstract class com/facebook/react/LazyReactPackage : com/facebook/react/ReactPackage {
	public fun <init> ()V
	public final fun createNativeModules (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public fun createViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	protected abstract fun getNativeModules (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public abstract fun getReactModuleInfoProvider ()Lcom/facebook/react/module/model/ReactModuleInfoProvider;
	public fun getViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
}

public class com/facebook/react/MemoryPressureRouter : android/content/ComponentCallbacks2 {
	public fun <init> (Landroid/content/Context;)V
	public fun addMemoryPressureListener (Lcom/facebook/react/bridge/MemoryPressureListener;)V
	public fun destroy (Landroid/content/Context;)V
	public fun onConfigurationChanged (Landroid/content/res/Configuration;)V
	public fun onLowMemory ()V
	public fun onTrimMemory (I)V
	public fun removeMemoryPressureListener (Lcom/facebook/react/bridge/MemoryPressureListener;)V
}

public final class com/facebook/react/NativeModuleRegistryBuilder {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/ReactInstanceManager;)V
	public final fun build ()Lcom/facebook/react/bridge/NativeModuleRegistry;
	public final fun processPackage (Lcom/facebook/react/ReactPackage;)V
}

public abstract class com/facebook/react/ReactActivity : androidx/appcompat/app/AppCompatActivity, com/facebook/react/modules/core/DefaultHardwareBackBtnHandler, com/facebook/react/modules/core/PermissionAwareActivity {
	protected fun <init> ()V
	protected fun createReactActivityDelegate ()Lcom/facebook/react/ReactActivityDelegate;
	protected fun getMainComponentName ()Ljava/lang/String;
	public fun getReactActivityDelegate ()Lcom/facebook/react/ReactActivityDelegate;
	public fun getReactDelegate ()Lcom/facebook/react/ReactDelegate;
	protected fun getReactHost ()Lcom/facebook/react/ReactHost;
	protected final fun getReactInstanceManager ()Lcom/facebook/react/ReactInstanceManager;
	protected final fun getReactNativeHost ()Lcom/facebook/react/ReactNativeHost;
	public fun invokeDefaultOnBackPressed ()V
	protected final fun loadApp (Ljava/lang/String;)V
	public fun onActivityResult (IILandroid/content/Intent;)V
	public fun onBackPressed ()V
	public fun onConfigurationChanged (Landroid/content/res/Configuration;)V
	protected fun onCreate (Landroid/os/Bundle;)V
	protected fun onDestroy ()V
	public fun onKeyDown (ILandroid/view/KeyEvent;)Z
	public fun onKeyLongPress (ILandroid/view/KeyEvent;)Z
	public fun onKeyUp (ILandroid/view/KeyEvent;)Z
	public fun onNewIntent (Landroid/content/Intent;)V
	protected fun onPause ()V
	public fun onRequestPermissionsResult (I[Ljava/lang/String;[I)V
	protected fun onResume ()V
	public fun onUserLeaveHint ()V
	public fun onWindowFocusChanged (Z)V
	public fun requestPermissions ([Ljava/lang/String;ILcom/facebook/react/modules/core/PermissionListener;)V
}

public class com/facebook/react/ReactActivityDelegate {
	public fun <init> (Landroid/app/Activity;Ljava/lang/String;)V
	public fun <init> (Lcom/facebook/react/ReactActivity;Ljava/lang/String;)V
	protected fun composeLaunchOptions ()Landroid/os/Bundle;
	protected fun createRootView ()Lcom/facebook/react/ReactRootView;
	protected fun getContext ()Landroid/content/Context;
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	protected fun getLaunchOptions ()Landroid/os/Bundle;
	public fun getMainComponentName ()Ljava/lang/String;
	protected fun getPlainActivity ()Landroid/app/Activity;
	protected fun getReactActivity ()Lcom/facebook/react/ReactActivity;
	protected fun getReactDelegate ()Lcom/facebook/react/ReactDelegate;
	public fun getReactHost ()Lcom/facebook/react/ReactHost;
	public fun getReactInstanceManager ()Lcom/facebook/react/ReactInstanceManager;
	protected fun getReactNativeHost ()Lcom/facebook/react/ReactNativeHost;
	protected fun isFabricEnabled ()Z
	protected fun isWideColorGamutEnabled ()Z
	protected fun loadApp (Ljava/lang/String;)V
	public fun onActivityResult (IILandroid/content/Intent;)V
	public fun onBackPressed ()Z
	public fun onConfigurationChanged (Landroid/content/res/Configuration;)V
	public fun onCreate (Landroid/os/Bundle;)V
	public fun onDestroy ()V
	public fun onKeyDown (ILandroid/view/KeyEvent;)Z
	public fun onKeyLongPress (ILandroid/view/KeyEvent;)Z
	public fun onKeyUp (ILandroid/view/KeyEvent;)Z
	public fun onNewIntent (Landroid/content/Intent;)Z
	public fun onPause ()V
	public fun onRequestPermissionsResult (I[Ljava/lang/String;[I)V
	public fun onResume ()V
	public fun onUserLeaveHint ()V
	public fun onWindowFocusChanged (Z)V
	public fun requestPermissions ([Ljava/lang/String;ILcom/facebook/react/modules/core/PermissionListener;)V
	public fun setReactRootView (Lcom/facebook/react/ReactRootView;)V
	public fun setReactSurface (Lcom/facebook/react/interfaces/fabric/ReactSurface;)V
}

public abstract interface class com/facebook/react/ReactApplication {
	public fun getReactHost ()Lcom/facebook/react/ReactHost;
	public abstract fun getReactNativeHost ()Lcom/facebook/react/ReactNativeHost;
}

public class com/facebook/react/ReactDelegate {
	public fun <init> (Landroid/app/Activity;Lcom/facebook/react/ReactHost;Ljava/lang/String;Landroid/os/Bundle;)V
	public fun <init> (Landroid/app/Activity;Lcom/facebook/react/ReactNativeHost;Ljava/lang/String;Landroid/os/Bundle;)V
	public fun <init> (Landroid/app/Activity;Lcom/facebook/react/ReactNativeHost;Ljava/lang/String;Landroid/os/Bundle;Z)V
	protected fun createRootView ()Lcom/facebook/react/ReactRootView;
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public fun getReactHost ()Lcom/facebook/react/ReactHost;
	public fun getReactInstanceManager ()Lcom/facebook/react/ReactInstanceManager;
	public fun getReactRootView ()Lcom/facebook/react/ReactRootView;
	protected fun isFabricEnabled ()Z
	public fun loadApp ()V
	public fun loadApp (Ljava/lang/String;)V
	public fun onActivityResult (IILandroid/content/Intent;Z)V
	public fun onBackPressed ()Z
	public fun onConfigurationChanged (Landroid/content/res/Configuration;)V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun onKeyDown (ILandroid/view/KeyEvent;)Z
	public fun onKeyLongPress (I)Z
	public fun onNewIntent (Landroid/content/Intent;)Z
	public fun onUserLeaveHint ()V
	public fun onWindowFocusChanged (Z)V
	public fun reload ()V
	public fun setReactRootView (Lcom/facebook/react/ReactRootView;)V
	public fun setReactSurface (Lcom/facebook/react/interfaces/fabric/ReactSurface;)V
	public fun shouldShowDevMenuOrReload (ILandroid/view/KeyEvent;)Z
	public fun unloadApp ()V
}

public class com/facebook/react/ReactFragment : androidx/fragment/app/Fragment, com/facebook/react/modules/core/PermissionAwareActivity {
	protected static final field ARG_COMPONENT_NAME Ljava/lang/String;
	protected static final field ARG_DISABLE_HOST_LIFECYCLE_EVENTS Ljava/lang/String;
	protected static final field ARG_FABRIC_ENABLED Ljava/lang/String;
	protected static final field ARG_LAUNCH_OPTIONS Ljava/lang/String;
	protected field mReactDelegate Lcom/facebook/react/ReactDelegate;
	public fun <init> ()V
	public fun checkPermission (Ljava/lang/String;II)I
	public fun checkSelfPermission (Ljava/lang/String;)I
	protected fun getReactDelegate ()Lcom/facebook/react/ReactDelegate;
	protected fun getReactHost ()Lcom/facebook/react/ReactHost;
	protected fun getReactNativeHost ()Lcom/facebook/react/ReactNativeHost;
	public fun onActivityResult (IILandroid/content/Intent;)V
	public fun onBackPressed ()Z
	public fun onCreate (Landroid/os/Bundle;)V
	public fun onCreateView (Landroid/view/LayoutInflater;Landroid/view/ViewGroup;Landroid/os/Bundle;)Landroid/view/View;
	public fun onDestroy ()V
	public fun onKeyUp (ILandroid/view/KeyEvent;)Z
	public fun onPause ()V
	public fun onRequestPermissionsResult (I[Ljava/lang/String;[I)V
	public fun onResume ()V
	public fun requestPermissions ([Ljava/lang/String;ILcom/facebook/react/modules/core/PermissionListener;)V
}

public class com/facebook/react/ReactFragment$Builder {
	public fun <init> ()V
	public fun build ()Lcom/facebook/react/ReactFragment;
	public fun setComponentName (Ljava/lang/String;)Lcom/facebook/react/ReactFragment$Builder;
	public fun setFabricEnabled (Z)Lcom/facebook/react/ReactFragment$Builder;
	public fun setLaunchOptions (Landroid/os/Bundle;)Lcom/facebook/react/ReactFragment$Builder;
}

public abstract interface class com/facebook/react/ReactHost {
	public abstract fun addBeforeDestroyListener (Lkotlin/jvm/functions/Function0;)V
	public abstract fun addReactInstanceEventListener (Lcom/facebook/react/ReactInstanceEventListener;)V
	public abstract fun createSurface (Landroid/content/Context;Ljava/lang/String;Landroid/os/Bundle;)Lcom/facebook/react/interfaces/fabric/ReactSurface;
	public abstract fun destroy (Ljava/lang/String;Ljava/lang/Exception;)Lcom/facebook/react/interfaces/TaskInterface;
	public abstract fun destroy (Ljava/lang/String;Ljava/lang/Exception;Lkotlin/jvm/functions/Function1;)Lcom/facebook/react/interfaces/TaskInterface;
	public static synthetic fun destroy$default (Lcom/facebook/react/ReactHost;Ljava/lang/String;Ljava/lang/Exception;Lkotlin/jvm/functions/Function1;ILjava/lang/Object;)Lcom/facebook/react/interfaces/TaskInterface;
	public abstract fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public abstract fun getDevSupportManager ()Lcom/facebook/react/devsupport/interfaces/DevSupportManager;
	public abstract fun getLifecycleState ()Lcom/facebook/react/common/LifecycleState;
	public abstract fun getMemoryPressureRouter ()Lcom/facebook/react/MemoryPressureRouter;
	public abstract fun getReactQueueConfiguration ()Lcom/facebook/react/bridge/queue/ReactQueueConfiguration;
	public abstract fun invalidate ()V
	public abstract fun onActivityResult (Landroid/app/Activity;IILandroid/content/Intent;)V
	public abstract fun onBackPressed ()Z
	public abstract fun onConfigurationChanged (Landroid/content/Context;)V
	public abstract fun onHostDestroy ()V
	public abstract fun onHostDestroy (Landroid/app/Activity;)V
	public abstract fun onHostLeaveHint (Landroid/app/Activity;)V
	public abstract fun onHostPause ()V
	public abstract fun onHostPause (Landroid/app/Activity;)V
	public abstract fun onHostResume (Landroid/app/Activity;)V
	public abstract fun onHostResume (Landroid/app/Activity;Lcom/facebook/react/modules/core/DefaultHardwareBackBtnHandler;)V
	public abstract fun onNewIntent (Landroid/content/Intent;)V
	public abstract fun onWindowFocusChange (Z)V
	public abstract fun reload (Ljava/lang/String;)Lcom/facebook/react/interfaces/TaskInterface;
	public abstract fun removeBeforeDestroyListener (Lkotlin/jvm/functions/Function0;)V
	public abstract fun removeReactInstanceEventListener (Lcom/facebook/react/ReactInstanceEventListener;)V
	public abstract fun start ()Lcom/facebook/react/interfaces/TaskInterface;
}

public abstract interface class com/facebook/react/ReactInstanceEventListener {
	public abstract fun onReactContextInitialized (Lcom/facebook/react/bridge/ReactContext;)V
}

public class com/facebook/react/ReactInstanceManager {
	public fun addReactInstanceEventListener (Lcom/facebook/react/ReactInstanceEventListener;)V
	public fun attachRootView (Lcom/facebook/react/uimanager/ReactRoot;)V
	public static fun builder ()Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun createReactContextInBackground ()V
	public fun createViewManager (Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public fun destroy ()V
	public fun detachRootView (Lcom/facebook/react/uimanager/ReactRoot;)V
	public fun getDevSupportManager ()Lcom/facebook/react/devsupport/interfaces/DevSupportManager;
	public fun getJsExecutorName ()Ljava/lang/String;
	public fun getLifecycleState ()Lcom/facebook/react/common/LifecycleState;
	public fun getMemoryPressureRouter ()Lcom/facebook/react/MemoryPressureRouter;
	public fun getOrCreateViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public fun getPackages ()Ljava/util/List;
	public fun getViewManagerNames ()Ljava/util/Collection;
	public fun handleCxxError (Ljava/lang/Exception;)V
	public fun hasStartedCreatingInitialContext ()Z
	public fun invalidate ()V
	public fun onActivityResult (Landroid/app/Activity;IILandroid/content/Intent;)V
	public fun onBackPressed ()V
	public fun onConfigurationChanged (Landroid/content/Context;Landroid/content/res/Configuration;)V
	public fun onHostDestroy ()V
	public fun onHostDestroy (Landroid/app/Activity;)V
	public fun onHostPause ()V
	public fun onHostPause (Landroid/app/Activity;)V
	public fun onHostResume (Landroid/app/Activity;)V
	public fun onHostResume (Landroid/app/Activity;Lcom/facebook/react/modules/core/DefaultHardwareBackBtnHandler;)V
	public fun onNewIntent (Landroid/content/Intent;)V
	public fun onUserLeaveHint (Landroid/app/Activity;)V
	public fun onWindowFocusChange (Z)V
	public fun recreateReactContextInBackground ()V
	public fun removeReactInstanceEventListener (Lcom/facebook/react/ReactInstanceEventListener;)V
	public fun setUseFallbackBundle (Z)V
	public fun showDevOptionsDialog ()V
}

public abstract interface class com/facebook/react/ReactInstanceManager$ReactInstanceEventListener : com/facebook/react/ReactInstanceEventListener {
}

public class com/facebook/react/ReactInstanceManagerBuilder {
	public fun addPackage (Lcom/facebook/react/ReactPackage;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun addPackages (Ljava/util/List;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun build ()Lcom/facebook/react/ReactInstanceManager;
	public fun setApplication (Landroid/app/Application;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setBundleAssetName (Ljava/lang/String;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setChoreographerProvider (Lcom/facebook/react/internal/ChoreographerProvider;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setCurrentActivity (Landroid/app/Activity;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setCustomPackagerCommandHandlers (Ljava/util/Map;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setDefaultHardwareBackBtnHandler (Lcom/facebook/react/modules/core/DefaultHardwareBackBtnHandler;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setDevBundleDownloadListener (Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setDevLoadingViewManager (Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setDevSupportManagerFactory (Lcom/facebook/react/devsupport/DevSupportManagerFactory;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setInitialLifecycleState (Lcom/facebook/react/common/LifecycleState;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setJSBundleFile (Ljava/lang/String;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setJSBundleLoader (Lcom/facebook/react/bridge/JSBundleLoader;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setJSEngineResolutionAlgorithm (Lcom/facebook/react/JSEngineResolutionAlgorithm;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setJSExceptionHandler (Lcom/facebook/react/bridge/JSExceptionHandler;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setJSMainModulePath (Ljava/lang/String;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setJavaScriptExecutorFactory (Lcom/facebook/react/bridge/JavaScriptExecutorFactory;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setKeepActivity (Z)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setLazyViewManagersEnabled (Z)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setMinNumShakes (I)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setMinTimeLeftInFrameForNonBatchedOperationMs (I)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setPausedInDebuggerOverlayManager (Lcom/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setReactPackageTurboModuleManagerDelegateBuilder (Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setRedBoxHandler (Lcom/facebook/react/devsupport/interfaces/RedBoxHandler;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setRequireActivity (Z)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setSurfaceDelegateFactory (Lcom/facebook/react/common/SurfaceDelegateFactory;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setUIManagerProvider (Lcom/facebook/react/bridge/UIManagerProvider;)Lcom/facebook/react/ReactInstanceManagerBuilder;
	public fun setUseDeveloperSupport (Z)Lcom/facebook/react/ReactInstanceManagerBuilder;
}

public abstract class com/facebook/react/ReactNativeHost {
	protected fun <init> (Landroid/app/Application;)V
	public fun clear ()V
	protected fun createReactInstanceManager ()Lcom/facebook/react/ReactInstanceManager;
	protected final fun getApplication ()Landroid/app/Application;
	protected fun getBaseReactInstanceManagerBuilder ()Lcom/facebook/react/ReactInstanceManagerBuilder;
	protected fun getBundleAssetName ()Ljava/lang/String;
	protected fun getChoreographerProvider ()Lcom/facebook/react/internal/ChoreographerProvider;
	protected fun getDevLoadingViewManager ()Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;
	protected fun getDevSupportManagerFactory ()Lcom/facebook/react/devsupport/DevSupportManagerFactory;
	protected fun getJSBundleFile ()Ljava/lang/String;
	protected fun getJSEngineResolutionAlgorithm ()Lcom/facebook/react/JSEngineResolutionAlgorithm;
	protected fun getJSExceptionHandler ()Lcom/facebook/react/bridge/JSExceptionHandler;
	protected fun getJSMainModuleName ()Ljava/lang/String;
	protected fun getJavaScriptExecutorFactory ()Lcom/facebook/react/bridge/JavaScriptExecutorFactory;
	public fun getLazyViewManagersEnabled ()Z
	protected abstract fun getPackages ()Ljava/util/List;
	protected fun getPausedInDebuggerOverlayManager ()Lcom/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager;
	public fun getReactInstanceManager ()Lcom/facebook/react/ReactInstanceManager;
	protected fun getReactPackageTurboModuleManagerDelegateBuilder ()Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder;
	protected fun getRedBoxHandler ()Lcom/facebook/react/devsupport/interfaces/RedBoxHandler;
	public fun getShouldRequireActivity ()Z
	public fun getSurfaceDelegateFactory ()Lcom/facebook/react/common/SurfaceDelegateFactory;
	protected fun getUIManagerProvider ()Lcom/facebook/react/bridge/UIManagerProvider;
	public abstract fun getUseDeveloperSupport ()Z
	public fun hasInstance ()Z
}

public abstract interface class com/facebook/react/ReactPackage {
	public abstract fun createNativeModules (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public abstract fun createViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public fun getModule (Ljava/lang/String;Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/bridge/NativeModule;
}

public abstract class com/facebook/react/ReactPackageTurboModuleManagerDelegate : com/facebook/react/internal/turbomodule/core/TurboModuleManagerDelegate {
	protected fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;)V
	protected fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;Lcom/facebook/jni/HybridData;)V
	public fun getEagerInitModuleNames ()Ljava/util/List;
	public fun getLegacyModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public fun getModule (Ljava/lang/String;)Lcom/facebook/react/turbomodule/core/interfaces/TurboModule;
	public fun unstable_isLegacyModuleRegistered (Ljava/lang/String;)Z
	public fun unstable_isModuleRegistered (Ljava/lang/String;)Z
	public fun unstable_shouldEnableLegacyModuleInterop ()Z
}

public abstract class com/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder {
	public fun <init> ()V
	public fun build ()Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate;
	protected abstract fun build (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;)Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate;
	public fun setPackages (Ljava/util/List;)Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder;
	public fun setReactApplicationContext (Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder;
}

public class com/facebook/react/ReactRootView : android/widget/FrameLayout, com/facebook/react/uimanager/ReactRoot, com/facebook/react/uimanager/RootView {
	public fun <init> (Landroid/content/Context;)V
	public fun <init> (Landroid/content/Context;Landroid/util/AttributeSet;)V
	public fun <init> (Landroid/content/Context;Landroid/util/AttributeSet;I)V
	protected fun dispatchDraw (Landroid/graphics/Canvas;)V
	protected fun dispatchJSPointerEvent (Landroid/view/MotionEvent;Z)V
	protected fun dispatchJSTouchEvent (Landroid/view/MotionEvent;)V
	public fun dispatchKeyEvent (Landroid/view/KeyEvent;)Z
	protected fun drawChild (Landroid/graphics/Canvas;Landroid/view/View;J)Z
	protected fun finalize ()V
	public fun getAppProperties ()Landroid/os/Bundle;
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public fun getHeightMeasureSpec ()I
	public fun getJSModuleName ()Ljava/lang/String;
	public fun getReactInstanceManager ()Lcom/facebook/react/ReactInstanceManager;
	public fun getRootViewGroup ()Landroid/view/ViewGroup;
	public fun getRootViewTag ()I
	public fun getState ()Ljava/util/concurrent/atomic/AtomicInteger;
	public fun getSurfaceID ()Ljava/lang/String;
	public fun getUIManagerType ()I
	public fun getWidthMeasureSpec ()I
	public fun handleException (Ljava/lang/Throwable;)V
	public fun hasActiveReactContext ()Z
	public fun hasActiveReactInstance ()Z
	public fun isViewAttachedToReactInstance ()Z
	public fun onAttachedToReactInstance ()V
	protected fun onAttachedToWindow ()V
	public fun onChildEndedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	public fun onChildStartedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	protected fun onDetachedFromWindow ()V
	protected fun onFocusChanged (ZILandroid/graphics/Rect;)V
	public fun onHoverEvent (Landroid/view/MotionEvent;)Z
	public fun onInterceptHoverEvent (Landroid/view/MotionEvent;)Z
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	protected fun onLayout (ZIIII)V
	protected fun onMeasure (II)V
	public fun onStage (I)V
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun onViewAdded (Landroid/view/View;)V
	public fun requestChildFocus (Landroid/view/View;Landroid/view/View;)V
	public fun requestDisallowInterceptTouchEvent (Z)V
	public fun runApplication ()V
	public fun setAppProperties (Landroid/os/Bundle;)V
	public fun setEventListener (Lcom/facebook/react/ReactRootView$ReactRootViewEventListener;)V
	public fun setIsFabric (Z)V
	public fun setRootViewTag (I)V
	public fun setShouldLogContentAppeared (Z)V
	public fun shouldDispatchJSTouchEvent (Landroid/view/MotionEvent;)Z
	public fun startReactApplication (Lcom/facebook/react/ReactInstanceManager;Ljava/lang/String;)V
	public fun startReactApplication (Lcom/facebook/react/ReactInstanceManager;Ljava/lang/String;Landroid/os/Bundle;)V
	public fun unmountReactApplication ()V
}

public abstract interface class com/facebook/react/ReactRootView$ReactRootViewEventListener {
	public abstract fun onAttachedToReactInstance (Lcom/facebook/react/ReactRootView;)V
}

public abstract class com/facebook/react/TurboReactPackage : com/facebook/react/BaseReactPackage {
	public fun <init> ()V
}

public abstract interface class com/facebook/react/ViewManagerOnDemandReactPackage {
	public abstract fun createViewManager (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public abstract fun getViewManagerNames (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/Collection;
}

public abstract class com/facebook/react/animated/AnimatedNode {
	public static final field Companion Lcom/facebook/react/animated/AnimatedNode$Companion;
	public static final field DEFAULT_ANIMATED_NODE_CHILD_COUNT I
	public static final field INITIAL_BFS_COLOR I
	public fun <init> ()V
	public final fun addChild (Lcom/facebook/react/animated/AnimatedNode;)V
	public fun onAttachedToNode (Lcom/facebook/react/animated/AnimatedNode;)V
	public fun onDetachedFromNode (Lcom/facebook/react/animated/AnimatedNode;)V
	public abstract fun prettyPrint ()Ljava/lang/String;
	public final fun prettyPrintWithChildren ()Ljava/lang/String;
	public final fun removeChild (Lcom/facebook/react/animated/AnimatedNode;)V
	public fun update ()V
}

public final class com/facebook/react/animated/AnimatedNode$Companion {
}

public class com/facebook/react/animated/NativeAnimatedModule : com/facebook/fbreact/specs/NativeAnimatedModuleSpec, com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/bridge/UIManagerListener {
	public static final field ANIMATED_MODULE_DEBUG Z
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun addAnimatedEventToView (DLjava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun addListener (Ljava/lang/String;)V
	public fun connectAnimatedNodeToView (DD)V
	public fun connectAnimatedNodes (DD)V
	public fun createAnimatedNode (DLcom/facebook/react/bridge/ReadableMap;)V
	public fun didDispatchMountItems (Lcom/facebook/react/bridge/UIManager;)V
	public fun didMountItems (Lcom/facebook/react/bridge/UIManager;)V
	public fun didScheduleMountItems (Lcom/facebook/react/bridge/UIManager;)V
	public fun disconnectAnimatedNodeFromView (DD)V
	public fun disconnectAnimatedNodes (DD)V
	public fun dropAnimatedNode (D)V
	public fun extractAnimatedNodeOffset (D)V
	public fun finishOperationBatch ()V
	public fun flattenAnimatedNodeOffset (D)V
	public fun getNodesManager ()Lcom/facebook/react/animated/NativeAnimatedNodesManager;
	public fun getValue (DLcom/facebook/react/bridge/Callback;)V
	public fun initialize ()V
	public fun invalidate ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun queueAndExecuteBatchedOperations (Lcom/facebook/react/bridge/ReadableArray;)V
	public fun removeAnimatedEventFromView (DLjava/lang/String;D)V
	public fun removeListeners (D)V
	public fun restoreDefaultValues (D)V
	public fun setAnimatedNodeOffset (DD)V
	public fun setAnimatedNodeValue (DD)V
	public fun startAnimatingNode (DDLcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V
	public fun startListeningToAnimatedNodeValue (D)V
	public fun startOperationBatch ()V
	public fun stopAnimation (D)V
	public fun stopListeningToAnimatedNodeValue (D)V
	public fun updateAnimatedNodeConfig (DLcom/facebook/react/bridge/ReadableMap;)V
	public fun userDrivenScrollEnded (I)V
	public fun willDispatchViewUpdates (Lcom/facebook/react/bridge/UIManager;)V
	public fun willMountItems (Lcom/facebook/react/bridge/UIManager;)V
}

public class com/facebook/react/animated/NativeAnimatedNodesManager : com/facebook/react/uimanager/events/EventDispatcherListener {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun addAnimatedEventToView (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun connectAnimatedNodeToView (II)V
	public fun connectAnimatedNodes (II)V
	public fun createAnimatedNode (ILcom/facebook/react/bridge/ReadableMap;)V
	public fun disconnectAnimatedNodeFromView (II)V
	public fun disconnectAnimatedNodes (II)V
	public fun dropAnimatedNode (I)V
	public fun extractAnimatedNodeOffset (I)V
	public fun flattenAnimatedNodeOffset (I)V
	public fun getNodeById (I)Lcom/facebook/react/animated/AnimatedNode;
	public fun getValue (ILcom/facebook/react/bridge/Callback;)V
	public fun hasActiveAnimations ()Z
	public fun initializeEventListenerForUIManagerType (I)V
	public fun onEventDispatch (Lcom/facebook/react/uimanager/events/Event;)V
	public fun removeAnimatedEventFromView (ILjava/lang/String;I)V
	public fun restoreDefaultValues (I)V
	public fun runUpdates (J)V
	public fun setAnimatedNodeOffset (ID)V
	public fun setAnimatedNodeValue (ID)V
	public fun startAnimatingNode (IILcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V
	public fun startListeningToAnimatedNodeValue (ILcom/facebook/react/animated/AnimatedNodeValueListener;)V
	public fun stopAnimation (I)V
	public fun stopListeningToAnimatedNodeValue (I)V
	public fun updateAnimatedNodeConfig (ILcom/facebook/react/bridge/ReadableMap;)V
}

public abstract interface class com/facebook/react/bridge/ActivityEventListener {
	public abstract fun onActivityResult (Landroid/app/Activity;IILandroid/content/Intent;)V
	public abstract fun onNewIntent (Landroid/content/Intent;)V
	public fun onUserLeaveHint (Landroid/app/Activity;)V
}

public class com/facebook/react/bridge/Arguments {
	public fun <init> ()V
	public static fun createArray ()Lcom/facebook/react/bridge/WritableArray;
	public static fun createMap ()Lcom/facebook/react/bridge/WritableMap;
	public static fun fromArray (Ljava/lang/Object;)Lcom/facebook/react/bridge/WritableArray;
	public static fun fromBundle (Landroid/os/Bundle;)Lcom/facebook/react/bridge/WritableMap;
	public static fun fromJavaArgs ([Ljava/lang/Object;)Lcom/facebook/react/bridge/WritableNativeArray;
	public static fun fromList (Ljava/util/List;)Lcom/facebook/react/bridge/WritableArray;
	public static fun makeNativeArray (Ljava/lang/Object;)Lcom/facebook/react/bridge/WritableNativeArray;
	public static fun makeNativeArray (Ljava/util/List;)Lcom/facebook/react/bridge/WritableNativeArray;
	public static fun makeNativeMap (Landroid/os/Bundle;)Lcom/facebook/react/bridge/WritableNativeMap;
	public static fun makeNativeMap (Ljava/util/Map;)Lcom/facebook/react/bridge/WritableNativeMap;
	public static fun toBundle (Lcom/facebook/react/bridge/ReadableMap;)Landroid/os/Bundle;
	public static fun toList (Lcom/facebook/react/bridge/ReadableArray;)Ljava/util/ArrayList;
}

public final class com/facebook/react/bridge/AssertionException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
}

public class com/facebook/react/bridge/BaseActivityEventListener : com/facebook/react/bridge/ActivityEventListener {
	public fun <init> ()V
	public fun onActivityResult (IILandroid/content/Intent;)V
	public fun onActivityResult (Landroid/app/Activity;IILandroid/content/Intent;)V
	public fun onNewIntent (Landroid/content/Intent;)V
}

public abstract class com/facebook/react/bridge/BaseJavaModule : com/facebook/react/bridge/NativeModule {
	public static final field METHOD_TYPE_ASYNC Ljava/lang/String;
	public static final field METHOD_TYPE_PROMISE Ljava/lang/String;
	public static final field METHOD_TYPE_SYNC Ljava/lang/String;
	protected field mEventEmitterCallback Lcom/facebook/react/bridge/CxxCallbackImpl;
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun canOverrideExistingModule ()Z
	public fun getConstants ()Ljava/util/Map;
	protected final fun getReactApplicationContext ()Lcom/facebook/react/bridge/ReactApplicationContext;
	protected final fun getReactApplicationContextIfActiveOrWarn ()Lcom/facebook/react/bridge/ReactApplicationContext;
	public fun initialize ()V
	public fun invalidate ()V
	protected fun setEventEmitterCallback (Lcom/facebook/react/bridge/CxxCallbackImpl;)V
}

public abstract interface class com/facebook/react/bridge/BridgeReactContext$RCTDeviceEventEmitter : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun emit (Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/bridge/Callback {
	public abstract fun invoke ([Ljava/lang/Object;)V
}

public final class com/facebook/react/bridge/CallbackImpl : com/facebook/react/bridge/Callback {
	public fun <init> (Lcom/facebook/react/bridge/JSInstance;I)V
	public fun invoke ([Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/bridge/CatalystInstance : com/facebook/react/bridge/JSBundleLoaderDelegate, com/facebook/react/bridge/JSInstance, com/facebook/react/bridge/MemoryPressureListener {
	public abstract fun addBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)V
	public abstract fun callFunction (Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/bridge/NativeArray;)V
	public abstract fun destroy ()V
	public abstract fun extendNativeModules (Lcom/facebook/react/bridge/NativeModuleRegistry;)V
	public abstract fun getFabricUIManager ()Lcom/facebook/react/bridge/UIManager;
	public abstract fun getJSCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/CallInvokerHolder;
	public abstract fun getJSModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/JavaScriptModule;
	public abstract fun getJavaScriptContextHolder ()Lcom/facebook/react/bridge/JavaScriptContextHolder;
	public abstract fun getNativeMethodCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/NativeMethodCallInvokerHolder;
	public abstract fun getNativeModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/NativeModule;
	public abstract fun getNativeModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public abstract fun getNativeModules ()Ljava/util/Collection;
	public abstract fun getReactQueueConfiguration ()Lcom/facebook/react/bridge/queue/ReactQueueConfiguration;
	public abstract fun getRuntimeExecutor ()Lcom/facebook/react/bridge/RuntimeExecutor;
	public abstract fun getRuntimeScheduler ()Lcom/facebook/react/bridge/RuntimeScheduler;
	public abstract fun getSourceURL ()Ljava/lang/String;
	public abstract fun hasNativeModule (Ljava/lang/Class;)Z
	public abstract fun hasRunJSBundle ()Z
	public abstract fun invokeCallback (ILcom/facebook/react/bridge/NativeArrayInterface;)V
	public abstract fun isDestroyed ()Z
	public abstract fun registerSegment (ILjava/lang/String;)V
	public abstract fun removeBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)V
	public abstract fun runJSBundle ()V
	public abstract fun setFabricUIManager (Lcom/facebook/react/bridge/UIManager;)V
	public abstract fun setTurboModuleRegistry (Lcom/facebook/react/internal/turbomodule/core/interfaces/TurboModuleRegistry;)V
}

public class com/facebook/react/bridge/CatalystInstanceImpl : com/facebook/react/bridge/CatalystInstance {
	public fun addBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)V
	public fun callFunction (Lcom/facebook/react/bridge/CatalystInstanceImpl$PendingJSCall;)V
	public fun callFunction (Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/bridge/NativeArray;)V
	public fun destroy ()V
	public fun extendNativeModules (Lcom/facebook/react/bridge/NativeModuleRegistry;)V
	public fun getFabricUIManager ()Lcom/facebook/react/bridge/UIManager;
	public fun getJSCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/CallInvokerHolderImpl;
	public synthetic fun getJSCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/CallInvokerHolder;
	public fun getJSModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/JavaScriptModule;
	public fun getJavaScriptContextHolder ()Lcom/facebook/react/bridge/JavaScriptContextHolder;
	public fun getNativeMethodCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/NativeMethodCallInvokerHolderImpl;
	public synthetic fun getNativeMethodCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/NativeMethodCallInvokerHolder;
	public fun getNativeModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/NativeModule;
	public fun getNativeModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public fun getNativeModules ()Ljava/util/Collection;
	public fun getReactQueueConfiguration ()Lcom/facebook/react/bridge/queue/ReactQueueConfiguration;
	public fun getRuntimeExecutor ()Lcom/facebook/react/bridge/RuntimeExecutor;
	public fun getRuntimeScheduler ()Lcom/facebook/react/bridge/RuntimeScheduler;
	public fun getSourceURL ()Ljava/lang/String;
	public fun handleMemoryPressure (I)V
	public fun hasNativeModule (Ljava/lang/Class;)Z
	public fun hasRunJSBundle ()Z
	public fun invokeCallback (ILcom/facebook/react/bridge/NativeArrayInterface;)V
	public fun isDestroyed ()Z
	public fun loadScriptFromAssets (Landroid/content/res/AssetManager;Ljava/lang/String;Z)V
	public fun loadScriptFromFile (Ljava/lang/String;Ljava/lang/String;Z)V
	public fun loadSplitBundleFromFile (Ljava/lang/String;Ljava/lang/String;)V
	public fun registerSegment (ILjava/lang/String;)V
	public fun removeBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)V
	public fun runJSBundle ()V
	public fun setFabricUIManager (Lcom/facebook/react/bridge/UIManager;)V
	public fun setGlobalVariable (Ljava/lang/String;Ljava/lang/String;)V
	public fun setSourceURLs (Ljava/lang/String;Ljava/lang/String;)V
	public fun setTurboModuleRegistry (Lcom/facebook/react/internal/turbomodule/core/interfaces/TurboModuleRegistry;)V
}

public class com/facebook/react/bridge/CatalystInstanceImpl$Builder {
	public fun <init> ()V
	public fun build ()Lcom/facebook/react/bridge/CatalystInstanceImpl;
	public fun setInspectorTarget (Lcom/facebook/react/bridge/ReactInstanceManagerInspectorTarget;)Lcom/facebook/react/bridge/CatalystInstanceImpl$Builder;
	public fun setJSBundleLoader (Lcom/facebook/react/bridge/JSBundleLoader;)Lcom/facebook/react/bridge/CatalystInstanceImpl$Builder;
	public fun setJSExceptionHandler (Lcom/facebook/react/bridge/JSExceptionHandler;)Lcom/facebook/react/bridge/CatalystInstanceImpl$Builder;
	public fun setJSExecutor (Lcom/facebook/react/bridge/JavaScriptExecutor;)Lcom/facebook/react/bridge/CatalystInstanceImpl$Builder;
	public fun setReactQueueConfigurationSpec (Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec;)Lcom/facebook/react/bridge/CatalystInstanceImpl$Builder;
	public fun setRegistry (Lcom/facebook/react/bridge/NativeModuleRegistry;)Lcom/facebook/react/bridge/CatalystInstanceImpl$Builder;
}

public class com/facebook/react/bridge/CatalystInstanceImpl$PendingJSCall {
	public field mArguments Lcom/facebook/react/bridge/NativeArray;
	public field mMethod Ljava/lang/String;
	public field mModule Ljava/lang/String;
	public fun <init> (Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/bridge/NativeArray;)V
	public fun toString ()Ljava/lang/String;
}

public class com/facebook/react/bridge/ColorPropConverter {
	public fun <init> ()V
	public static fun getColor (Ljava/lang/Object;Landroid/content/Context;)Ljava/lang/Integer;
	public static fun getColor (Ljava/lang/Object;Landroid/content/Context;I)Ljava/lang/Integer;
	public static fun getColorInstance (Ljava/lang/Object;Landroid/content/Context;)Landroid/graphics/Color;
	public static fun resolveResourcePath (Landroid/content/Context;Ljava/lang/String;)Ljava/lang/Integer;
}

public final class com/facebook/react/bridge/CxxCallbackImpl : com/facebook/jni/HybridClassBase, com/facebook/react/bridge/Callback {
	public fun invoke ([Ljava/lang/Object;)V
}

public class com/facebook/react/bridge/CxxModuleWrapper : com/facebook/react/bridge/CxxModuleWrapperBase {
	protected fun <init> (Lcom/facebook/jni/HybridData;)V
}

public class com/facebook/react/bridge/CxxModuleWrapperBase : com/facebook/react/bridge/NativeModule {
	protected fun <init> (Lcom/facebook/jni/HybridData;)V
	public fun canOverrideExistingModule ()Z
	public fun getName ()Ljava/lang/String;
	public fun initialize ()V
	public fun invalidate ()V
	protected fun resetModule (Lcom/facebook/jni/HybridData;)V
}

public final class com/facebook/react/bridge/DefaultJSExceptionHandler : com/facebook/react/bridge/JSExceptionHandler {
	public fun <init> ()V
	public fun handleException (Ljava/lang/Exception;)V
}

public abstract interface class com/facebook/react/bridge/Dynamic {
	public abstract fun asArray ()Lcom/facebook/react/bridge/ReadableArray;
	public abstract fun asBoolean ()Z
	public abstract fun asDouble ()D
	public abstract fun asInt ()I
	public abstract fun asMap ()Lcom/facebook/react/bridge/ReadableMap;
	public abstract fun asString ()Ljava/lang/String;
	public abstract fun getType ()Lcom/facebook/react/bridge/ReadableType;
	public abstract fun isNull ()Z
	public abstract fun recycle ()V
}

public class com/facebook/react/bridge/DynamicFromObject : com/facebook/react/bridge/Dynamic {
	public fun <init> (Ljava/lang/Object;)V
	public fun asArray ()Lcom/facebook/react/bridge/ReadableArray;
	public fun asBoolean ()Z
	public fun asDouble ()D
	public fun asInt ()I
	public fun asMap ()Lcom/facebook/react/bridge/ReadableMap;
	public fun asString ()Ljava/lang/String;
	public fun getType ()Lcom/facebook/react/bridge/ReadableType;
	public fun isNull ()Z
	public fun recycle ()V
}

public abstract class com/facebook/react/bridge/GuardedAsyncTask : android/os/AsyncTask {
	protected fun <init> (Lcom/facebook/react/bridge/JSExceptionHandler;)V
	protected fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	protected synthetic fun doInBackground ([Ljava/lang/Object;)Ljava/lang/Object;
	protected final fun doInBackground ([Ljava/lang/Object;)Ljava/lang/Void;
	protected abstract fun doInBackgroundGuarded ([Ljava/lang/Object;)V
}

public abstract class com/facebook/react/bridge/GuardedRunnable : java/lang/Runnable {
	public fun <init> (Lcom/facebook/react/bridge/JSExceptionHandler;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public final fun run ()V
	public abstract fun runGuarded ()V
}

public class com/facebook/react/bridge/Inspector {
	public static fun connect (ILcom/facebook/react/bridge/Inspector$RemoteConnection;)Lcom/facebook/react/bridge/Inspector$LocalConnection;
	public static fun getPages ()Ljava/util/List;
}

public class com/facebook/react/bridge/Inspector$LocalConnection {
	public fun disconnect ()V
	public fun sendMessage (Ljava/lang/String;)V
}

public class com/facebook/react/bridge/Inspector$Page {
	public fun getId ()I
	public fun getTitle ()Ljava/lang/String;
	public fun getVM ()Ljava/lang/String;
	public fun toString ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/bridge/Inspector$RemoteConnection {
	public abstract fun onDisconnect ()V
	public abstract fun onMessage (Ljava/lang/String;)V
}

public final class com/facebook/react/bridge/InvalidIteratorException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
}

public class com/facebook/react/bridge/JSApplicationCausedNativeException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
}

public final class com/facebook/react/bridge/JSApplicationIllegalArgumentException : com/facebook/react/bridge/JSApplicationCausedNativeException {
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
}

public abstract class com/facebook/react/bridge/JSBundleLoader {
	public fun <init> ()V
	public static fun createAssetLoader (Landroid/content/Context;Ljava/lang/String;Z)Lcom/facebook/react/bridge/JSBundleLoader;
	public static fun createCachedBundleFromNetworkLoader (Ljava/lang/String;Ljava/lang/String;)Lcom/facebook/react/bridge/JSBundleLoader;
	public static fun createCachedSplitBundleFromNetworkLoader (Ljava/lang/String;Ljava/lang/String;)Lcom/facebook/react/bridge/JSBundleLoader;
	public static fun createFileLoader (Ljava/lang/String;)Lcom/facebook/react/bridge/JSBundleLoader;
	public static fun createFileLoader (Ljava/lang/String;Ljava/lang/String;Z)Lcom/facebook/react/bridge/JSBundleLoader;
	public abstract fun loadScript (Lcom/facebook/react/bridge/JSBundleLoaderDelegate;)Ljava/lang/String;
}

public abstract interface class com/facebook/react/bridge/JSBundleLoaderDelegate {
	public abstract fun loadScriptFromAssets (Landroid/content/res/AssetManager;Ljava/lang/String;Z)V
	public abstract fun loadScriptFromFile (Ljava/lang/String;Ljava/lang/String;Z)V
	public abstract fun loadSplitBundleFromFile (Ljava/lang/String;Ljava/lang/String;)V
	public abstract fun setSourceURLs (Ljava/lang/String;Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/JSExceptionHandler {
	public abstract fun handleException (Ljava/lang/Exception;)V
}

public abstract interface class com/facebook/react/bridge/JSInstance {
	public abstract fun invokeCallback (ILcom/facebook/react/bridge/NativeArrayInterface;)V
}

public class com/facebook/react/bridge/JSONArguments {
	public fun <init> ()V
	public static fun fromJSONArray (Lorg/json/JSONArray;)Lcom/facebook/react/bridge/ReadableArray;
	public static fun fromJSONArrayString (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableArray;
	public static fun fromJSONObject (Lorg/json/JSONObject;)Lcom/facebook/react/bridge/ReadableMap;
	public static fun fromJSONObjectString (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableMap;
}

public final class com/facebook/react/bridge/JavaOnlyArray : com/facebook/react/bridge/ReadableArray, com/facebook/react/bridge/WritableArray {
	public static final field Companion Lcom/facebook/react/bridge/JavaOnlyArray$Companion;
	public fun <init> ()V
	public synthetic fun <init> (Ljava/util/List;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public synthetic fun <init> ([Ljava/lang/Object;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun deepClone (Lcom/facebook/react/bridge/ReadableArray;)Lcom/facebook/react/bridge/JavaOnlyArray;
	public fun equals (Ljava/lang/Object;)Z
	public static final fun from (Ljava/util/List;)Lcom/facebook/react/bridge/JavaOnlyArray;
	public fun getArray (I)Lcom/facebook/react/bridge/ReadableArray;
	public fun getBoolean (I)Z
	public fun getDouble (I)D
	public fun getDynamic (I)Lcom/facebook/react/bridge/Dynamic;
	public fun getInt (I)I
	public fun getLong (I)J
	public fun getMap (I)Lcom/facebook/react/bridge/ReadableMap;
	public fun getString (I)Ljava/lang/String;
	public fun getType (I)Lcom/facebook/react/bridge/ReadableType;
	public fun hashCode ()I
	public fun isNull (I)Z
	public static final fun of ([Ljava/lang/Object;)Lcom/facebook/react/bridge/JavaOnlyArray;
	public fun pushArray (Lcom/facebook/react/bridge/ReadableArray;)V
	public fun pushBoolean (Z)V
	public fun pushDouble (D)V
	public fun pushInt (I)V
	public fun pushLong (J)V
	public fun pushMap (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun pushNull ()V
	public fun pushString (Ljava/lang/String;)V
	public fun size ()I
	public fun toArrayList ()Ljava/util/ArrayList;
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/bridge/JavaOnlyArray$Companion {
	public final fun deepClone (Lcom/facebook/react/bridge/ReadableArray;)Lcom/facebook/react/bridge/JavaOnlyArray;
	public final fun from (Ljava/util/List;)Lcom/facebook/react/bridge/JavaOnlyArray;
	public final fun of ([Ljava/lang/Object;)Lcom/facebook/react/bridge/JavaOnlyArray;
}

public final class com/facebook/react/bridge/JavaOnlyMap : com/facebook/react/bridge/ReadableMap, com/facebook/react/bridge/WritableMap {
	public static final field Companion Lcom/facebook/react/bridge/JavaOnlyMap$Companion;
	public fun <init> ()V
	public synthetic fun <init> ([Ljava/lang/Object;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun copy ()Lcom/facebook/react/bridge/WritableMap;
	public static final fun deepClone (Lcom/facebook/react/bridge/ReadableMap;)Lcom/facebook/react/bridge/JavaOnlyMap;
	public fun equals (Ljava/lang/Object;)Z
	public static final fun from (Ljava/util/Map;)Lcom/facebook/react/bridge/JavaOnlyMap;
	public fun getArray (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableArray;
	public fun getBoolean (Ljava/lang/String;)Z
	public fun getDouble (Ljava/lang/String;)D
	public fun getDynamic (Ljava/lang/String;)Lcom/facebook/react/bridge/Dynamic;
	public fun getEntryIterator ()Ljava/util/Iterator;
	public fun getInt (Ljava/lang/String;)I
	public fun getLong (Ljava/lang/String;)J
	public fun getMap (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableMap;
	public fun getString (Ljava/lang/String;)Ljava/lang/String;
	public fun getType (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableType;
	public fun hasKey (Ljava/lang/String;)Z
	public fun hashCode ()I
	public fun isNull (Ljava/lang/String;)Z
	public fun keySetIterator ()Lcom/facebook/react/bridge/ReadableMapKeySetIterator;
	public fun merge (Lcom/facebook/react/bridge/ReadableMap;)V
	public static final fun of ([Ljava/lang/Object;)Lcom/facebook/react/bridge/JavaOnlyMap;
	public fun putArray (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun putBoolean (Ljava/lang/String;Z)V
	public fun putDouble (Ljava/lang/String;D)V
	public fun putInt (Ljava/lang/String;I)V
	public fun putLong (Ljava/lang/String;J)V
	public fun putMap (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun putNull (Ljava/lang/String;)V
	public fun putString (Ljava/lang/String;Ljava/lang/String;)V
	public final fun remove (Ljava/lang/String;)V
	public fun toHashMap ()Ljava/util/HashMap;
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/bridge/JavaOnlyMap$Companion {
	public final fun deepClone (Lcom/facebook/react/bridge/ReadableMap;)Lcom/facebook/react/bridge/JavaOnlyMap;
	public final fun from (Ljava/util/Map;)Lcom/facebook/react/bridge/JavaOnlyMap;
	public final fun of ([Ljava/lang/Object;)Lcom/facebook/react/bridge/JavaOnlyMap;
}

public class com/facebook/react/bridge/JavaScriptContextHolder {
	public fun <init> (J)V
	public fun clear ()V
	public fun get ()J
}

public abstract class com/facebook/react/bridge/JavaScriptExecutor {
	protected fun <init> (Lcom/facebook/jni/HybridData;)V
	public fun close ()V
	public abstract fun getName ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/bridge/JavaScriptExecutorFactory {
	public abstract fun create ()Lcom/facebook/react/bridge/JavaScriptExecutor;
	public abstract fun startSamplingProfiler ()V
	public abstract fun stopSamplingProfiler (Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/JavaScriptModule {
}

public final class com/facebook/react/bridge/JavaScriptModuleRegistry {
	public fun <init> ()V
	public static fun getJSModuleName (Ljava/lang/Class;)Ljava/lang/String;
	public fun getJavaScriptModule (Lcom/facebook/react/bridge/CatalystInstance;Ljava/lang/Class;)Lcom/facebook/react/bridge/JavaScriptModule;
}

public class com/facebook/react/bridge/JsonWriterHelper {
	public fun <init> ()V
	public static fun readableArrayValue (Landroid/util/JsonWriter;Lcom/facebook/react/bridge/ReadableArray;)V
	public static fun value (Landroid/util/JsonWriter;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/bridge/LifecycleEventListener {
	public abstract fun onHostDestroy ()V
	public abstract fun onHostPause ()V
	public abstract fun onHostResume ()V
}

public final class com/facebook/react/bridge/MemoryPressure : java/lang/Enum {
	public static final field CRITICAL Lcom/facebook/react/bridge/MemoryPressure;
	public static final field MODERATE Lcom/facebook/react/bridge/MemoryPressure;
	public static final field UI_HIDDEN Lcom/facebook/react/bridge/MemoryPressure;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/bridge/MemoryPressure;
	public static fun values ()[Lcom/facebook/react/bridge/MemoryPressure;
}

public abstract interface class com/facebook/react/bridge/MemoryPressureListener {
	public abstract fun handleMemoryPressure (I)V
}

public class com/facebook/react/bridge/ModuleHolder {
	public fun <init> (Lcom/facebook/react/bridge/NativeModule;)V
	public fun <init> (Lcom/facebook/react/module/model/ReactModuleInfo;Ljavax/inject/Provider;)V
	public fun destroy ()V
	public fun getCanOverrideExistingModule ()Z
	public fun getClassName ()Ljava/lang/String;
	public fun getModule ()Lcom/facebook/react/bridge/NativeModule;
	public fun getName ()Ljava/lang/String;
	public fun isCxxModule ()Z
	public fun isTurboModule ()Z
}

public class com/facebook/react/bridge/ModuleSpec {
	public fun getName ()Ljava/lang/String;
	public fun getProvider ()Ljavax/inject/Provider;
	public static fun nativeModuleSpec (Ljava/lang/Class;Ljavax/inject/Provider;)Lcom/facebook/react/bridge/ModuleSpec;
	public static fun nativeModuleSpec (Ljava/lang/String;Ljavax/inject/Provider;)Lcom/facebook/react/bridge/ModuleSpec;
	public static fun viewManagerSpec (Ljavax/inject/Provider;)Lcom/facebook/react/bridge/ModuleSpec;
}

public abstract class com/facebook/react/bridge/NativeArray : com/facebook/jni/HybridClassBase, com/facebook/react/bridge/NativeArrayInterface {
	protected fun <init> ()V
	public fun toString ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/bridge/NativeArrayInterface {
	public abstract fun toString ()Ljava/lang/String;
}

public abstract class com/facebook/react/bridge/NativeMap : com/facebook/jni/HybridClassBase {
	public fun <init> ()V
	public fun toString ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/bridge/NativeModule {
	public fun canOverrideExistingModule ()Z
	public abstract fun getName ()Ljava/lang/String;
	public abstract fun initialize ()V
	public abstract fun invalidate ()V
	public fun onCatalystInstanceDestroy ()V
}

public class com/facebook/react/bridge/NativeModuleRegistry {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/Map;)V
	public fun getAllModules ()Ljava/util/List;
	public fun getModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/NativeModule;
	public fun getModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public fun hasModule (Ljava/lang/Class;)Z
	public fun hasModule (Ljava/lang/String;)Z
	public fun onBatchComplete ()V
}

public final class com/facebook/react/bridge/NoSuchKeyException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener {
	public abstract fun onBridgeDestroyed ()V
	public abstract fun onTransitionToBridgeBusy ()V
	public abstract fun onTransitionToBridgeIdle ()V
}

public abstract interface class com/facebook/react/bridge/OnBatchCompleteListener {
	public abstract fun onBatchComplete ()V
}

public abstract interface class com/facebook/react/bridge/PerformanceCounter {
	public abstract fun getPerformanceCounters ()Ljava/util/Map;
	public abstract fun profileNextBatch ()V
}

public abstract interface class com/facebook/react/bridge/Promise {
	public abstract fun reject (Ljava/lang/String;)V
	public abstract fun reject (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun reject (Ljava/lang/String;Ljava/lang/String;)V
	public abstract fun reject (Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun reject (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V
	public abstract fun reject (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun reject (Ljava/lang/String;Ljava/lang/Throwable;)V
	public abstract fun reject (Ljava/lang/String;Ljava/lang/Throwable;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun reject (Ljava/lang/Throwable;)V
	public abstract fun reject (Ljava/lang/Throwable;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun resolve (Ljava/lang/Object;)V
}

public class com/facebook/react/bridge/PromiseImpl : com/facebook/react/bridge/Promise {
	public fun <init> (Lcom/facebook/react/bridge/Callback;Lcom/facebook/react/bridge/Callback;)V
	public fun reject (Ljava/lang/String;)V
	public fun reject (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun reject (Ljava/lang/String;Ljava/lang/String;)V
	public fun reject (Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun reject (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun reject (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;Lcom/facebook/react/bridge/WritableMap;)V
	public fun reject (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun reject (Ljava/lang/String;Ljava/lang/Throwable;Lcom/facebook/react/bridge/WritableMap;)V
	public fun reject (Ljava/lang/Throwable;)V
	public fun reject (Ljava/lang/Throwable;Lcom/facebook/react/bridge/WritableMap;)V
	public fun resolve (Ljava/lang/Object;)V
}

public abstract class com/facebook/react/bridge/ReactApplicationContext : com/facebook/react/bridge/ReactContext {
	public fun <init> (Landroid/content/Context;)V
}

public abstract class com/facebook/react/bridge/ReactContext : android/content/ContextWrapper {
	protected field mInteropModuleRegistry Lcom/facebook/react/bridge/interop/InteropModuleRegistry;
	public fun <init> (Landroid/content/Context;)V
	public fun addActivityEventListener (Lcom/facebook/react/bridge/ActivityEventListener;)V
	public fun addLifecycleEventListener (Lcom/facebook/react/bridge/LifecycleEventListener;)V
	public fun addWindowFocusChangeListener (Lcom/facebook/react/bridge/WindowFocusChangeListener;)V
	public fun assertOnJSQueueThread ()V
	public fun assertOnNativeModulesQueueThread ()V
	public fun assertOnNativeModulesQueueThread (Ljava/lang/String;)V
	public fun assertOnUiQueueThread ()V
	public abstract fun destroy ()V
	public fun emitDeviceEvent (Ljava/lang/String;)V
	public fun emitDeviceEvent (Ljava/lang/String;Ljava/lang/Object;)V
	public abstract fun getCatalystInstance ()Lcom/facebook/react/bridge/CatalystInstance;
	public fun getCurrentActivity ()Landroid/app/Activity;
	public fun getExceptionHandler ()Lcom/facebook/react/bridge/JSExceptionHandler;
	public abstract fun getFabricUIManager ()Lcom/facebook/react/bridge/UIManager;
	public abstract fun getJSCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/CallInvokerHolder;
	public fun getJSExceptionHandler ()Lcom/facebook/react/bridge/JSExceptionHandler;
	public fun getJSMessageQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public abstract fun getJSModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/JavaScriptModule;
	public abstract fun getJavaScriptContextHolder ()Lcom/facebook/react/bridge/JavaScriptContextHolder;
	public fun getLifecycleState ()Lcom/facebook/react/common/LifecycleState;
	public abstract fun getNativeModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/NativeModule;
	public abstract fun getNativeModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public abstract fun getNativeModules ()Ljava/util/Collection;
	public fun getNativeModulesMessageQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public abstract fun getSourceURL ()Ljava/lang/String;
	public fun getSystemService (Ljava/lang/String;)Ljava/lang/Object;
	public fun getUiMessageQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public abstract fun handleException (Ljava/lang/Exception;)V
	public abstract fun hasActiveCatalystInstance ()Z
	public abstract fun hasActiveReactInstance ()Z
	public abstract fun hasCatalystInstance ()Z
	public fun hasCurrentActivity ()Z
	public abstract fun hasNativeModule (Ljava/lang/Class;)Z
	public abstract fun hasReactInstance ()Z
	protected fun initializeFromOther (Lcom/facebook/react/bridge/ReactContext;)V
	protected fun initializeInteropModules ()V
	public fun initializeMessageQueueThreads (Lcom/facebook/react/bridge/queue/ReactQueueConfiguration;)V
	public fun internal_registerInteropModule (Ljava/lang/Class;Ljava/lang/Object;)V
	public abstract fun isBridgeless ()Z
	public fun isOnJSQueueThread ()Z
	public fun isOnNativeModulesQueueThread ()Z
	public fun isOnUiQueueThread ()Z
	public fun onActivityResult (Landroid/app/Activity;IILandroid/content/Intent;)V
	public fun onHostDestroy ()V
	public fun onHostDestroy (Z)V
	public fun onHostPause ()V
	public fun onHostResume (Landroid/app/Activity;)V
	public fun onNewIntent (Landroid/app/Activity;Landroid/content/Intent;)V
	public fun onUserLeaveHint (Landroid/app/Activity;)V
	public fun onWindowFocusChange (Z)V
	public abstract fun registerSegment (ILjava/lang/String;Lcom/facebook/react/bridge/Callback;)V
	public fun removeActivityEventListener (Lcom/facebook/react/bridge/ActivityEventListener;)V
	public fun removeLifecycleEventListener (Lcom/facebook/react/bridge/LifecycleEventListener;)V
	public fun removeWindowFocusChangeListener (Lcom/facebook/react/bridge/WindowFocusChangeListener;)V
	public fun resetPerfStats ()V
	public fun runOnJSQueueThread (Ljava/lang/Runnable;)Z
	public fun runOnNativeModulesQueueThread (Ljava/lang/Runnable;)V
	public fun runOnUiQueueThread (Ljava/lang/Runnable;)V
	public fun setJSExceptionHandler (Lcom/facebook/react/bridge/JSExceptionHandler;)V
	public fun startActivityForResult (Landroid/content/Intent;ILandroid/os/Bundle;)Z
}

public class com/facebook/react/bridge/ReactContext$ExceptionHandlerWrapper : com/facebook/react/bridge/JSExceptionHandler {
	public fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public fun handleException (Ljava/lang/Exception;)V
}

public abstract interface class com/facebook/react/bridge/ReactContext$RCTDeviceEventEmitter : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun emit (Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract class com/facebook/react/bridge/ReactContextBaseJavaModule : com/facebook/react/bridge/BaseJavaModule {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	protected final fun getCurrentActivity ()Landroid/app/Activity;
}

public class com/facebook/react/bridge/ReactCxxErrorHandler {
	public fun <init> ()V
	public static fun setHandleErrorFunc (Ljava/lang/Object;Ljava/lang/reflect/Method;)V
}

public final class com/facebook/react/bridge/ReactIgnorableMountingException : java/lang/RuntimeException {
	public static final field Companion Lcom/facebook/react/bridge/ReactIgnorableMountingException$Companion;
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun <init> (Ljava/lang/Throwable;)V
	public static final fun isIgnorable (Ljava/lang/Throwable;)Z
}

public final class com/facebook/react/bridge/ReactIgnorableMountingException$Companion {
	public final fun isIgnorable (Ljava/lang/Throwable;)Z
}

public class com/facebook/react/bridge/ReactInstanceManagerInspectorTarget : java/lang/AutoCloseable {
	public fun <init> (Lcom/facebook/react/bridge/ReactInstanceManagerInspectorTarget$TargetDelegate;)V
	public fun close ()V
	public fun sendDebuggerResumeCommand ()V
}

public abstract interface class com/facebook/react/bridge/ReactInstanceManagerInspectorTarget$TargetDelegate {
	public abstract fun getMetadata ()Ljava/util/Map;
	public abstract fun loadNetworkResource (Ljava/lang/String;Lcom/facebook/react/devsupport/inspector/InspectorNetworkRequestListener;)V
	public abstract fun onReload ()V
	public abstract fun onSetPausedInDebuggerMessage (Ljava/lang/String;)V
}

public class com/facebook/react/bridge/ReactMarker {
	public fun <init> ()V
	public static fun addFabricListener (Lcom/facebook/react/bridge/ReactMarker$FabricMarkerListener;)V
	public static fun addListener (Lcom/facebook/react/bridge/ReactMarker$MarkerListener;)V
	public static fun clearFabricMarkerListeners ()V
	public static fun clearMarkerListeners ()V
	public static fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;I)V
	public static fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;IJ)V
	public static fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;IJI)V
	public static fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;)V
	public static fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;I)V
	public static fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;J)V
	public static fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;)V
	public static fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;I)V
	public static fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;ILjava/lang/Long;)V
	public static fun logMarker (Ljava/lang/String;)V
	public static fun logMarker (Ljava/lang/String;I)V
	public static fun logMarker (Ljava/lang/String;Ljava/lang/String;)V
	public static fun logMarker (Ljava/lang/String;Ljava/lang/String;I)V
	public static fun removeFabricListener (Lcom/facebook/react/bridge/ReactMarker$FabricMarkerListener;)V
	public static fun removeListener (Lcom/facebook/react/bridge/ReactMarker$MarkerListener;)V
}

public abstract interface class com/facebook/react/bridge/ReactMarker$FabricMarkerListener {
	public abstract fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;IJ)V
	public fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;IJI)V
}

public abstract interface class com/facebook/react/bridge/ReactMarker$MarkerListener {
	public abstract fun logMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;I)V
}

public final class com/facebook/react/bridge/ReactMarkerConstants : java/lang/Enum {
	public static final field APP_STARTUP_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field APP_STARTUP_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ATTACH_MEASURED_ROOT_VIEWS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ATTACH_MEASURED_ROOT_VIEWS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field BUILD_NATIVE_MODULE_REGISTRY_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field BUILD_NATIVE_MODULE_REGISTRY_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field BUILD_REACT_INSTANCE_MANAGER_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field BUILD_REACT_INSTANCE_MANAGER_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CHANGE_THREAD_PRIORITY Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CONTENT_APPEARED Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CONVERT_CONSTANTS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CONVERT_CONSTANTS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_CATALYST_INSTANCE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_CATALYST_INSTANCE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_I18N_MODULE_CONSTANTS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_I18N_MODULE_CONSTANTS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_MC_MODULE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_MC_MODULE_GET_METADATA_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_MC_MODULE_GET_METADATA_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_MC_MODULE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_MODULE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_MODULE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_REACT_CONTEXT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_REACT_CONTEXT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_UI_MANAGER_MODULE_CONSTANTS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_UI_MANAGER_MODULE_CONSTANTS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_UI_MANAGER_MODULE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_UI_MANAGER_MODULE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_VIEW_MANAGERS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field CREATE_VIEW_MANAGERS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field DESTROY_CATALYST_INSTANCE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field DESTROY_CATALYST_INSTANCE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field DOWNLOAD_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field DOWNLOAD_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_BATCH_EXECUTION_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_BATCH_EXECUTION_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_COMMIT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_COMMIT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_DIFF_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_DIFF_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_FINISH_TRANSACTION_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_FINISH_TRANSACTION_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_LAYOUT_AFFECTED_NODES Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_LAYOUT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_LAYOUT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_UPDATE_UI_MAIN_THREAD_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field FABRIC_UPDATE_UI_MAIN_THREAD_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field GET_CONSTANTS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field GET_CONSTANTS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field GET_REACT_INSTANCE_HOLDER_SPEC_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field GET_REACT_INSTANCE_HOLDER_SPEC_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field GET_REACT_INSTANCE_MANAGER_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field GET_REACT_INSTANCE_MANAGER_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field I18N_MODULE_CONSTANTS_CONVERT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field I18N_MODULE_CONSTANTS_CONVERT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field INITIALIZE_MODULE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field INITIALIZE_MODULE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field INIT_REACT_RUNTIME_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field INIT_REACT_RUNTIME_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field JAVASCRIPT_EXECUTOR_FACTORY_INJECT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field JAVASCRIPT_EXECUTOR_FACTORY_INJECT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field LOAD_REACT_NATIVE_FABRIC_SO_FILE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field LOAD_REACT_NATIVE_FABRIC_SO_FILE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field LOAD_REACT_NATIVE_SO_FILE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field LOAD_REACT_NATIVE_SO_FILE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field NATIVE_MODULE_INITIALIZE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field NATIVE_MODULE_INITIALIZE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field NATIVE_MODULE_SETUP_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field NATIVE_MODULE_SETUP_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_FRAGMENT_CREATE Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_HOST_PAUSE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_HOST_PAUSE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_HOST_RESUME_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_HOST_RESUME_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_USER_LEAVE_HINT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ON_USER_LEAVE_HINT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PRE_REACT_CONTEXT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PRE_RUN_JS_BUNDLE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PRE_SETUP_REACT_CONTEXT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PRE_SETUP_REACT_CONTEXT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_CORE_REACT_PACKAGE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_CORE_REACT_PACKAGE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_INFRA_PACKAGE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_INFRA_PACKAGE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_PACKAGES_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_PACKAGES_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_PRODUCT_PACKAGE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field PROCESS_PRODUCT_PACKAGE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REACT_BRIDGELESS_LOADING_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REACT_BRIDGELESS_LOADING_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REACT_BRIDGE_LOADING_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REACT_BRIDGE_LOADING_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REACT_CONTEXT_THREAD_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REACT_CONTEXT_THREAD_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REGISTER_JS_SEGMENT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field REGISTER_JS_SEGMENT_STOP Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field RELOAD Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ROOT_VIEW_ATTACH_TO_REACT_INSTANCE_MANAGER_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ROOT_VIEW_ATTACH_TO_REACT_INSTANCE_MANAGER_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ROOT_VIEW_ON_MEASURE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ROOT_VIEW_ON_MEASURE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ROOT_VIEW_UPDATE_LAYOUT_SPECS_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field ROOT_VIEW_UPDATE_LAYOUT_SPECS_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field RUN_JS_BUNDLE_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field RUN_JS_BUNDLE_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field SETUP_REACT_CONTEXT_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field SETUP_REACT_CONTEXT_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field UNPACKING_JS_BUNDLE_LOADER_BLOCKED Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field UNPACKING_JS_BUNDLE_LOADER_CHECK_END Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field UNPACKING_JS_BUNDLE_LOADER_CHECK_START Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field UNPACKING_JS_BUNDLE_LOADER_EXTRACTED Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field VM_INIT Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field loadApplicationScript_endStringConvert Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static final field loadApplicationScript_startStringConvert Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public final fun hasMatchingNameMarker ()Z
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/bridge/ReactMarkerConstants;
	public static fun values ()[Lcom/facebook/react/bridge/ReactMarkerConstants;
}

public abstract interface annotation class com/facebook/react/bridge/ReactMethod : java/lang/annotation/Annotation {
	public abstract fun isBlockingSynchronousMethod ()Z
}

public abstract interface class com/facebook/react/bridge/ReactModuleWithSpec {
}

public final class com/facebook/react/bridge/ReactNoCrashBridgeNotAllowedSoftException : com/facebook/react/bridge/ReactNoCrashSoftException {
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun <init> (Ljava/lang/Throwable;)V
}

public class com/facebook/react/bridge/ReactNoCrashSoftException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun <init> (Ljava/lang/Throwable;)V
}

public final class com/facebook/react/bridge/ReactSoftExceptionLogger {
	public static final field INSTANCE Lcom/facebook/react/bridge/ReactSoftExceptionLogger;
	public static final fun addListener (Lcom/facebook/react/bridge/ReactSoftExceptionLogger$ReactSoftExceptionListener;)V
	public static final fun clearListeners ()V
	public static final fun logSoftException (Ljava/lang/String;Ljava/lang/Throwable;)V
	public static final fun logSoftExceptionVerbose (Ljava/lang/String;Ljava/lang/Throwable;)V
	public static final fun removeListener (Lcom/facebook/react/bridge/ReactSoftExceptionLogger$ReactSoftExceptionListener;)V
}

public final class com/facebook/react/bridge/ReactSoftExceptionLogger$Categories {
	public static final field INSTANCE Lcom/facebook/react/bridge/ReactSoftExceptionLogger$Categories;
	public static final field RVG_IS_VIEW_CLIPPED Ljava/lang/String;
	public static final field RVG_ON_VIEW_REMOVED Ljava/lang/String;
	public static final field SOFT_ASSERTIONS Ljava/lang/String;
	public static final field SURFACE_MOUNTING_MANAGER_MISSING_VIEWSTATE Ljava/lang/String;
}

public abstract interface class com/facebook/react/bridge/ReactSoftExceptionLogger$ReactSoftExceptionListener {
	public abstract fun logSoftException (Ljava/lang/String;Ljava/lang/Throwable;)V
}

public abstract interface class com/facebook/react/bridge/ReadableArray {
	public abstract fun getArray (I)Lcom/facebook/react/bridge/ReadableArray;
	public abstract fun getBoolean (I)Z
	public abstract fun getDouble (I)D
	public abstract fun getDynamic (I)Lcom/facebook/react/bridge/Dynamic;
	public abstract fun getInt (I)I
	public abstract fun getLong (I)J
	public abstract fun getMap (I)Lcom/facebook/react/bridge/ReadableMap;
	public abstract fun getString (I)Ljava/lang/String;
	public abstract fun getType (I)Lcom/facebook/react/bridge/ReadableType;
	public abstract fun isNull (I)Z
	public abstract fun size ()I
	public abstract fun toArrayList ()Ljava/util/ArrayList;
}

public abstract interface class com/facebook/react/bridge/ReadableMap {
	public abstract fun getArray (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableArray;
	public abstract fun getBoolean (Ljava/lang/String;)Z
	public abstract fun getDouble (Ljava/lang/String;)D
	public abstract fun getDynamic (Ljava/lang/String;)Lcom/facebook/react/bridge/Dynamic;
	public abstract fun getEntryIterator ()Ljava/util/Iterator;
	public abstract fun getInt (Ljava/lang/String;)I
	public abstract fun getLong (Ljava/lang/String;)J
	public abstract fun getMap (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableMap;
	public abstract fun getString (Ljava/lang/String;)Ljava/lang/String;
	public abstract fun getType (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableType;
	public abstract fun hasKey (Ljava/lang/String;)Z
	public abstract fun isNull (Ljava/lang/String;)Z
	public abstract fun keySetIterator ()Lcom/facebook/react/bridge/ReadableMapKeySetIterator;
	public abstract fun toHashMap ()Ljava/util/HashMap;
}

public abstract interface class com/facebook/react/bridge/ReadableMapKeySetIterator {
	public abstract fun hasNextKey ()Z
	public abstract fun nextKey ()Ljava/lang/String;
}

public class com/facebook/react/bridge/ReadableNativeArray : com/facebook/react/bridge/NativeArray, com/facebook/react/bridge/ReadableArray {
	protected fun <init> ()V
	public fun equals (Ljava/lang/Object;)Z
	public synthetic fun getArray (I)Lcom/facebook/react/bridge/ReadableArray;
	public fun getArray (I)Lcom/facebook/react/bridge/ReadableNativeArray;
	public fun getBoolean (I)Z
	public fun getDouble (I)D
	public fun getDynamic (I)Lcom/facebook/react/bridge/Dynamic;
	public fun getInt (I)I
	public static final fun getJNIPassCounter ()I
	public fun getLong (I)J
	public synthetic fun getMap (I)Lcom/facebook/react/bridge/ReadableMap;
	public fun getMap (I)Lcom/facebook/react/bridge/ReadableNativeMap;
	public fun getString (I)Ljava/lang/String;
	public fun getType (I)Lcom/facebook/react/bridge/ReadableType;
	public fun hashCode ()I
	public fun isNull (I)Z
	public fun size ()I
	public fun toArrayList ()Ljava/util/ArrayList;
}

public class com/facebook/react/bridge/ReadableNativeMap : com/facebook/react/bridge/NativeMap, com/facebook/react/bridge/ReadableMap {
	protected fun <init> ()V
	public fun equals (Ljava/lang/Object;)Z
	public fun getArray (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableArray;
	public fun getBoolean (Ljava/lang/String;)Z
	public fun getDouble (Ljava/lang/String;)D
	public fun getDynamic (Ljava/lang/String;)Lcom/facebook/react/bridge/Dynamic;
	public fun getEntryIterator ()Ljava/util/Iterator;
	public fun getInt (Ljava/lang/String;)I
	public static final fun getJNIPassCounter ()I
	public fun getLong (Ljava/lang/String;)J
	public synthetic fun getMap (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableMap;
	public fun getMap (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableNativeMap;
	public fun getString (Ljava/lang/String;)Ljava/lang/String;
	public fun getType (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableType;
	public fun hasKey (Ljava/lang/String;)Z
	public fun hashCode ()I
	public fun isNull (Ljava/lang/String;)Z
	public fun keySetIterator ()Lcom/facebook/react/bridge/ReadableMapKeySetIterator;
	public fun toHashMap ()Ljava/util/HashMap;
}

public final class com/facebook/react/bridge/ReadableType : java/lang/Enum {
	public static final field Array Lcom/facebook/react/bridge/ReadableType;
	public static final field Boolean Lcom/facebook/react/bridge/ReadableType;
	public static final field Map Lcom/facebook/react/bridge/ReadableType;
	public static final field Null Lcom/facebook/react/bridge/ReadableType;
	public static final field Number Lcom/facebook/react/bridge/ReadableType;
	public static final field String Lcom/facebook/react/bridge/ReadableType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableType;
	public static fun values ()[Lcom/facebook/react/bridge/ReadableType;
}

public final class com/facebook/react/bridge/RetryableMountingLayerException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun <init> (Ljava/lang/Throwable;)V
}

public final class com/facebook/react/bridge/RuntimeExecutor : com/facebook/jni/HybridClassBase {
}

public final class com/facebook/react/bridge/RuntimeScheduler : com/facebook/jni/HybridClassBase {
}

public final class com/facebook/react/bridge/SoftAssertions {
	public static final field INSTANCE Lcom/facebook/react/bridge/SoftAssertions;
	public static final fun assertCondition (ZLjava/lang/String;)V
	public static final fun assertNotNull (Ljava/lang/Object;)Ljava/lang/Object;
	public static final fun assertUnreachable (Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/Systrace : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun setEnabled (Z)V
}

public abstract interface class com/facebook/react/bridge/UIManager : com/facebook/react/bridge/PerformanceCounter {
	public abstract fun addRootView (Landroid/view/View;Lcom/facebook/react/bridge/WritableMap;)I
	public abstract fun addUIManagerEventListener (Lcom/facebook/react/bridge/UIManagerListener;)V
	public abstract fun dispatchCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun dispatchCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun getEventDispatcher ()Lcom/facebook/react/uimanager/events/EventDispatcher;
	public abstract fun initialize ()V
	public abstract fun invalidate ()V
	public abstract fun markActiveTouchForTag (II)V
	public abstract fun receiveEvent (IILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun receiveEvent (ILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun removeUIManagerEventListener (Lcom/facebook/react/bridge/UIManagerListener;)V
	public abstract fun resolveCustomDirectEventName (Ljava/lang/String;)Ljava/lang/String;
	public abstract fun resolveView (I)Landroid/view/View;
	public abstract fun sendAccessibilityEvent (II)V
	public abstract fun startSurface (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;II)I
	public abstract fun stopSurface (I)V
	public abstract fun sweepActiveTouchForTag (II)V
	public abstract fun synchronouslyUpdateViewOnUIThread (ILcom/facebook/react/bridge/ReadableMap;)V
	public abstract fun updateRootLayoutSpecs (IIIII)V
}

public abstract interface class com/facebook/react/bridge/UIManagerProvider {
	public abstract fun createUIManager (Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/bridge/UIManager;
}

public class com/facebook/react/bridge/UiThreadUtil {
	public fun <init> ()V
	public static fun assertNotOnUiThread ()V
	public static fun assertOnUiThread ()V
	public static fun getUiThreadHandler ()Landroid/os/Handler;
	public static fun isOnUiThread ()Z
	public static fun removeOnUiThread (Ljava/lang/Runnable;)V
	public static fun runOnUiThread (Ljava/lang/Runnable;)Z
	public static fun runOnUiThread (Ljava/lang/Runnable;J)Z
}

public final class com/facebook/react/bridge/UnexpectedNativeTypeException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/WindowFocusChangeListener {
	public abstract fun onWindowFocusChange (Z)V
}

public abstract interface class com/facebook/react/bridge/WritableArray : com/facebook/react/bridge/ReadableArray {
	public abstract fun pushArray (Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun pushBoolean (Z)V
	public abstract fun pushDouble (D)V
	public abstract fun pushInt (I)V
	public abstract fun pushLong (J)V
	public abstract fun pushMap (Lcom/facebook/react/bridge/ReadableMap;)V
	public abstract fun pushNull ()V
	public abstract fun pushString (Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/WritableMap : com/facebook/react/bridge/ReadableMap {
	public abstract fun copy ()Lcom/facebook/react/bridge/WritableMap;
	public abstract fun merge (Lcom/facebook/react/bridge/ReadableMap;)V
	public abstract fun putArray (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun putBoolean (Ljava/lang/String;Z)V
	public abstract fun putDouble (Ljava/lang/String;D)V
	public abstract fun putInt (Ljava/lang/String;I)V
	public abstract fun putLong (Ljava/lang/String;J)V
	public abstract fun putMap (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	public abstract fun putNull (Ljava/lang/String;)V
	public abstract fun putString (Ljava/lang/String;Ljava/lang/String;)V
}

public final class com/facebook/react/bridge/WritableNativeArray : com/facebook/react/bridge/ReadableNativeArray, com/facebook/react/bridge/WritableArray {
	public fun <init> ()V
	public fun pushArray (Lcom/facebook/react/bridge/ReadableArray;)V
	public fun pushBoolean (Z)V
	public fun pushDouble (D)V
	public fun pushInt (I)V
	public fun pushLong (J)V
	public fun pushMap (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun pushNull ()V
	public fun pushString (Ljava/lang/String;)V
}

public final class com/facebook/react/bridge/WritableNativeMap : com/facebook/react/bridge/ReadableNativeMap, com/facebook/react/bridge/WritableMap {
	public fun <init> ()V
	public fun copy ()Lcom/facebook/react/bridge/WritableMap;
	public fun merge (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun putArray (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun putBoolean (Ljava/lang/String;Z)V
	public fun putDouble (Ljava/lang/String;D)V
	public fun putInt (Ljava/lang/String;I)V
	public fun putLong (Ljava/lang/String;J)V
	public fun putMap (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun putNull (Ljava/lang/String;)V
	public fun putString (Ljava/lang/String;Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/bridge/queue/MessageQueueThread {
	public abstract fun assertIsOnThread ()V
	public abstract fun assertIsOnThread (Ljava/lang/String;)V
	public abstract fun callOnQueue (Ljava/util/concurrent/Callable;)Ljava/util/concurrent/Future;
	public abstract fun getPerfStats ()Lcom/facebook/react/bridge/queue/MessageQueueThreadPerfStats;
	public abstract fun isIdle ()Z
	public abstract fun isOnThread ()Z
	public abstract fun quitSynchronous ()V
	public abstract fun resetPerfStats ()V
	public abstract fun runOnQueue (Ljava/lang/Runnable;)Z
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadHandler : android/os/Handler {
	public fun <init> (Landroid/os/Looper;Lcom/facebook/react/bridge/queue/QueueThreadExceptionHandler;)V
	public fun dispatchMessage (Landroid/os/Message;)V
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadImpl : com/facebook/react/bridge/queue/MessageQueueThread {
	public static final field Companion Lcom/facebook/react/bridge/queue/MessageQueueThreadImpl$Companion;
	public synthetic fun <init> (Ljava/lang/String;Landroid/os/Looper;Lcom/facebook/react/bridge/queue/QueueThreadExceptionHandler;Lcom/facebook/react/bridge/queue/MessageQueueThreadPerfStats;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun assertIsOnThread ()V
	public fun assertIsOnThread (Ljava/lang/String;)V
	public fun callOnQueue (Ljava/util/concurrent/Callable;)Ljava/util/concurrent/Future;
	public static final fun create (Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;Lcom/facebook/react/bridge/queue/QueueThreadExceptionHandler;)Lcom/facebook/react/bridge/queue/MessageQueueThreadImpl;
	public final fun getLooper ()Landroid/os/Looper;
	public final fun getName ()Ljava/lang/String;
	public fun getPerfStats ()Lcom/facebook/react/bridge/queue/MessageQueueThreadPerfStats;
	public fun isIdle ()Z
	public fun isOnThread ()Z
	public fun quitSynchronous ()V
	public fun resetPerfStats ()V
	public fun runOnQueue (Ljava/lang/Runnable;)Z
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadImpl$Companion {
	public final fun create (Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;Lcom/facebook/react/bridge/queue/QueueThreadExceptionHandler;)Lcom/facebook/react/bridge/queue/MessageQueueThreadImpl;
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadPerfStats {
	public field cpuTime J
	public field wallTime J
	public fun <init> ()V
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadSpec {
	public static final field Companion Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$Companion;
	public static final field DEFAULT_STACK_SIZE_BYTES J
	public synthetic fun <init> (Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType;Ljava/lang/String;JLkotlin/jvm/internal/DefaultConstructorMarker;)V
	public final fun getName ()Ljava/lang/String;
	public final fun getStackSize ()J
	public final fun getThreadType ()Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType;
	public static final fun mainThreadSpec ()Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public static final fun newBackgroundThreadSpec (Ljava/lang/String;)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public static final fun newBackgroundThreadSpec (Ljava/lang/String;J)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public static final fun newUIBackgroundTreadSpec (Ljava/lang/String;)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadSpec$Companion {
	public final fun mainThreadSpec ()Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public final fun newBackgroundThreadSpec (Ljava/lang/String;)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public final fun newBackgroundThreadSpec (Ljava/lang/String;J)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public final fun newUIBackgroundTreadSpec (Ljava/lang/String;)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
}

public final class com/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType : java/lang/Enum {
	public static final field MAIN_UI Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType;
	public static final field NEW_BACKGROUND Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType;
	public static fun values ()[Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec$ThreadType;
}

public abstract interface class com/facebook/react/bridge/queue/QueueThreadExceptionHandler {
	public abstract fun handleException (Ljava/lang/Exception;)V
}

public abstract interface class com/facebook/react/bridge/queue/ReactQueueConfiguration {
	public abstract fun destroy ()V
	public abstract fun getJSQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public abstract fun getNativeModulesQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public abstract fun getUIQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
}

public final class com/facebook/react/bridge/queue/ReactQueueConfigurationImpl : com/facebook/react/bridge/queue/ReactQueueConfiguration {
	public static final field Companion Lcom/facebook/react/bridge/queue/ReactQueueConfigurationImpl$Companion;
	public synthetic fun <init> (Lcom/facebook/react/bridge/queue/MessageQueueThreadImpl;Lcom/facebook/react/bridge/queue/MessageQueueThreadImpl;Lcom/facebook/react/bridge/queue/MessageQueueThreadImpl;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun create (Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec;Lcom/facebook/react/bridge/queue/QueueThreadExceptionHandler;)Lcom/facebook/react/bridge/queue/ReactQueueConfigurationImpl;
	public fun destroy ()V
	public fun getJSQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public fun getNativeModulesQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
	public fun getUIQueueThread ()Lcom/facebook/react/bridge/queue/MessageQueueThread;
}

public final class com/facebook/react/bridge/queue/ReactQueueConfigurationImpl$Companion {
	public final fun create (Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec;Lcom/facebook/react/bridge/queue/QueueThreadExceptionHandler;)Lcom/facebook/react/bridge/queue/ReactQueueConfigurationImpl;
}

public final class com/facebook/react/bridge/queue/ReactQueueConfigurationSpec {
	public static final field Companion Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec$Companion;
	public fun <init> (Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;)V
	public static final fun createDefault ()Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec;
	public final fun getJSQueueThreadSpec ()Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
	public final fun getNativeModulesQueueThreadSpec ()Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;
}

public final class com/facebook/react/bridge/queue/ReactQueueConfigurationSpec$Builder {
	public fun <init> ()V
	public final fun build ()Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec;
	public final fun setJSQueueThreadSpec (Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;)Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec$Builder;
	public final fun setNativeModulesQueueThreadSpec (Lcom/facebook/react/bridge/queue/MessageQueueThreadSpec;)Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec$Builder;
}

public final class com/facebook/react/bridge/queue/ReactQueueConfigurationSpec$Companion {
	public final fun builder ()Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec$Builder;
	public final fun createDefault ()Lcom/facebook/react/bridge/queue/ReactQueueConfigurationSpec;
}

public final class com/facebook/react/common/ClassFinder {
	public static final field INSTANCE Lcom/facebook/react/common/ClassFinder;
	public static final fun canLoadClassesFromAnnotationProcessors ()Z
	public static final fun findClass (Ljava/lang/String;)Ljava/lang/Class;
}

public final class com/facebook/react/common/ClearableSynchronizedPool : androidx/core/util/Pools$Pool {
	public fun <init> (I)V
	public fun acquire ()Ljava/lang/Object;
	public final fun clear ()V
	public fun release (Ljava/lang/Object;)Z
}

public final class com/facebook/react/common/DebugServerException : java/lang/RuntimeException {
	public static final field Companion Lcom/facebook/react/common/DebugServerException$Companion;
	public fun <init> (Ljava/lang/String;)V
	public synthetic fun <init> (Ljava/lang/String;Ljava/lang/String;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun <init> (Ljava/lang/String;Ljava/lang/Throwable;)V
	public final fun getOriginalMessage ()Ljava/lang/String;
	public static final fun makeGeneric (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Lcom/facebook/react/common/DebugServerException;
	public static final fun makeGeneric (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Lcom/facebook/react/common/DebugServerException;
	public static final fun parse (Ljava/lang/String;Ljava/lang/String;)Lcom/facebook/react/common/DebugServerException;
}

public final class com/facebook/react/common/DebugServerException$Companion {
	public final fun makeGeneric (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Lcom/facebook/react/common/DebugServerException;
	public final fun makeGeneric (Ljava/lang/String;Ljava/lang/String;Ljava/lang/Throwable;)Lcom/facebook/react/common/DebugServerException;
	public final fun parse (Ljava/lang/String;Ljava/lang/String;)Lcom/facebook/react/common/DebugServerException;
}

public class com/facebook/react/common/JavascriptException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;)V
	public final fun getExtraDataAsJson ()Ljava/lang/String;
	public final fun setExtraDataAsJson (Ljava/lang/String;)V
}

public final class com/facebook/react/common/LifecycleState : java/lang/Enum {
	public static final field BEFORE_CREATE Lcom/facebook/react/common/LifecycleState;
	public static final field BEFORE_RESUME Lcom/facebook/react/common/LifecycleState;
	public static final field RESUMED Lcom/facebook/react/common/LifecycleState;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/common/LifecycleState;
	public static fun values ()[Lcom/facebook/react/common/LifecycleState;
}

public class com/facebook/react/common/MapBuilder {
	public fun <init> ()V
	public static fun builder ()Lcom/facebook/react/common/MapBuilder$Builder;
	public static fun newHashMap ()Ljava/util/HashMap;
	public static fun of ()Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
	public static fun of (Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;Ljava/lang/Object;)Ljava/util/Map;
}

public final class com/facebook/react/common/MapBuilder$Builder {
	public fun build ()Ljava/util/Map;
	public fun put (Ljava/lang/Object;Ljava/lang/Object;)Lcom/facebook/react/common/MapBuilder$Builder;
}

public final class com/facebook/react/common/ReactConstants {
	public static final field INSTANCE Lcom/facebook/react/common/ReactConstants;
	public static final field TAG Ljava/lang/String;
	public static final field UNSET I
}

public final class com/facebook/react/common/ReleaseLevel : java/lang/Enum {
	public static final field CANARY Lcom/facebook/react/common/ReleaseLevel;
	public static final field EXPERIMENTAL Lcom/facebook/react/common/ReleaseLevel;
	public static final field STABLE Lcom/facebook/react/common/ReleaseLevel;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/common/ReleaseLevel;
	public static fun values ()[Lcom/facebook/react/common/ReleaseLevel;
}

public final class com/facebook/react/common/ShakeDetector : android/hardware/SensorEventListener {
	public fun <init> (Lcom/facebook/react/common/ShakeDetector$ShakeListener;)V
	public fun <init> (Lcom/facebook/react/common/ShakeDetector$ShakeListener;I)V
	public synthetic fun <init> (Lcom/facebook/react/common/ShakeDetector$ShakeListener;IILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun onAccuracyChanged (Landroid/hardware/Sensor;I)V
	public fun onSensorChanged (Landroid/hardware/SensorEvent;)V
	public final fun start (Landroid/hardware/SensorManager;)V
	public final fun stop ()V
}

public abstract interface class com/facebook/react/common/ShakeDetector$ShakeListener {
	public abstract fun onShake ()V
}

public final class com/facebook/react/common/SingleThreadAsserter {
	public fun <init> ()V
	public final fun assertNow ()V
}

public final class com/facebook/react/common/StandardCharsets {
	public static final field INSTANCE Lcom/facebook/react/common/StandardCharsets;
	public static final field UTF_16 Ljava/nio/charset/Charset;
	public static final field UTF_16BE Ljava/nio/charset/Charset;
	public static final field UTF_16LE Ljava/nio/charset/Charset;
	public static final field UTF_8 Ljava/nio/charset/Charset;
}

public abstract interface class com/facebook/react/common/SurfaceDelegate {
	public abstract fun createContentView (Ljava/lang/String;)V
	public abstract fun destroyContentView ()V
	public abstract fun hide ()V
	public abstract fun isContentViewReady ()Z
	public abstract fun isShowing ()Z
	public abstract fun show ()V
}

public abstract interface class com/facebook/react/common/SurfaceDelegateFactory {
	public abstract fun createSurfaceDelegate (Ljava/lang/String;)Lcom/facebook/react/common/SurfaceDelegate;
}

public final class com/facebook/react/common/SystemClock {
	public static final field INSTANCE Lcom/facebook/react/common/SystemClock;
	public static final fun currentTimeMillis ()J
	public static final fun nanoTime ()J
	public static final fun uptimeMillis ()J
}

public final class com/facebook/react/common/assets/ReactFontManager {
	public static final field Companion Lcom/facebook/react/common/assets/ReactFontManager$Companion;
	public fun <init> ()V
	public final fun addCustomFont (Landroid/content/Context;Ljava/lang/String;I)V
	public final fun addCustomFont (Ljava/lang/String;Landroid/graphics/Typeface;)V
	public static final fun getInstance ()Lcom/facebook/react/common/assets/ReactFontManager;
	public final fun getTypeface (Ljava/lang/String;IILandroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun getTypeface (Ljava/lang/String;ILandroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun getTypeface (Ljava/lang/String;IZLandroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun getTypeface (Ljava/lang/String;Lcom/facebook/react/common/assets/ReactFontManager$TypefaceStyle;Landroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun setTypeface (Ljava/lang/String;ILandroid/graphics/Typeface;)V
}

public final class com/facebook/react/common/assets/ReactFontManager$Companion {
	public final fun getInstance ()Lcom/facebook/react/common/assets/ReactFontManager;
}

public final class com/facebook/react/common/assets/ReactFontManager$TypefaceStyle {
	public static final field BOLD I
	public static final field Companion Lcom/facebook/react/common/assets/ReactFontManager$TypefaceStyle$Companion;
	public static final field NORMAL I
	public fun <init> (I)V
	public fun <init> (II)V
	public synthetic fun <init> (IIILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun <init> (IZ)V
	public final fun apply (Landroid/graphics/Typeface;)Landroid/graphics/Typeface;
	public final fun getNearestStyle ()I
}

public final class com/facebook/react/common/assets/ReactFontManager$TypefaceStyle$Companion {
}

public final class com/facebook/react/common/build/ReactBuildConfig {
	public static final field DEBUG Z
	public static final field ENABLE_PERFETTO Z
	public static final field EXOPACKAGE_FLAGS I
	public static final field INSTANCE Lcom/facebook/react/common/build/ReactBuildConfig;
	public static final field IS_INTERNAL_BUILD Z
	public static final field UNSTABLE_ENABLE_FUSEBOX_RELEASE Z
	public static final field UNSTABLE_ENABLE_MINIFY_LEGACY_ARCHITECTURE Z
}

public abstract interface class com/facebook/react/common/mapbuffer/MapBuffer : java/lang/Iterable, kotlin/jvm/internal/markers/KMappedMarker {
	public static final field Companion Lcom/facebook/react/common/mapbuffer/MapBuffer$Companion;
	public abstract fun contains (I)Z
	public abstract fun entryAt (I)Lcom/facebook/react/common/mapbuffer/MapBuffer$Entry;
	public abstract fun getBoolean (I)Z
	public abstract fun getCount ()I
	public abstract fun getDouble (I)D
	public abstract fun getInt (I)I
	public abstract fun getKeyOffset (I)I
	public abstract fun getLong (I)J
	public abstract fun getMapBuffer (I)Lcom/facebook/react/common/mapbuffer/MapBuffer;
	public abstract fun getMapBufferList (I)Ljava/util/List;
	public abstract fun getString (I)Ljava/lang/String;
	public abstract fun getType (I)Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
}

public final class com/facebook/react/common/mapbuffer/MapBuffer$Companion {
}

public final class com/facebook/react/common/mapbuffer/MapBuffer$DataType : java/lang/Enum {
	public static final field BOOL Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static final field DOUBLE Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static final field INT Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static final field LONG Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static final field MAP Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static final field STRING Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public static fun values ()[Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
}

public abstract interface class com/facebook/react/common/mapbuffer/MapBuffer$Entry {
	public abstract fun getBooleanValue ()Z
	public abstract fun getDoubleValue ()D
	public abstract fun getIntValue ()I
	public abstract fun getKey ()I
	public abstract fun getLongValue ()J
	public abstract fun getMapBufferValue ()Lcom/facebook/react/common/mapbuffer/MapBuffer;
	public abstract fun getStringValue ()Ljava/lang/String;
	public abstract fun getType ()Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
}

public final class com/facebook/react/common/mapbuffer/ReadableMapBuffer : com/facebook/jni/HybridClassBase, com/facebook/react/common/mapbuffer/MapBuffer {
	public static final field Companion Lcom/facebook/react/common/mapbuffer/ReadableMapBuffer$Companion;
	public fun contains (I)Z
	public fun entryAt (I)Lcom/facebook/react/common/mapbuffer/MapBuffer$Entry;
	public fun equals (Ljava/lang/Object;)Z
	public fun getBoolean (I)Z
	public fun getCount ()I
	public fun getDouble (I)D
	public fun getInt (I)I
	public fun getKeyOffset (I)I
	public fun getLong (I)J
	public synthetic fun getMapBuffer (I)Lcom/facebook/react/common/mapbuffer/MapBuffer;
	public fun getMapBuffer (I)Lcom/facebook/react/common/mapbuffer/ReadableMapBuffer;
	public fun getMapBufferList (I)Ljava/util/List;
	public fun getString (I)Ljava/lang/String;
	public fun getType (I)Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public fun hashCode ()I
	public fun iterator ()Ljava/util/Iterator;
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/common/mapbuffer/ReadableMapBuffer$Companion {
}

public final class com/facebook/react/common/mapbuffer/WritableMapBuffer : com/facebook/react/common/mapbuffer/MapBuffer {
	public fun <init> ()V
	public fun contains (I)Z
	public fun entryAt (I)Lcom/facebook/react/common/mapbuffer/MapBuffer$Entry;
	public fun getBoolean (I)Z
	public fun getCount ()I
	public fun getDouble (I)D
	public fun getInt (I)I
	public fun getKeyOffset (I)I
	public fun getLong (I)J
	public fun getMapBuffer (I)Lcom/facebook/react/common/mapbuffer/MapBuffer;
	public fun getMapBufferList (I)Ljava/util/List;
	public fun getString (I)Ljava/lang/String;
	public fun getType (I)Lcom/facebook/react/common/mapbuffer/MapBuffer$DataType;
	public fun iterator ()Ljava/util/Iterator;
	public final fun put (ID)Lcom/facebook/react/common/mapbuffer/WritableMapBuffer;
	public final fun put (II)Lcom/facebook/react/common/mapbuffer/WritableMapBuffer;
	public final fun put (IJ)Lcom/facebook/react/common/mapbuffer/WritableMapBuffer;
	public final fun put (ILcom/facebook/react/common/mapbuffer/MapBuffer;)Lcom/facebook/react/common/mapbuffer/WritableMapBuffer;
	public final fun put (ILjava/lang/String;)Lcom/facebook/react/common/mapbuffer/WritableMapBuffer;
	public final fun put (IZ)Lcom/facebook/react/common/mapbuffer/WritableMapBuffer;
}

public final class com/facebook/react/config/ReactFeatureFlags {
	public static final field INSTANCE Lcom/facebook/react/config/ReactFeatureFlags;
	public static field dispatchPointerEvents Z
}

public final class com/facebook/react/defaults/DefaultComponentsRegistry {
	public static final field INSTANCE Lcom/facebook/react/defaults/DefaultComponentsRegistry;
	public static final fun register (Lcom/facebook/react/fabric/ComponentFactory;)V
}

public final class com/facebook/react/defaults/DefaultNewArchitectureEntryPoint {
	public static final field INSTANCE Lcom/facebook/react/defaults/DefaultNewArchitectureEntryPoint;
	public static final fun getBridgelessEnabled ()Z
	public static final fun getConcurrentReactEnabled ()Z
	public static final fun getFabricEnabled ()Z
	public final fun getReleaseLevel ()Lcom/facebook/react/common/ReleaseLevel;
	public static final fun getTurboModulesEnabled ()Z
	public static final fun load ()V
	public static final fun load (Z)V
	public static final fun load (ZZ)V
	public static final fun load (ZZZ)V
	public static synthetic fun load$default (ZZZILjava/lang/Object;)V
	public final fun setReleaseLevel (Lcom/facebook/react/common/ReleaseLevel;)V
}

public class com/facebook/react/defaults/DefaultReactActivityDelegate : com/facebook/react/ReactActivityDelegate {
	public fun <init> (Lcom/facebook/react/ReactActivity;Ljava/lang/String;Z)V
	public synthetic fun <init> (Lcom/facebook/react/ReactActivity;Ljava/lang/String;ZILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun <init> (Lcom/facebook/react/ReactActivity;Ljava/lang/String;ZZ)V
	protected fun isFabricEnabled ()Z
}

public final class com/facebook/react/defaults/DefaultReactHost {
	public static final field INSTANCE Lcom/facebook/react/defaults/DefaultReactHost;
	public static final fun getDefaultReactHost (Landroid/content/Context;Lcom/facebook/react/ReactNativeHost;Lcom/facebook/react/runtime/JSRuntimeFactory;)Lcom/facebook/react/ReactHost;
	public static final fun getDefaultReactHost (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/runtime/JSRuntimeFactory;ZLjava/util/List;)Lcom/facebook/react/ReactHost;
	public static final fun getDefaultReactHost (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/runtime/JSRuntimeFactory;ZLjava/util/List;Lkotlin/jvm/functions/Function1;Lcom/facebook/react/runtime/BindingsInstaller;)Lcom/facebook/react/ReactHost;
	public static final fun getDefaultReactHost (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/util/List;)Lcom/facebook/react/ReactHost;
	public static final fun getDefaultReactHost (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/util/List;Lkotlin/jvm/functions/Function1;Lcom/facebook/react/runtime/BindingsInstaller;)Lcom/facebook/react/ReactHost;
	public static synthetic fun getDefaultReactHost$default (Landroid/content/Context;Lcom/facebook/react/ReactNativeHost;Lcom/facebook/react/runtime/JSRuntimeFactory;ILjava/lang/Object;)Lcom/facebook/react/ReactHost;
	public static synthetic fun getDefaultReactHost$default (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/runtime/JSRuntimeFactory;ZLjava/util/List;ILjava/lang/Object;)Lcom/facebook/react/ReactHost;
	public static synthetic fun getDefaultReactHost$default (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/runtime/JSRuntimeFactory;ZLjava/util/List;Lkotlin/jvm/functions/Function1;Lcom/facebook/react/runtime/BindingsInstaller;ILjava/lang/Object;)Lcom/facebook/react/ReactHost;
	public static synthetic fun getDefaultReactHost$default (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/util/List;ILjava/lang/Object;)Lcom/facebook/react/ReactHost;
	public static synthetic fun getDefaultReactHost$default (Landroid/content/Context;Ljava/util/List;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;ZZLjava/util/List;Lkotlin/jvm/functions/Function1;Lcom/facebook/react/runtime/BindingsInstaller;ILjava/lang/Object;)Lcom/facebook/react/ReactHost;
}

public abstract class com/facebook/react/defaults/DefaultReactNativeHost : com/facebook/react/ReactNativeHost {
	protected fun <init> (Landroid/app/Application;)V
	public fun clear ()V
	protected fun getJSEngineResolutionAlgorithm ()Lcom/facebook/react/JSEngineResolutionAlgorithm;
	protected fun getReactPackageTurboModuleManagerDelegateBuilder ()Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder;
	protected fun getUIManagerProvider ()Lcom/facebook/react/bridge/UIManagerProvider;
	protected fun isHermesEnabled ()Ljava/lang/Boolean;
	protected fun isNewArchEnabled ()Z
}

public final class com/facebook/react/defaults/DefaultTurboModuleManagerDelegate : com/facebook/react/ReactPackageTurboModuleManagerDelegate {
	public synthetic fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;Ljava/util/List;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun initHybrid (Ljava/util/List;)Lcom/facebook/jni/HybridData;
}

public final class com/facebook/react/defaults/DefaultTurboModuleManagerDelegate$Builder : com/facebook/react/ReactPackageTurboModuleManagerDelegate$Builder {
	public fun <init> ()V
	public final fun addCxxReactPackage (Lkotlin/jvm/functions/Function0;)Lcom/facebook/react/defaults/DefaultTurboModuleManagerDelegate$Builder;
	public final fun addCxxReactPackage (Lkotlin/jvm/functions/Function1;)Lcom/facebook/react/defaults/DefaultTurboModuleManagerDelegate$Builder;
	public final fun addCxxReactPackages (Lkotlin/jvm/functions/Function1;)Lcom/facebook/react/defaults/DefaultTurboModuleManagerDelegate$Builder;
	public synthetic fun build (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;)Lcom/facebook/react/ReactPackageTurboModuleManagerDelegate;
}

public final class com/facebook/react/devsupport/BridgeDevSupportManager : com/facebook/react/devsupport/DevSupportManagerBase {
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/devsupport/ReactInstanceDevHelper;Ljava/lang/String;ZLcom/facebook/react/devsupport/interfaces/RedBoxHandler;Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;ILjava/util/Map;Lcom/facebook/react/common/SurfaceDelegateFactory;Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;Lcom/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager;)V
	public fun handleReloadJS ()V
	public fun loadSplitBundleFromServer (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSplitBundleCallback;)V
}

public class com/facebook/react/devsupport/BundleDownloader {
	public fun <init> (Lokhttp3/OkHttpClient;)V
	public fun downloadBundleFromURL (Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;Ljava/io/File;Ljava/lang/String;Lcom/facebook/react/devsupport/BundleDownloader$BundleInfo;)V
	public fun downloadBundleFromURL (Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;Ljava/io/File;Ljava/lang/String;Lcom/facebook/react/devsupport/BundleDownloader$BundleInfo;Lokhttp3/Request$Builder;)V
}

public class com/facebook/react/devsupport/BundleDownloader$BundleInfo {
	public fun <init> ()V
	public static fun fromJSONString (Ljava/lang/String;)Lcom/facebook/react/devsupport/BundleDownloader$BundleInfo;
	public fun getFilesChangedCount ()I
	public fun getUrl ()Ljava/lang/String;
	public fun toJSONString ()Ljava/lang/String;
}

public final class com/facebook/react/devsupport/DefaultDevLoadingViewImplementation : com/facebook/react/devsupport/interfaces/DevLoadingViewManager {
	public static final field Companion Lcom/facebook/react/devsupport/DefaultDevLoadingViewImplementation$Companion;
	public fun <init> (Lcom/facebook/react/devsupport/ReactInstanceDevHelper;)V
	public fun hide ()V
	public fun showMessage (Ljava/lang/String;)V
	public fun updateProgress (Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)V
}

public final class com/facebook/react/devsupport/DefaultDevLoadingViewImplementation$Companion {
	public final fun setDevLoadingEnabled (Z)V
}

public class com/facebook/react/devsupport/DevServerHelper {
	public fun <init> (Lcom/facebook/react/modules/debug/interfaces/DeveloperSettings;Landroid/content/Context;Lcom/facebook/react/packagerconnection/PackagerConnectionSettings;)V
	public fun closeInspectorConnection ()V
	public fun closePackagerConnection ()V
	public fun disableDebugger ()V
	public fun downloadBundleFromURL (Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;Ljava/io/File;Ljava/lang/String;Lcom/facebook/react/devsupport/BundleDownloader$BundleInfo;)V
	public fun downloadBundleFromURL (Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;Ljava/io/File;Ljava/lang/String;Lcom/facebook/react/devsupport/BundleDownloader$BundleInfo;Lokhttp3/Request$Builder;)V
	public fun downloadBundleResourceFromUrlSync (Ljava/lang/String;Ljava/io/File;)Ljava/io/File;
	public fun getDevServerBundleURL (Ljava/lang/String;)Ljava/lang/String;
	public fun getDevServerSplitBundleURL (Ljava/lang/String;)Ljava/lang/String;
	public fun getSourceMapUrl (Ljava/lang/String;)Ljava/lang/String;
	public fun getSourceUrl (Ljava/lang/String;)Ljava/lang/String;
	public fun getWebsocketProxyURL ()Ljava/lang/String;
	public fun isPackagerRunning (Lcom/facebook/react/devsupport/interfaces/PackagerStatusCallback;)V
	public fun openDebugger (Lcom/facebook/react/bridge/ReactContext;Ljava/lang/String;)V
	public fun openInspectorConnection ()V
	public fun openPackagerConnection (Ljava/lang/String;Lcom/facebook/react/devsupport/DevServerHelper$PackagerCommandListener;)V
}

public abstract interface class com/facebook/react/devsupport/DevServerHelper$PackagerCommandListener {
	public abstract fun customCommandHandlers ()Ljava/util/Map;
	public fun onCaptureHeapCommand (Lcom/facebook/react/packagerconnection/Responder;)V
	public abstract fun onPackagerConnected ()V
	public abstract fun onPackagerDevMenuCommand ()V
	public abstract fun onPackagerDisconnected ()V
	public abstract fun onPackagerReloadCommand ()V
}

public abstract class com/facebook/react/devsupport/DevSupportManagerBase : com/facebook/react/devsupport/interfaces/DevSupportManager {
	protected final field mReactInstanceDevHelper Lcom/facebook/react/devsupport/ReactInstanceDevHelper;
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/devsupport/ReactInstanceDevHelper;Ljava/lang/String;ZLcom/facebook/react/devsupport/interfaces/RedBoxHandler;Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;ILjava/util/Map;Lcom/facebook/react/common/SurfaceDelegateFactory;Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;Lcom/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager;)V
	public fun addCustomDevOption (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevOptionHandler;)V
	public fun createRootView (Ljava/lang/String;)Landroid/view/View;
	public fun createSurfaceDelegate (Ljava/lang/String;)Lcom/facebook/react/common/SurfaceDelegate;
	public fun destroyRootView (Landroid/view/View;)V
	public fun downloadBundleResourceFromUrlSync (Ljava/lang/String;Ljava/io/File;)Ljava/io/File;
	public fun fetchSplitBundleAndCreateBundleLoader (Ljava/lang/String;Lcom/facebook/react/devsupport/DevSupportManagerBase$CallbackWithBundleLoader;)V
	protected fun getApplicationContext ()Landroid/content/Context;
	public fun getCurrentActivity ()Landroid/app/Activity;
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public fun getDevLoadingViewManager ()Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;
	public fun getDevServerHelper ()Lcom/facebook/react/devsupport/DevServerHelper;
	public fun getDevSettings ()Lcom/facebook/react/modules/debug/interfaces/DeveloperSettings;
	public fun getDevSupportEnabled ()Z
	public fun getDownloadedJSBundleFile ()Ljava/lang/String;
	public fun getJSAppBundleName ()Ljava/lang/String;
	public fun getLastErrorCookie ()I
	public fun getLastErrorStack ()[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public fun getLastErrorTitle ()Ljava/lang/String;
	public fun getLastErrorType ()Lcom/facebook/react/devsupport/interfaces/ErrorType;
	public fun getReactInstanceDevHelper ()Lcom/facebook/react/devsupport/ReactInstanceDevHelper;
	public fun getRedBoxHandler ()Lcom/facebook/react/devsupport/interfaces/RedBoxHandler;
	public fun getSourceMapUrl ()Ljava/lang/String;
	public fun getSourceUrl ()Ljava/lang/String;
	protected abstract fun getUniqueTag ()Ljava/lang/String;
	public fun handleException (Ljava/lang/Exception;)V
	public fun hasUpToDateJSBundleInCache ()Z
	protected fun hideDevLoadingView ()V
	public fun hidePausedInDebuggerOverlay ()V
	public fun hideRedboxDialog ()V
	public fun isPackagerRunning (Lcom/facebook/react/devsupport/interfaces/PackagerStatusCallback;)V
	public fun onNewReactContextCreated (Lcom/facebook/react/bridge/ReactContext;)V
	public fun onReactInstanceDestroyed (Lcom/facebook/react/bridge/ReactContext;)V
	public fun openDebugger ()V
	public fun processErrorCustomizers (Landroid/util/Pair;)Landroid/util/Pair;
	public fun registerErrorCustomizer (Lcom/facebook/react/devsupport/interfaces/ErrorCustomizer;)V
	public fun reloadJSFromServer (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/BundleLoadCallback;)V
	public fun reloadSettings ()V
	public fun setAdditionalOptionForPackager (Ljava/lang/String;Ljava/lang/String;)V
	public fun setDevSupportEnabled (Z)V
	public fun setFpsDebugEnabled (Z)V
	public fun setHotModuleReplacementEnabled (Z)V
	public fun setPackagerLocationCustomizer (Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PackagerLocationCustomizer;)V
	protected fun showDevLoadingViewForRemoteJSEnabled ()V
	public fun showDevOptionsDialog ()V
	public fun showNewJSError (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;I)V
	public fun showNewJavaError (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun showPausedInDebuggerOverlay (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PausedInDebuggerOverlayCommandListener;)V
	public fun startInspector ()V
	public fun stopInspector ()V
	public fun toggleElementInspector ()V
}

public abstract interface class com/facebook/react/devsupport/DevSupportManagerBase$CallbackWithBundleLoader {
	public abstract fun onError (Ljava/lang/String;Ljava/lang/Throwable;)V
	public abstract fun onSuccess (Lcom/facebook/react/bridge/JSBundleLoader;)V
}

public abstract interface class com/facebook/react/devsupport/DevSupportManagerFactory {
	public abstract fun create (Landroid/content/Context;Lcom/facebook/react/devsupport/ReactInstanceDevHelper;Ljava/lang/String;ZLcom/facebook/react/devsupport/interfaces/RedBoxHandler;Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;ILjava/util/Map;Lcom/facebook/react/common/SurfaceDelegateFactory;Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;Lcom/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager;)Lcom/facebook/react/devsupport/interfaces/DevSupportManager;
	public abstract fun create (Landroid/content/Context;Lcom/facebook/react/devsupport/ReactInstanceDevHelper;Ljava/lang/String;ZLcom/facebook/react/devsupport/interfaces/RedBoxHandler;Lcom/facebook/react/devsupport/interfaces/DevBundleDownloadListener;ILjava/util/Map;Lcom/facebook/react/common/SurfaceDelegateFactory;Lcom/facebook/react/devsupport/interfaces/DevLoadingViewManager;Lcom/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager;Z)Lcom/facebook/react/devsupport/interfaces/DevSupportManager;
}

public final class com/facebook/react/devsupport/DoubleTapReloadRecognizer {
	public fun <init> ()V
	public final fun didDoubleTapR (ILandroid/view/View;)Z
}

public abstract interface class com/facebook/react/devsupport/HMRClient : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun disable ()V
	public abstract fun enable ()V
	public abstract fun registerBundle (Ljava/lang/String;)V
	public abstract fun setup (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;IZLjava/lang/String;)V
}

public final class com/facebook/react/devsupport/LogBoxModule : com/facebook/fbreact/specs/NativeLogBoxSpec {
	public static final field Companion Lcom/facebook/react/devsupport/LogBoxModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/devsupport/interfaces/DevSupportManager;)V
	public fun hide ()V
	public fun invalidate ()V
	public fun show ()V
}

public final class com/facebook/react/devsupport/LogBoxModule$Companion {
}

public abstract interface class com/facebook/react/devsupport/ReactInstanceDevHelper {
	public abstract fun createRootView (Ljava/lang/String;)Landroid/view/View;
	public abstract fun destroyRootView (Landroid/view/View;)V
	public abstract fun getCurrentActivity ()Landroid/app/Activity;
	public abstract fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public abstract fun getJavaScriptExecutorFactory ()Lcom/facebook/react/bridge/JavaScriptExecutorFactory;
	public abstract fun loadBundle (Lcom/facebook/react/bridge/JSBundleLoader;)Lcom/facebook/react/interfaces/TaskInterface;
	public abstract fun onJSBundleLoadedFromServer ()V
	public abstract fun reload (Ljava/lang/String;)V
	public abstract fun toggleElementInspector ()V
}

public class com/facebook/react/devsupport/ReleaseDevSupportManager : com/facebook/react/devsupport/interfaces/DevSupportManager {
	public fun <init> ()V
	public fun addCustomDevOption (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevOptionHandler;)V
	public fun createRootView (Ljava/lang/String;)Landroid/view/View;
	public fun createSurfaceDelegate (Ljava/lang/String;)Lcom/facebook/react/common/SurfaceDelegate;
	public fun destroyRootView (Landroid/view/View;)V
	public fun downloadBundleResourceFromUrlSync (Ljava/lang/String;Ljava/io/File;)Ljava/io/File;
	public fun getCurrentActivity ()Landroid/app/Activity;
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public fun getDevSettings ()Lcom/facebook/react/modules/debug/interfaces/DeveloperSettings;
	public fun getDevSupportEnabled ()Z
	public fun getDownloadedJSBundleFile ()Ljava/lang/String;
	public fun getLastErrorCookie ()I
	public fun getLastErrorStack ()[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public fun getLastErrorTitle ()Ljava/lang/String;
	public fun getLastErrorType ()Lcom/facebook/react/devsupport/interfaces/ErrorType;
	public fun getRedBoxHandler ()Lcom/facebook/react/devsupport/interfaces/RedBoxHandler;
	public fun getSourceMapUrl ()Ljava/lang/String;
	public fun getSourceUrl ()Ljava/lang/String;
	public fun handleException (Ljava/lang/Exception;)V
	public fun handleReloadJS ()V
	public fun hasUpToDateJSBundleInCache ()Z
	public fun hidePausedInDebuggerOverlay ()V
	public fun hideRedboxDialog ()V
	public fun isPackagerRunning (Lcom/facebook/react/devsupport/interfaces/PackagerStatusCallback;)V
	public fun loadSplitBundleFromServer (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSplitBundleCallback;)V
	public fun onNewReactContextCreated (Lcom/facebook/react/bridge/ReactContext;)V
	public fun onReactInstanceDestroyed (Lcom/facebook/react/bridge/ReactContext;)V
	public fun openDebugger ()V
	public fun processErrorCustomizers (Landroid/util/Pair;)Landroid/util/Pair;
	public fun registerErrorCustomizer (Lcom/facebook/react/devsupport/interfaces/ErrorCustomizer;)V
	public fun reloadJSFromServer (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/BundleLoadCallback;)V
	public fun reloadSettings ()V
	public fun setAdditionalOptionForPackager (Ljava/lang/String;Ljava/lang/String;)V
	public fun setDevSupportEnabled (Z)V
	public fun setFpsDebugEnabled (Z)V
	public fun setHotModuleReplacementEnabled (Z)V
	public fun setPackagerLocationCustomizer (Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PackagerLocationCustomizer;)V
	public fun showDevOptionsDialog ()V
	public fun showNewJSError (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;I)V
	public fun showNewJavaError (Ljava/lang/String;Ljava/lang/Throwable;)V
	public fun showPausedInDebuggerOverlay (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PausedInDebuggerOverlayCommandListener;)V
	public fun startInspector ()V
	public fun stopInspector ()V
	public fun toggleElementInspector ()V
}

public class com/facebook/react/devsupport/StackTraceHelper {
	public static final field COLUMN_KEY Ljava/lang/String;
	public static final field COMPONENT_STACK_KEY Ljava/lang/String;
	public static final field EXTRA_DATA_KEY Ljava/lang/String;
	public static final field FILE_KEY Ljava/lang/String;
	public static final field ID_KEY Ljava/lang/String;
	public static final field IS_FATAL_KEY Ljava/lang/String;
	public static final field LINE_NUMBER_KEY Ljava/lang/String;
	public static final field MESSAGE_KEY Ljava/lang/String;
	public static final field METHOD_NAME_KEY Ljava/lang/String;
	public static final field NAME_KEY Ljava/lang/String;
	public static final field ORIGINAL_MESSAGE_KEY Ljava/lang/String;
	public static final field STACK_KEY Ljava/lang/String;
	public fun <init> ()V
	public static fun convertJavaStackTrace (Ljava/lang/Throwable;)[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public static fun convertJsStackTrace (Lcom/facebook/react/bridge/ReadableArray;)[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public static fun convertJsStackTrace (Ljava/lang/String;)[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public static fun convertJsStackTrace (Lorg/json/JSONArray;)[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public static fun convertProcessedError (Lcom/facebook/react/interfaces/exceptionmanager/ReactJsExceptionHandler$ProcessedError;)Lcom/facebook/react/bridge/JavaOnlyMap;
	public static fun formatFrameSource (Lcom/facebook/react/devsupport/interfaces/StackFrame;)Ljava/lang/String;
	public static fun formatStackTrace (Ljava/lang/String;[Lcom/facebook/react/devsupport/interfaces/StackFrame;)Ljava/lang/String;
}

public class com/facebook/react/devsupport/StackTraceHelper$StackFrameImpl : com/facebook/react/devsupport/interfaces/StackFrame {
	public fun getColumn ()I
	public fun getFile ()Ljava/lang/String;
	public fun getFileName ()Ljava/lang/String;
	public fun getLine ()I
	public fun getMethod ()Ljava/lang/String;
	public fun isCollapsed ()Z
	public fun toJSON ()Lorg/json/JSONObject;
}

public abstract interface class com/facebook/react/devsupport/interfaces/BundleLoadCallback {
	public fun onError (Ljava/lang/Exception;)V
	public abstract fun onSuccess ()V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevBundleDownloadListener {
	public abstract fun onFailure (Ljava/lang/Exception;)V
	public abstract fun onProgress (Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)V
	public abstract fun onSuccess ()V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevLoadingViewManager {
	public abstract fun hide ()V
	public abstract fun showMessage (Ljava/lang/String;)V
	public abstract fun updateProgress (Ljava/lang/String;Ljava/lang/Integer;Ljava/lang/Integer;)V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevOptionHandler {
	public abstract fun onOptionSelected ()V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevSplitBundleCallback {
	public abstract fun onError (Ljava/lang/String;Ljava/lang/Throwable;)V
	public abstract fun onSuccess ()V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevSupportManager : com/facebook/react/bridge/JSExceptionHandler {
	public abstract fun addCustomDevOption (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevOptionHandler;)V
	public abstract fun createRootView (Ljava/lang/String;)Landroid/view/View;
	public abstract fun createSurfaceDelegate (Ljava/lang/String;)Lcom/facebook/react/common/SurfaceDelegate;
	public abstract fun destroyRootView (Landroid/view/View;)V
	public abstract fun downloadBundleResourceFromUrlSync (Ljava/lang/String;Ljava/io/File;)Ljava/io/File;
	public abstract fun getCurrentActivity ()Landroid/app/Activity;
	public abstract fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public abstract fun getDevSettings ()Lcom/facebook/react/modules/debug/interfaces/DeveloperSettings;
	public abstract fun getDevSupportEnabled ()Z
	public abstract fun getDownloadedJSBundleFile ()Ljava/lang/String;
	public abstract fun getLastErrorCookie ()I
	public abstract fun getLastErrorStack ()[Lcom/facebook/react/devsupport/interfaces/StackFrame;
	public abstract fun getLastErrorTitle ()Ljava/lang/String;
	public abstract fun getLastErrorType ()Lcom/facebook/react/devsupport/interfaces/ErrorType;
	public abstract fun getRedBoxHandler ()Lcom/facebook/react/devsupport/interfaces/RedBoxHandler;
	public abstract fun getSourceMapUrl ()Ljava/lang/String;
	public abstract fun getSourceUrl ()Ljava/lang/String;
	public abstract fun handleReloadJS ()V
	public abstract fun hasUpToDateJSBundleInCache ()Z
	public abstract fun hidePausedInDebuggerOverlay ()V
	public abstract fun hideRedboxDialog ()V
	public abstract fun isPackagerRunning (Lcom/facebook/react/devsupport/interfaces/PackagerStatusCallback;)V
	public abstract fun loadSplitBundleFromServer (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSplitBundleCallback;)V
	public abstract fun onNewReactContextCreated (Lcom/facebook/react/bridge/ReactContext;)V
	public abstract fun onReactInstanceDestroyed (Lcom/facebook/react/bridge/ReactContext;)V
	public abstract fun openDebugger ()V
	public abstract fun processErrorCustomizers (Landroid/util/Pair;)Landroid/util/Pair;
	public abstract fun registerErrorCustomizer (Lcom/facebook/react/devsupport/interfaces/ErrorCustomizer;)V
	public abstract fun reloadJSFromServer (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/BundleLoadCallback;)V
	public abstract fun reloadSettings ()V
	public abstract fun setAdditionalOptionForPackager (Ljava/lang/String;Ljava/lang/String;)V
	public abstract fun setDevSupportEnabled (Z)V
	public abstract fun setFpsDebugEnabled (Z)V
	public abstract fun setHotModuleReplacementEnabled (Z)V
	public abstract fun setPackagerLocationCustomizer (Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PackagerLocationCustomizer;)V
	public abstract fun showDevOptionsDialog ()V
	public abstract fun showNewJSError (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;I)V
	public abstract fun showNewJavaError (Ljava/lang/String;Ljava/lang/Throwable;)V
	public abstract fun showPausedInDebuggerOverlay (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PausedInDebuggerOverlayCommandListener;)V
	public abstract fun startInspector ()V
	public abstract fun stopInspector ()V
	public abstract fun toggleElementInspector ()V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevSupportManager$PackagerLocationCustomizer {
	public abstract fun run (Ljava/lang/Runnable;)V
}

public abstract interface class com/facebook/react/devsupport/interfaces/DevSupportManager$PausedInDebuggerOverlayCommandListener {
	public abstract fun onResume ()V
}

public abstract interface class com/facebook/react/devsupport/interfaces/ErrorCustomizer {
	public abstract fun customizeErrorInfo (Landroid/util/Pair;)Landroid/util/Pair;
}

public final class com/facebook/react/devsupport/interfaces/ErrorType : java/lang/Enum {
	public static final field JS Lcom/facebook/react/devsupport/interfaces/ErrorType;
	public static final field NATIVE Lcom/facebook/react/devsupport/interfaces/ErrorType;
	public final fun getDisplayName ()Ljava/lang/String;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public fun toString ()Ljava/lang/String;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/devsupport/interfaces/ErrorType;
	public static fun values ()[Lcom/facebook/react/devsupport/interfaces/ErrorType;
}

public abstract interface class com/facebook/react/devsupport/interfaces/PackagerStatusCallback {
	public abstract fun onPackagerStatusFetched (Z)V
}

public abstract interface class com/facebook/react/devsupport/interfaces/PausedInDebuggerOverlayManager {
	public abstract fun hidePausedInDebuggerOverlay ()V
	public abstract fun showPausedInDebuggerOverlay (Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/DevSupportManager$PausedInDebuggerOverlayCommandListener;)V
}

public abstract interface class com/facebook/react/devsupport/interfaces/RedBoxHandler {
	public abstract fun handleRedbox (Ljava/lang/String;[Lcom/facebook/react/devsupport/interfaces/StackFrame;Lcom/facebook/react/devsupport/interfaces/ErrorType;)V
	public abstract fun isReportEnabled ()Z
	public abstract fun reportRedbox (Landroid/content/Context;Ljava/lang/String;[Lcom/facebook/react/devsupport/interfaces/StackFrame;Ljava/lang/String;Lcom/facebook/react/devsupport/interfaces/RedBoxHandler$ReportCompletedListener;)V
}

public abstract interface class com/facebook/react/devsupport/interfaces/RedBoxHandler$ReportCompletedListener {
	public abstract fun onReportError (Landroid/text/SpannedString;)V
	public abstract fun onReportSuccess (Landroid/text/SpannedString;)V
}

public abstract interface class com/facebook/react/devsupport/interfaces/StackFrame {
	public abstract fun getColumn ()I
	public abstract fun getFile ()Ljava/lang/String;
	public abstract fun getFileName ()Ljava/lang/String;
	public abstract fun getLine ()I
	public abstract fun getMethod ()Ljava/lang/String;
	public abstract fun isCollapsed ()Z
	public abstract fun toJSON ()Lorg/json/JSONObject;
}

public final class com/facebook/react/fabric/ComponentFactory {
	public fun <init> ()V
}

public class com/facebook/react/fabric/DevToolsReactPerfLogger : com/facebook/react/bridge/ReactMarker$FabricMarkerListener {
	public fun <init> ()V
	public fun addDevToolsReactPerfLoggerListener (Lcom/facebook/react/fabric/DevToolsReactPerfLogger$DevToolsReactPerfLoggerListener;)V
	public fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;IJ)V
	public fun logFabricMarker (Lcom/facebook/react/bridge/ReactMarkerConstants;Ljava/lang/String;IJI)V
	public fun removeDevToolsReactPerfLoggerListener (Lcom/facebook/react/fabric/DevToolsReactPerfLogger$DevToolsReactPerfLoggerListener;)V
}

public abstract interface class com/facebook/react/fabric/DevToolsReactPerfLogger$DevToolsReactPerfLoggerListener {
	public abstract fun onFabricCommitEnd (Lcom/facebook/react/fabric/DevToolsReactPerfLogger$FabricCommitPoint;)V
}

public class com/facebook/react/fabric/DevToolsReactPerfLogger$FabricCommitPoint {
	public fun getAffectedLayoutNodesCount ()I
	public fun getAffectedLayoutNodesCountTime ()J
	public fun getBatchExecutionDuration ()J
	public fun getBatchExecutionEnd ()J
	public fun getBatchExecutionStart ()J
	public fun getCommitDuration ()J
	public fun getCommitEnd ()J
	public fun getCommitNumber ()J
	public fun getCommitStart ()J
	public fun getDiffDuration ()J
	public fun getDiffEnd ()J
	public fun getDiffStart ()J
	public fun getFinishTransactionEnd ()J
	public fun getFinishTransactionStart ()J
	public fun getLayoutDuration ()J
	public fun getLayoutEnd ()J
	public fun getLayoutStart ()J
	public fun getTransactionEndDuration ()J
	public fun getUpdateUIMainThreadEnd ()J
	public fun getUpdateUIMainThreadStart ()J
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/fabric/FabricSoLoader {
	public static final field INSTANCE Lcom/facebook/react/fabric/FabricSoLoader;
	public static final fun staticInit ()V
}

public class com/facebook/react/fabric/FabricUIManager : com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/bridge/UIManager, com/facebook/react/fabric/interop/UIBlockViewResolver, com/facebook/react/uimanager/events/SynchronousEventReceiver {
	public static final field IS_DEVELOPMENT_ENVIRONMENT Z
	public static final field TAG Ljava/lang/String;
	public field mDevToolsReactPerfLogger Lcom/facebook/react/fabric/DevToolsReactPerfLogger;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/uimanager/ViewManagerRegistry;Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public fun addRootView (Landroid/view/View;Lcom/facebook/react/bridge/WritableMap;)I
	public fun addUIBlock (Lcom/facebook/react/fabric/interop/UIBlock;)V
	public fun addUIManagerEventListener (Lcom/facebook/react/bridge/UIManagerListener;)V
	public fun attachRootView (Lcom/facebook/react/interfaces/fabric/SurfaceHandler;Landroid/view/View;)V
	public fun clearJSResponder ()V
	public fun dispatchCommand (IIILcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchCommand (IILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun getColor (I[Ljava/lang/String;)I
	public fun getEventDispatcher ()Lcom/facebook/react/uimanager/events/EventDispatcher;
	public fun getPerformanceCounters ()Ljava/util/Map;
	public fun getThemeData (I[F)Z
	public fun initialize ()V
	public fun invalidate ()V
	public fun markActiveTouchForTag (II)V
	public fun onAllAnimationsComplete ()V
	public fun onAnimationStarted ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun onRequestEventBeat ()V
	public fun prependUIBlock (Lcom/facebook/react/fabric/interop/UIBlock;)V
	public fun profileNextBatch ()V
	public fun receiveEvent (IILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun receiveEvent (IILjava/lang/String;ZLcom/facebook/react/bridge/WritableMap;I)V
	public fun receiveEvent (IILjava/lang/String;ZLcom/facebook/react/bridge/WritableMap;IZ)V
	public fun receiveEvent (ILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun removeUIManagerEventListener (Lcom/facebook/react/bridge/UIManagerListener;)V
	public fun resolveCustomDirectEventName (Ljava/lang/String;)Ljava/lang/String;
	public fun resolveView (I)Landroid/view/View;
	public fun sendAccessibilityEvent (II)V
	public fun sendAccessibilityEventFromJS (IILjava/lang/String;)V
	public fun setJSResponder (IIIZ)V
	public fun startSurface (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;II)I
	public fun startSurface (Lcom/facebook/react/interfaces/fabric/SurfaceHandler;Landroid/content/Context;Landroid/view/View;)V
	public fun stopSurface (I)V
	public fun stopSurface (Lcom/facebook/react/interfaces/fabric/SurfaceHandler;)V
	public fun sweepActiveTouchForTag (II)V
	public fun synchronouslyUpdateViewOnUIThread (ILcom/facebook/react/bridge/ReadableMap;)V
	public fun updateRootLayoutSpecs (IIIII)V
}

public final class com/facebook/react/fabric/FabricUIManagerProviderImpl : com/facebook/react/bridge/UIManagerProvider {
	public fun <init> (Lcom/facebook/react/fabric/ComponentFactory;Lcom/facebook/react/uimanager/ViewManagerRegistry;)V
	public fun createUIManager (Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/bridge/UIManager;
}

public class com/facebook/react/fabric/StateWrapperImpl : com/facebook/jni/HybridClassBase, com/facebook/react/uimanager/StateWrapper {
	public fun destroyState ()V
	public fun getStateData ()Lcom/facebook/react/bridge/ReadableNativeMap;
	public fun getStateDataMapBuffer ()Lcom/facebook/react/common/mapbuffer/ReadableMapBuffer;
	public fun toString ()Ljava/lang/String;
	public fun updateState (Lcom/facebook/react/bridge/WritableMap;)V
	public fun updateStateImpl (Lcom/facebook/react/bridge/NativeMap;)V
}

public final class com/facebook/react/fabric/events/EventBeatManager : com/facebook/react/uimanager/events/BatchEventDispatchedListener {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun onBatchEventDispatched ()V
}

public final class com/facebook/react/fabric/events/EventEmitterWrapper : com/facebook/jni/HybridClassBase {
	public final fun destroy ()V
	public final fun dispatch (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;I)V
	public final fun dispatchEventSynchronously (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public final fun dispatchUnique (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
}

public final class com/facebook/react/fabric/events/FabricEventEmitter : com/facebook/react/uimanager/events/RCTModernEventEmitter {
	public fun <init> (Lcom/facebook/react/fabric/FabricUIManager;)V
	public fun receiveEvent (IILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun receiveEvent (IILjava/lang/String;ZILcom/facebook/react/bridge/WritableMap;I)V
	public fun receiveEvent (ILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun receiveTouches (Lcom/facebook/react/uimanager/events/TouchEvent;)V
	public fun receiveTouches (Ljava/lang/String;Lcom/facebook/react/bridge/WritableArray;Lcom/facebook/react/bridge/WritableArray;)V
}

public abstract interface class com/facebook/react/fabric/mounting/LayoutMetricsConversions {
	public static final field Companion Lcom/facebook/react/fabric/mounting/LayoutMetricsConversions$Companion;
	public static fun getMaxSize (I)F
	public static fun getMinSize (I)F
	public static fun getYogaMeasureMode (FF)Lcom/facebook/yoga/YogaMeasureMode;
	public static fun getYogaSize (FF)F
}

public final class com/facebook/react/fabric/mounting/LayoutMetricsConversions$Companion {
	public final fun getMaxSize (I)F
	public final fun getMinSize (I)F
	public final fun getYogaMeasureMode (FF)Lcom/facebook/yoga/YogaMeasureMode;
	public final fun getYogaSize (FF)F
}

public class com/facebook/react/fabric/mounting/MountItemDispatcher {
	public fun <init> (Lcom/facebook/react/fabric/mounting/MountingManager;Lcom/facebook/react/fabric/mounting/MountItemDispatcher$ItemDispatchListener;)V
	public fun addMountItem (Lcom/facebook/react/fabric/mounting/mountitems/MountItem;)V
	public fun addPreAllocateMountItem (Lcom/facebook/react/fabric/mounting/mountitems/MountItem;)V
	public fun addViewCommandMountItem (Lcom/facebook/react/fabric/mounting/mountitems/DispatchCommandMountItem;)V
	public fun dispatchMountItems (Ljava/util/Queue;)V
	public fun dispatchPreMountItems (J)V
	public fun getBatchedExecutionTime ()J
	public fun getRunStartTime ()J
	public fun tryDispatchMountItems ()V
}

public abstract interface class com/facebook/react/fabric/mounting/MountItemDispatcher$ItemDispatchListener {
	public abstract fun didDispatchMountItems ()V
	public abstract fun didMountItems (Ljava/util/List;)V
	public abstract fun willMountItems (Ljava/util/List;)V
}

public class com/facebook/react/fabric/mounting/MountingManager {
	public static final field TAG Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/uimanager/ViewManagerRegistry;Lcom/facebook/react/fabric/mounting/MountingManager$MountItemExecutor;)V
	public fun attachRootView (ILandroid/view/View;Lcom/facebook/react/uimanager/ThemedReactContext;)V
	public fun clearJSResponder ()V
	public fun enqueuePendingEvent (IILjava/lang/String;ZLcom/facebook/react/bridge/WritableMap;I)V
	public fun getEventEmitter (II)Lcom/facebook/react/fabric/events/EventEmitterWrapper;
	public fun getSurfaceManager (I)Lcom/facebook/react/fabric/mounting/SurfaceMountingManager;
	public fun getSurfaceManagerEnforced (ILjava/lang/String;)Lcom/facebook/react/fabric/mounting/SurfaceMountingManager;
	public fun getSurfaceManagerForView (I)Lcom/facebook/react/fabric/mounting/SurfaceMountingManager;
	public fun getSurfaceManagerForViewEnforced (I)Lcom/facebook/react/fabric/mounting/SurfaceMountingManager;
	public fun getViewExists (I)Z
	public fun isWaitingForViewAttach (I)Z
	public fun measure (Lcom/facebook/react/bridge/ReactContext;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/ReadableMap;FLcom/facebook/yoga/YogaMeasureMode;FLcom/facebook/yoga/YogaMeasureMode;[F)J
	public fun measureMapBuffer (Lcom/facebook/react/bridge/ReactContext;Ljava/lang/String;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;FLcom/facebook/yoga/YogaMeasureMode;FLcom/facebook/yoga/YogaMeasureMode;[F)J
	public fun receiveCommand (IIILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (IILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun sendAccessibilityEvent (III)V
	public fun startSurface (ILcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)Lcom/facebook/react/fabric/mounting/SurfaceMountingManager;
	public fun stopSurface (I)V
	public fun surfaceIsStopped (I)Z
	public fun updateProps (ILcom/facebook/react/bridge/ReadableMap;)V
}

public abstract interface class com/facebook/react/fabric/mounting/MountingManager$MountItemExecutor {
	public abstract fun executeItems (Ljava/util/Queue;)V
}

public class com/facebook/react/fabric/mounting/SurfaceMountingManager {
	public static final field TAG Ljava/lang/String;
	public fun <init> (ILcom/facebook/react/touch/JSResponderHandler;Lcom/facebook/react/uimanager/ViewManagerRegistry;Lcom/facebook/react/uimanager/RootViewManager;Lcom/facebook/react/fabric/mounting/MountingManager$MountItemExecutor;Lcom/facebook/react/uimanager/ThemedReactContext;)V
	public fun addViewAt (III)V
	public fun attachRootView (Landroid/view/View;Lcom/facebook/react/uimanager/ThemedReactContext;)V
	public fun createView (Ljava/lang/String;ILcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/uimanager/StateWrapper;Lcom/facebook/react/fabric/events/EventEmitterWrapper;Z)V
	public fun createViewUnsafe (Ljava/lang/String;ILcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/uimanager/StateWrapper;Lcom/facebook/react/fabric/events/EventEmitterWrapper;Z)V
	public fun deleteView (I)V
	public fun enqueuePendingEvent (ILjava/lang/String;ZLcom/facebook/react/bridge/WritableMap;I)V
	public fun getContext ()Lcom/facebook/react/uimanager/ThemedReactContext;
	public fun getEventEmitter (I)Lcom/facebook/react/fabric/events/EventEmitterWrapper;
	public fun getSurfaceId ()I
	public fun getView (I)Landroid/view/View;
	public fun getViewExists (I)Z
	public fun isRootViewAttached ()Z
	public fun isStopped ()Z
	public fun markActiveTouchForTag (I)V
	public fun preallocateView (Ljava/lang/String;ILcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/uimanager/StateWrapper;Z)V
	public fun printSurfaceState ()V
	public fun receiveCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun removeViewAt (III)V
	public fun scheduleMountItemOnViewAttach (Lcom/facebook/react/fabric/mounting/mountitems/MountItem;)V
	public fun sendAccessibilityEvent (II)V
	public fun setJSResponder (IIZ)V
	public fun stopSurface ()V
	public fun sweepActiveTouchForTag (I)V
	public fun updateEventEmitter (ILcom/facebook/react/fabric/events/EventEmitterWrapper;)V
	public fun updateLayout (IIIIIIII)V
	public fun updateOverflowInset (IIIII)V
	public fun updatePadding (IIIII)V
	public fun updateProps (ILcom/facebook/react/bridge/ReadableMap;)V
	public fun updateState (ILcom/facebook/react/uimanager/StateWrapper;)V
}

public abstract interface class com/facebook/react/fabric/mounting/mountitems/BatchMountItem : com/facebook/react/fabric/mounting/mountitems/MountItem {
	public abstract fun isBatchEmpty ()Z
}

public abstract class com/facebook/react/fabric/mounting/mountitems/DispatchCommandMountItem : com/facebook/react/fabric/mounting/mountitems/MountItem {
	public fun <init> ()V
	public final fun getRetries ()I
	public final fun incrementRetries ()V
}

public abstract interface class com/facebook/react/fabric/mounting/mountitems/MountItem {
	public abstract fun execute (Lcom/facebook/react/fabric/mounting/MountingManager;)V
	public abstract fun getSurfaceId ()I
}

public final class com/facebook/react/fabric/mounting/mountitems/MountItemFactory {
	public static final field INSTANCE Lcom/facebook/react/fabric/mounting/mountitems/MountItemFactory;
	public static final fun createDestroyViewMountItem (II)Lcom/facebook/react/fabric/mounting/mountitems/MountItem;
	public static final fun createDispatchCommandMountItem (IIILcom/facebook/react/bridge/ReadableArray;)Lcom/facebook/react/fabric/mounting/mountitems/DispatchCommandMountItem;
	public static final fun createDispatchCommandMountItem (IILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)Lcom/facebook/react/fabric/mounting/mountitems/DispatchCommandMountItem;
	public static final fun createIntBufferBatchMountItem (I[I[Ljava/lang/Object;I)Lcom/facebook/react/fabric/mounting/mountitems/MountItem;
	public static final fun createPreAllocateViewMountItem (IILjava/lang/String;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/uimanager/StateWrapper;Z)Lcom/facebook/react/fabric/mounting/mountitems/MountItem;
	public static final fun createSendAccessibilityEventMountItem (III)Lcom/facebook/react/fabric/mounting/mountitems/MountItem;
}

public abstract interface class com/facebook/react/interfaces/TaskInterface {
	public abstract fun getError ()Ljava/lang/Exception;
	public abstract fun getResult ()Ljava/lang/Object;
	public abstract fun isCancelled ()Z
	public abstract fun isCompleted ()Z
	public abstract fun isFaulted ()Z
	public abstract fun waitForCompletion ()V
	public abstract fun waitForCompletion (JLjava/util/concurrent/TimeUnit;)Z
}

public abstract interface class com/facebook/react/interfaces/exceptionmanager/ReactJsExceptionHandler$ProcessedError {
	public abstract fun getComponentStack ()Ljava/lang/String;
	public abstract fun getExtraData ()Lcom/facebook/react/bridge/ReadableMap;
	public abstract fun getId ()I
	public abstract fun getMessage ()Ljava/lang/String;
	public abstract fun getName ()Ljava/lang/String;
	public abstract fun getOriginalMessage ()Ljava/lang/String;
	public abstract fun getStack ()Ljava/util/List;
	public abstract fun isFatal ()Z
}

public abstract interface class com/facebook/react/interfaces/exceptionmanager/ReactJsExceptionHandler$ProcessedError$StackFrame {
	public abstract fun getColumn ()Ljava/lang/Integer;
	public abstract fun getFile ()Ljava/lang/String;
	public abstract fun getLineNumber ()Ljava/lang/Integer;
	public abstract fun getMethodName ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/interfaces/fabric/ReactSurface {
	public abstract fun clear ()V
	public abstract fun detach ()V
	public abstract fun getContext ()Landroid/content/Context;
	public abstract fun getModuleName ()Ljava/lang/String;
	public abstract fun getSurfaceID ()I
	public abstract fun getView ()Landroid/view/ViewGroup;
	public abstract fun isRunning ()Z
	public abstract fun prerender ()Lcom/facebook/react/interfaces/TaskInterface;
	public abstract fun start ()Lcom/facebook/react/interfaces/TaskInterface;
	public abstract fun stop ()Lcom/facebook/react/interfaces/TaskInterface;
}

public abstract interface class com/facebook/react/interfaces/fabric/SurfaceHandler {
	public abstract fun getModuleName ()Ljava/lang/String;
	public abstract fun getSurfaceId ()I
	public abstract fun isRunning ()Z
	public abstract fun setLayoutConstraints (IIIIZZF)V
	public abstract fun setMountable (Z)V
	public abstract fun setProps (Lcom/facebook/react/bridge/NativeMap;)V
}

public final class com/facebook/react/jscexecutor/JSCExecutor : com/facebook/react/bridge/JavaScriptExecutor {
	public fun getName ()Ljava/lang/String;
	public static final fun loadLibrary ()V
}

public final class com/facebook/react/jscexecutor/JSCExecutorFactory : com/facebook/react/bridge/JavaScriptExecutorFactory {
	public fun <init> (Ljava/lang/String;Ljava/lang/String;)V
	public fun create ()Lcom/facebook/react/bridge/JavaScriptExecutor;
	public fun startSamplingProfiler ()V
	public fun stopSamplingProfiler (Ljava/lang/String;)V
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/jstasks/HeadlessJsTaskConfig {
	public fun <init> (Lcom/facebook/react/jstasks/HeadlessJsTaskConfig;)V
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;J)V
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;JZ)V
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;JZLcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;)V
	public synthetic fun <init> (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;JZLcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public final fun getData ()Lcom/facebook/react/bridge/WritableMap;
	public final fun getRetryPolicy ()Lcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;
	public final fun getTaskKey ()Ljava/lang/String;
	public final fun getTimeout ()J
	public final fun isAllowedInForeground ()Z
}

public final class com/facebook/react/jstasks/HeadlessJsTaskContext {
	public static final field Companion Lcom/facebook/react/jstasks/HeadlessJsTaskContext$Companion;
	public synthetic fun <init> (Lcom/facebook/react/bridge/ReactContext;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public final fun addTaskEventListener (Lcom/facebook/react/jstasks/HeadlessJsTaskEventListener;)V
	public final fun finishTask (I)V
	public static final fun getInstance (Lcom/facebook/react/bridge/ReactContext;)Lcom/facebook/react/jstasks/HeadlessJsTaskContext;
	public final fun hasActiveTasks ()Z
	public final fun isTaskRunning (I)Z
	public final fun removeTaskEventListener (Lcom/facebook/react/jstasks/HeadlessJsTaskEventListener;)V
	public final fun retryTask (I)Z
	public final fun startTask (Lcom/facebook/react/jstasks/HeadlessJsTaskConfig;)I
}

public final class com/facebook/react/jstasks/HeadlessJsTaskContext$Companion {
	public final fun getInstance (Lcom/facebook/react/bridge/ReactContext;)Lcom/facebook/react/jstasks/HeadlessJsTaskContext;
}

public abstract interface class com/facebook/react/jstasks/HeadlessJsTaskEventListener {
	public abstract fun onHeadlessJsTaskFinish (I)V
	public abstract fun onHeadlessJsTaskStart (I)V
}

public abstract interface class com/facebook/react/jstasks/HeadlessJsTaskRetryPolicy {
	public abstract fun canRetry ()Z
	public abstract fun copy ()Lcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;
	public abstract fun getDelay ()I
	public abstract fun update ()Lcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;
}

public final class com/facebook/react/jstasks/LinearCountingRetryPolicy : com/facebook/react/jstasks/HeadlessJsTaskRetryPolicy {
	public fun <init> (II)V
	public fun canRetry ()Z
	public fun copy ()Lcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;
	public fun getDelay ()I
	public fun update ()Lcom/facebook/react/jstasks/HeadlessJsTaskRetryPolicy;
}

public abstract interface annotation class com/facebook/react/module/annotations/ReactModule : java/lang/annotation/Annotation {
	public abstract fun canOverrideExistingModule ()Z
	public abstract fun hasConstants ()Z
	public abstract fun isCxxModule ()Z
	public abstract fun name ()Ljava/lang/String;
	public abstract fun needsEagerInit ()Z
}

public abstract interface annotation class com/facebook/react/module/annotations/ReactModuleList : java/lang/annotation/Annotation {
	public abstract fun nativeModules ()[Ljava/lang/Class;
}

public final class com/facebook/react/module/model/ReactModuleInfo {
	public static final field Companion Lcom/facebook/react/module/model/ReactModuleInfo$Companion;
	public fun <init> (Ljava/lang/String;Ljava/lang/String;ZZZZ)V
	public fun <init> (Ljava/lang/String;Ljava/lang/String;ZZZZZ)V
	public final fun canOverrideExistingModule ()Z
	public static final fun classIsTurboModule (Ljava/lang/Class;)Z
	public final fun className ()Ljava/lang/String;
	public final fun isCxxModule ()Z
	public final fun isTurboModule ()Z
	public final fun name ()Ljava/lang/String;
	public final fun needsEagerInit ()Z
}

public final class com/facebook/react/module/model/ReactModuleInfo$Companion {
	public final fun classIsTurboModule (Ljava/lang/Class;)Z
}

public abstract interface class com/facebook/react/module/model/ReactModuleInfoProvider {
	public abstract fun getReactModuleInfos ()Ljava/util/Map;
}

public final class com/facebook/react/modules/appearance/AppearanceModule : com/facebook/fbreact/specs/NativeAppearanceSpec {
	public static final field Companion Lcom/facebook/react/modules/appearance/AppearanceModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/modules/appearance/AppearanceModule$OverrideColorScheme;)V
	public synthetic fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/modules/appearance/AppearanceModule$OverrideColorScheme;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun addListener (Ljava/lang/String;)V
	public final fun emitAppearanceChanged (Ljava/lang/String;)V
	public fun getColorScheme ()Ljava/lang/String;
	public final fun onConfigurationChanged (Landroid/content/Context;)V
	public fun removeListeners (D)V
	public fun setColorScheme (Ljava/lang/String;)V
}

public final class com/facebook/react/modules/appearance/AppearanceModule$Companion {
}

public abstract interface class com/facebook/react/modules/appearance/AppearanceModule$OverrideColorScheme {
	public abstract fun getScheme ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/modules/appregistry/AppRegistry : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun runApplication (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun startHeadlessTask (ILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun unmountApplicationComponentAtRootTag (I)V
}

public class com/facebook/react/modules/blob/BlobModule : com/facebook/fbreact/specs/NativeBlobModuleSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun addNetworkingHandler ()V
	public fun addWebSocketHandler (D)V
	public fun createFromParts (Lcom/facebook/react/bridge/ReadableArray;Ljava/lang/String;)V
	public fun getLengthOfBlob (Ljava/lang/String;)J
	public fun getTypedExportedConstants ()Ljava/util/Map;
	public fun initialize ()V
	public fun release (Ljava/lang/String;)V
	public fun remove (Ljava/lang/String;)V
	public fun removeWebSocketHandler (D)V
	public fun resolve (Landroid/net/Uri;)[B
	public fun resolve (Lcom/facebook/react/bridge/ReadableMap;)[B
	public fun resolve (Ljava/lang/String;II)[B
	public fun sendOverSocket (Lcom/facebook/react/bridge/ReadableMap;D)V
	public fun store ([B)Ljava/lang/String;
	public fun store ([BLjava/lang/String;)V
}

public final class com/facebook/react/modules/blob/BlobProvider : android/content/ContentProvider {
	public fun <init> ()V
	public fun delete (Landroid/net/Uri;Ljava/lang/String;[Ljava/lang/String;)I
	public fun getType (Landroid/net/Uri;)Ljava/lang/String;
	public fun insert (Landroid/net/Uri;Landroid/content/ContentValues;)Landroid/net/Uri;
	public fun onCreate ()Z
	public fun openFile (Landroid/net/Uri;Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;
	public fun query (Landroid/net/Uri;[Ljava/lang/String;Ljava/lang/String;[Ljava/lang/String;Ljava/lang/String;)Landroid/database/Cursor;
	public fun update (Landroid/net/Uri;Landroid/content/ContentValues;Ljava/lang/String;[Ljava/lang/String;)I
}

public class com/facebook/react/modules/blob/FileReaderModule : com/facebook/fbreact/specs/NativeFileReaderModuleSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun readAsDataURL (Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Promise;)V
	public fun readAsText (Lcom/facebook/react/bridge/ReadableMap;Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
}

public final class com/facebook/react/modules/common/ModuleDataCleaner {
	public static final field INSTANCE Lcom/facebook/react/modules/common/ModuleDataCleaner;
	public static final fun cleanDataFromModules (Lcom/facebook/react/bridge/ReactContext;)V
}

public abstract interface class com/facebook/react/modules/common/ModuleDataCleaner$Cleanable {
	public abstract fun clearSensitiveData ()V
}

public class com/facebook/react/modules/core/ChoreographerCompat {
	public fun <init> ()V
}

public abstract class com/facebook/react/modules/core/ChoreographerCompat$FrameCallback : android/view/Choreographer$FrameCallback {
	public fun <init> ()V
}

public abstract interface class com/facebook/react/modules/core/DefaultHardwareBackBtnHandler {
	public abstract fun invokeDefaultOnBackPressed ()V
}

public class com/facebook/react/modules/core/DeviceEventManagerModule : com/facebook/fbreact/specs/NativeDeviceEventManagerSpec {
	public static final field Companion Lcom/facebook/react/modules/core/DeviceEventManagerModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/modules/core/DefaultHardwareBackBtnHandler;)V
	public fun emitHardwareBackPressed ()V
	public fun emitNewIntentReceived (Landroid/net/Uri;)V
	public fun invokeDefaultBackPressHandler ()V
}

public final class com/facebook/react/modules/core/DeviceEventManagerModule$Companion {
}

public abstract interface class com/facebook/react/modules/core/DeviceEventManagerModule$RCTDeviceEventEmitter : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun emit (Ljava/lang/String;Ljava/lang/Object;)V
}

public class com/facebook/react/modules/core/ExceptionsManagerModule : com/facebook/fbreact/specs/NativeExceptionsManagerSpec {
	public fun <init> (Lcom/facebook/react/devsupport/interfaces/DevSupportManager;)V
	public fun dismissRedbox ()V
	public fun reportException (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun reportFatalException (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;D)V
	public fun reportSoftException (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;D)V
}

public class com/facebook/react/modules/core/HeadlessJsTaskSupportModule : com/facebook/fbreact/specs/NativeHeadlessJsTaskSupportSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun notifyTaskFinished (D)V
	public fun notifyTaskRetry (DLcom/facebook/react/bridge/Promise;)V
}

public abstract interface class com/facebook/react/modules/core/JSTimers : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun callIdleCallbacks (D)V
	public abstract fun callTimers (Lcom/facebook/react/bridge/WritableArray;)V
	public abstract fun emitTimeDriftWarning (Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/modules/core/JavaScriptTimerExecutor {
	public abstract fun callIdleCallbacks (D)V
	public abstract fun callTimers (Lcom/facebook/react/bridge/WritableArray;)V
	public abstract fun emitTimeDriftWarning (Ljava/lang/String;)V
}

public class com/facebook/react/modules/core/JavaTimerManager : com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/jstasks/HeadlessJsTaskEventListener {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/modules/core/JavaScriptTimerExecutor;Lcom/facebook/react/modules/core/ReactChoreographer;Lcom/facebook/react/devsupport/interfaces/DevSupportManager;)V
	public fun createAndMaybeCallTimer (IIDZ)V
	public fun createTimer (IJZ)V
	public fun deleteTimer (I)V
	public fun onHeadlessJsTaskFinish (I)V
	public fun onHeadlessJsTaskStart (I)V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun onInstanceDestroy ()V
	public fun setSendIdleEvents (Z)V
}

public abstract interface class com/facebook/react/modules/core/PermissionAwareActivity {
	public abstract fun checkPermission (Ljava/lang/String;II)I
	public abstract fun checkSelfPermission (Ljava/lang/String;)I
	public abstract fun requestPermissions ([Ljava/lang/String;ILcom/facebook/react/modules/core/PermissionListener;)V
	public abstract fun shouldShowRequestPermissionRationale (Ljava/lang/String;)Z
}

public abstract interface class com/facebook/react/modules/core/PermissionListener {
	public abstract fun onRequestPermissionsResult (I[Ljava/lang/String;[I)Z
}

public abstract interface class com/facebook/react/modules/core/RCTNativeAppEventEmitter : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun emit (Ljava/lang/String;Ljava/lang/Object;)V
}

public final class com/facebook/react/modules/core/ReactChoreographer {
	public static final field Companion Lcom/facebook/react/modules/core/ReactChoreographer$Companion;
	public synthetic fun <init> (Lcom/facebook/react/internal/ChoreographerProvider;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun getInstance ()Lcom/facebook/react/modules/core/ReactChoreographer;
	public static final fun initialize (Lcom/facebook/react/internal/ChoreographerProvider;)V
	public final fun postFrameCallback (Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;Landroid/view/Choreographer$FrameCallback;)V
	public final fun removeFrameCallback (Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;Landroid/view/Choreographer$FrameCallback;)V
}

public final class com/facebook/react/modules/core/ReactChoreographer$CallbackType : java/lang/Enum {
	public static final field DISPATCH_UI Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
	public static final field IDLE_EVENT Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
	public static final field NATIVE_ANIMATED_MODULE Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
	public static final field PERF_MARKERS Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
	public static final field TIMERS_EVENTS Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
	public static fun values ()[Lcom/facebook/react/modules/core/ReactChoreographer$CallbackType;
}

public final class com/facebook/react/modules/core/ReactChoreographer$Companion {
	public final fun getInstance ()Lcom/facebook/react/modules/core/ReactChoreographer;
	public final fun initialize (Lcom/facebook/react/internal/ChoreographerProvider;)V
}

public final class com/facebook/react/modules/core/TimingModule : com/facebook/fbreact/specs/NativeTimingSpec, com/facebook/react/modules/core/JavaScriptTimerExecutor {
	public static final field Companion Lcom/facebook/react/modules/core/TimingModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/devsupport/interfaces/DevSupportManager;)V
	public fun callIdleCallbacks (D)V
	public fun callTimers (Lcom/facebook/react/bridge/WritableArray;)V
	public fun createTimer (DDDZ)V
	public fun deleteTimer (D)V
	public fun emitTimeDriftWarning (Ljava/lang/String;)V
	public fun invalidate ()V
	public fun setSendIdleEvents (Z)V
}

public final class com/facebook/react/modules/core/TimingModule$Companion {
}

public final class com/facebook/react/modules/debug/DevMenuModule : com/facebook/fbreact/specs/NativeDevMenuSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/devsupport/interfaces/DevSupportManager;)V
	public fun reload ()V
	public fun setHotLoadingEnabled (Z)V
	public fun setProfilingEnabled (Z)V
	public fun show ()V
}

public final class com/facebook/react/modules/debug/DevSettingsModule : com/facebook/fbreact/specs/NativeDevSettingsSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/devsupport/interfaces/DevSupportManager;)V
	public fun addListener (Ljava/lang/String;)V
	public fun addMenuItem (Ljava/lang/String;)V
	public fun onFastRefresh ()V
	public fun openDebugger ()V
	public fun reload ()V
	public fun reloadWithReason (Ljava/lang/String;)V
	public fun removeListeners (D)V
	public fun setHotLoadingEnabled (Z)V
	public fun setIsShakeToShowDevMenuEnabled (Z)V
	public fun setProfilingEnabled (Z)V
	public fun toggleElementInspector ()V
}

public final class com/facebook/react/modules/debug/FpsDebugFrameCallback : android/view/Choreographer$FrameCallback {
	public fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public fun doFrame (J)V
	public final fun get4PlusFrameStutters ()I
	public final fun getExpectedNumFrames ()I
	public final fun getFps ()D
	public final fun getFpsInfo (J)Lcom/facebook/react/modules/debug/FpsDebugFrameCallback$FpsInfo;
	public final fun getJsFPS ()D
	public final fun getNumFrames ()I
	public final fun getNumJSFrames ()I
	public final fun getTotalTimeMS ()I
	public final fun reset ()V
	public final fun start ()V
	public final fun start (D)V
	public static synthetic fun start$default (Lcom/facebook/react/modules/debug/FpsDebugFrameCallback;DILjava/lang/Object;)V
	public final fun startAndRecordFpsAtEachFrame ()V
	public final fun stop ()V
}

public final class com/facebook/react/modules/debug/FpsDebugFrameCallback$FpsInfo {
	public fun <init> (IIIIDDI)V
	public final fun getFps ()D
	public final fun getJsFps ()D
	public final fun getTotal4PlusFrameStutters ()I
	public final fun getTotalExpectedFrames ()I
	public final fun getTotalFrames ()I
	public final fun getTotalJsFrames ()I
	public final fun getTotalTimeMs ()I
}

public final class com/facebook/react/modules/debug/SourceCodeModule : com/facebook/fbreact/specs/NativeSourceCodeSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
}

public abstract interface class com/facebook/react/modules/debug/interfaces/DeveloperSettings {
	public abstract fun addMenuItem (Ljava/lang/String;)V
	public abstract fun getPackagerConnectionSettings ()Lcom/facebook/react/packagerconnection/PackagerConnectionSettings;
	public abstract fun isAnimationFpsDebugEnabled ()Z
	public abstract fun isDeviceDebugEnabled ()Z
	public abstract fun isElementInspectorEnabled ()Z
	public abstract fun isFpsDebugEnabled ()Z
	public abstract fun isHotModuleReplacementEnabled ()Z
	public abstract fun isJSDevModeEnabled ()Z
	public abstract fun isJSMinifyEnabled ()Z
	public abstract fun isStartSamplingProfilerOnInit ()Z
	public abstract fun setAnimationFpsDebugEnabled (Z)V
	public abstract fun setDeviceDebugEnabled (Z)V
	public abstract fun setElementInspectorEnabled (Z)V
	public abstract fun setFpsDebugEnabled (Z)V
	public abstract fun setHotModuleReplacementEnabled (Z)V
	public abstract fun setJSDevModeEnabled (Z)V
	public abstract fun setJSMinifyEnabled (Z)V
	public abstract fun setStartSamplingProfilerOnInit (Z)V
}

public final class com/facebook/react/modules/deviceinfo/DeviceInfoModule : com/facebook/fbreact/specs/NativeDeviceInfoSpec, com/facebook/react/bridge/LifecycleEventListener {
	public fun <init> (Landroid/content/Context;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public final fun emitUpdateDimensionsEvent ()V
	public fun getTypedExportedConstants ()Ljava/util/Map;
	public fun invalidate ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
}

public class com/facebook/react/modules/dialog/AlertFragment : androidx/fragment/app/DialogFragment, android/content/DialogInterface$OnClickListener {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/modules/dialog/DialogModule$AlertFragmentListener;Landroid/os/Bundle;)V
	public static fun createDialog (Landroid/content/Context;Landroid/os/Bundle;Landroid/content/DialogInterface$OnClickListener;)Landroid/app/Dialog;
	public fun onClick (Landroid/content/DialogInterface;I)V
	public fun onCreateDialog (Landroid/os/Bundle;)Landroid/app/Dialog;
	public fun onDismiss (Landroid/content/DialogInterface;)V
}

public class com/facebook/react/modules/dialog/DialogModule : com/facebook/fbreact/specs/NativeDialogManagerAndroidSpec, com/facebook/react/bridge/LifecycleEventListener {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun getTypedExportedConstants ()Ljava/util/Map;
	public fun initialize ()V
	public fun invalidate ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun showAlert (Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;Lcom/facebook/react/bridge/Callback;)V
}

public class com/facebook/react/modules/fresco/FrescoModule : com/facebook/react/bridge/ReactContextBaseJavaModule, com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/modules/common/ModuleDataCleaner$Cleanable, com/facebook/react/turbomodule/core/interfaces/TurboModule {
	public static final field Companion Lcom/facebook/react/modules/fresco/FrescoModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/imagepipeline/core/ImagePipeline;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/imagepipeline/core/ImagePipeline;Z)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/imagepipeline/core/ImagePipeline;ZZ)V
	public synthetic fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/imagepipeline/core/ImagePipeline;ZZILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Z)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;ZLcom/facebook/imagepipeline/core/ImagePipelineConfig;)V
	public synthetic fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;ZLcom/facebook/imagepipeline/core/ImagePipelineConfig;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun clearSensitiveData ()V
	public static final fun getDefaultConfigBuilder (Lcom/facebook/react/bridge/ReactContext;)Lcom/facebook/imagepipeline/core/ImagePipelineConfig$Builder;
	public fun getName ()Ljava/lang/String;
	public static final fun hasBeenInitialized ()Z
	public fun initialize ()V
	public fun invalidate ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
}

public final class com/facebook/react/modules/fresco/FrescoModule$Companion {
	public final fun getDefaultConfigBuilder (Lcom/facebook/react/bridge/ReactContext;)Lcom/facebook/imagepipeline/core/ImagePipelineConfig$Builder;
	public final fun hasBeenInitialized ()Z
}

public final class com/facebook/react/modules/fresco/ImageCacheControl : java/lang/Enum {
	public static final field DEFAULT Lcom/facebook/react/modules/fresco/ImageCacheControl;
	public static final field FORCE_CACHE Lcom/facebook/react/modules/fresco/ImageCacheControl;
	public static final field ONLY_IF_CACHED Lcom/facebook/react/modules/fresco/ImageCacheControl;
	public static final field RELOAD Lcom/facebook/react/modules/fresco/ImageCacheControl;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/modules/fresco/ImageCacheControl;
	public static fun values ()[Lcom/facebook/react/modules/fresco/ImageCacheControl;
}

public final class com/facebook/react/modules/fresco/ReactNetworkImageRequest : com/facebook/imagepipeline/request/ImageRequest {
	public static final field Companion Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest$Companion;
	public synthetic fun <init> (Lcom/facebook/imagepipeline/request/ImageRequestBuilder;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/modules/fresco/ImageCacheControl;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun fromBuilderWithHeaders (Lcom/facebook/imagepipeline/request/ImageRequestBuilder;Lcom/facebook/react/bridge/ReadableMap;)Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest;
	public static final fun fromBuilderWithHeaders (Lcom/facebook/imagepipeline/request/ImageRequestBuilder;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/modules/fresco/ImageCacheControl;)Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest;
}

public final class com/facebook/react/modules/fresco/ReactNetworkImageRequest$Companion {
	public final fun fromBuilderWithHeaders (Lcom/facebook/imagepipeline/request/ImageRequestBuilder;Lcom/facebook/react/bridge/ReadableMap;)Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest;
	public final fun fromBuilderWithHeaders (Lcom/facebook/imagepipeline/request/ImageRequestBuilder;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/modules/fresco/ImageCacheControl;)Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest;
	public static synthetic fun fromBuilderWithHeaders$default (Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest$Companion;Lcom/facebook/imagepipeline/request/ImageRequestBuilder;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/modules/fresco/ImageCacheControl;ILjava/lang/Object;)Lcom/facebook/react/modules/fresco/ReactNetworkImageRequest;
}

public final class com/facebook/react/modules/i18nmanager/I18nManagerModule : com/facebook/fbreact/specs/NativeI18nManagerSpec {
	public static final field Companion Lcom/facebook/react/modules/i18nmanager/I18nManagerModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun allowRTL (Z)V
	public fun forceRTL (Z)V
	public fun getTypedExportedConstants ()Ljava/util/Map;
	public fun swapLeftAndRightInRTL (Z)V
}

public final class com/facebook/react/modules/i18nmanager/I18nManagerModule$Companion {
}

public final class com/facebook/react/modules/i18nmanager/I18nUtil {
	public static final field Companion Lcom/facebook/react/modules/i18nmanager/I18nUtil$Companion;
	public final fun allowRTL (Landroid/content/Context;Z)V
	public final fun doLeftAndRightSwapInRTL (Landroid/content/Context;)Z
	public final fun forceRTL (Landroid/content/Context;Z)V
	public static final fun getInstance ()Lcom/facebook/react/modules/i18nmanager/I18nUtil;
	public final fun isRTL (Landroid/content/Context;)Z
	public final fun swapLeftAndRightInRTL (Landroid/content/Context;Z)V
}

public final class com/facebook/react/modules/i18nmanager/I18nUtil$Companion {
	public final fun DEPRECATED$getInstance ()Lcom/facebook/react/modules/i18nmanager/I18nUtil;
	public final fun getInstance ()Lcom/facebook/react/modules/i18nmanager/I18nUtil;
}

public final class com/facebook/react/modules/image/ImageLoaderModule : com/facebook/fbreact/specs/NativeImageLoaderAndroidSpec, com/facebook/react/bridge/LifecycleEventListener {
	public static final field Companion Lcom/facebook/react/modules/image/ImageLoaderModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/imagepipeline/core/ImagePipeline;Lcom/facebook/react/views/image/ReactCallerContextFactory;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/lang/Object;)V
	public fun abortRequest (D)V
	public fun getSize (Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
	public fun getSizeWithHeaders (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Promise;)V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun prefetchImage (Ljava/lang/String;DLcom/facebook/react/bridge/Promise;)V
	public fun queryCache (Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Promise;)V
}

public final class com/facebook/react/modules/image/ImageLoaderModule$Companion {
}

public class com/facebook/react/modules/intent/IntentModule : com/facebook/fbreact/specs/NativeIntentAndroidSpec {
	public static final field Companion Lcom/facebook/react/modules/intent/IntentModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun canOpenURL (Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
	public fun getInitialURL (Lcom/facebook/react/bridge/Promise;)V
	public fun invalidate ()V
	public fun openSettings (Lcom/facebook/react/bridge/Promise;)V
	public fun openURL (Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
	public fun sendIntent (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Promise;)V
}

public final class com/facebook/react/modules/intent/IntentModule$Companion {
}

public abstract interface class com/facebook/react/modules/network/CookieJarContainer : okhttp3/CookieJar {
	public abstract fun removeCookieJar ()V
	public abstract fun setCookieJar (Lokhttp3/CookieJar;)V
}

public abstract interface class com/facebook/react/modules/network/CustomClientBuilder {
	public abstract fun apply (Lokhttp3/OkHttpClient$Builder;)V
}

public final class com/facebook/react/modules/network/ForwardingCookieHandler : java/net/CookieHandler {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public final fun addCookies (Ljava/lang/String;Ljava/util/List;)V
	public final fun clearCookies (Lcom/facebook/react/bridge/Callback;)V
	public final fun destroy ()V
	public fun get (Ljava/net/URI;Ljava/util/Map;)Ljava/util/Map;
	public fun put (Ljava/net/URI;Ljava/util/Map;)V
}

public abstract interface class com/facebook/react/modules/network/NetworkInterceptorCreator {
	public abstract fun create ()Lokhttp3/Interceptor;
}

public final class com/facebook/react/modules/network/NetworkingModule : com/facebook/fbreact/specs/NativeNetworkingAndroidSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/lang/String;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/lang/String;Lokhttp3/OkHttpClient;Ljava/util/List;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;)V
	public fun abortRequest (D)V
	public fun addListener (Ljava/lang/String;)V
	public fun addRequestBodyHandler (Lcom/facebook/react/modules/network/NetworkingModule$RequestBodyHandler;)V
	public fun addResponseHandler (Lcom/facebook/react/modules/network/NetworkingModule$ResponseHandler;)V
	public fun addUriHandler (Lcom/facebook/react/modules/network/NetworkingModule$UriHandler;)V
	public fun clearCookies (Lcom/facebook/react/bridge/Callback;)V
	public fun initialize ()V
	public fun invalidate ()V
	public fun removeListeners (D)V
	public fun removeRequestBodyHandler (Lcom/facebook/react/modules/network/NetworkingModule$RequestBodyHandler;)V
	public fun removeResponseHandler (Lcom/facebook/react/modules/network/NetworkingModule$ResponseHandler;)V
	public fun removeUriHandler (Lcom/facebook/react/modules/network/NetworkingModule$UriHandler;)V
	public fun sendRequest (Ljava/lang/String;Ljava/lang/String;DLcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableMap;Ljava/lang/String;ZDZ)V
	public fun sendRequestInternal (Ljava/lang/String;Ljava/lang/String;ILcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableMap;Ljava/lang/String;ZIZ)V
	public static fun setCustomClientBuilder (Lcom/facebook/react/modules/network/CustomClientBuilder;)V
}

public abstract interface class com/facebook/react/modules/network/NetworkingModule$CustomClientBuilder : com/facebook/react/modules/network/CustomClientBuilder {
}

public abstract interface class com/facebook/react/modules/network/NetworkingModule$RequestBodyHandler {
	public abstract fun supports (Lcom/facebook/react/bridge/ReadableMap;)Z
	public abstract fun toRequestBody (Lcom/facebook/react/bridge/ReadableMap;Ljava/lang/String;)Lokhttp3/RequestBody;
}

public abstract interface class com/facebook/react/modules/network/NetworkingModule$ResponseHandler {
	public abstract fun supports (Ljava/lang/String;)Z
	public abstract fun toResponseData (Lokhttp3/ResponseBody;)Lcom/facebook/react/bridge/WritableMap;
}

public abstract interface class com/facebook/react/modules/network/NetworkingModule$UriHandler {
	public abstract fun fetch (Landroid/net/Uri;)Lcom/facebook/react/bridge/WritableMap;
	public abstract fun supports (Landroid/net/Uri;Ljava/lang/String;)Z
}

public abstract interface class com/facebook/react/modules/network/OkHttpClientFactory {
	public abstract fun createNewNetworkModuleClient ()Lokhttp3/OkHttpClient;
}

public final class com/facebook/react/modules/network/OkHttpClientProvider {
	public static final field INSTANCE Lcom/facebook/react/modules/network/OkHttpClientProvider;
	public static final fun createClient ()Lokhttp3/OkHttpClient;
	public static final fun createClient (Landroid/content/Context;)Lokhttp3/OkHttpClient;
	public static final fun createClientBuilder ()Lokhttp3/OkHttpClient$Builder;
	public static final fun createClientBuilder (Landroid/content/Context;)Lokhttp3/OkHttpClient$Builder;
	public static final fun createClientBuilder (Landroid/content/Context;I)Lokhttp3/OkHttpClient$Builder;
	public static final fun getOkHttpClient ()Lokhttp3/OkHttpClient;
	public static final fun setOkHttpClientFactory (Lcom/facebook/react/modules/network/OkHttpClientFactory;)V
}

public class com/facebook/react/modules/network/OkHttpCompat {
	public fun <init> ()V
	public static fun getCookieJarContainer (Lokhttp3/OkHttpClient;)Lcom/facebook/react/modules/network/CookieJarContainer;
	public static fun getHeadersFromMap (Ljava/util/Map;)Lokhttp3/Headers;
}

public abstract interface class com/facebook/react/modules/network/ProgressListener {
	public abstract fun onProgress (JJZ)V
}

public class com/facebook/react/modules/network/ProgressResponseBody : okhttp3/ResponseBody {
	public fun <init> (Lokhttp3/ResponseBody;Lcom/facebook/react/modules/network/ProgressListener;)V
	public fun contentLength ()J
	public fun contentType ()Lokhttp3/MediaType;
	public fun source ()Lokio/BufferedSource;
	public fun totalBytesRead ()J
}

public final class com/facebook/react/modules/network/ReactCookieJarContainer : com/facebook/react/modules/network/CookieJarContainer {
	public fun <init> ()V
	public fun loadForRequest (Lokhttp3/HttpUrl;)Ljava/util/List;
	public fun removeCookieJar ()V
	public fun saveFromResponse (Lokhttp3/HttpUrl;Ljava/util/List;)V
	public fun setCookieJar (Lokhttp3/CookieJar;)V
}

public final class com/facebook/react/modules/permissions/PermissionsModule : com/facebook/fbreact/specs/NativePermissionsAndroidSpec, com/facebook/react/modules/core/PermissionListener {
	public static final field Companion Lcom/facebook/react/modules/permissions/PermissionsModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun checkPermission (Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
	public fun onRequestPermissionsResult (I[Ljava/lang/String;[I)Z
	public fun requestMultiplePermissions (Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Promise;)V
	public fun requestPermission (Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
	public fun shouldShowRequestPermissionRationale (Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
}

public final class com/facebook/react/modules/permissions/PermissionsModule$Companion {
}

public final class com/facebook/react/modules/share/ShareModule : com/facebook/fbreact/specs/NativeShareModuleSpec {
	public static final field Companion Lcom/facebook/react/modules/share/ShareModule$Companion;
	public static final field ERROR_INVALID_CONTENT Ljava/lang/String;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun share (Lcom/facebook/react/bridge/ReadableMap;Ljava/lang/String;Lcom/facebook/react/bridge/Promise;)V
}

public final class com/facebook/react/modules/share/ShareModule$Companion {
}

public final class com/facebook/react/modules/sound/SoundManagerModule : com/facebook/fbreact/specs/NativeSoundManagerSpec {
	public static final field Companion Lcom/facebook/react/modules/sound/SoundManagerModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun playTouchSound ()V
}

public final class com/facebook/react/modules/sound/SoundManagerModule$Companion {
}

public final class com/facebook/react/modules/statusbar/StatusBarModule : com/facebook/fbreact/specs/NativeStatusBarManagerAndroidSpec {
	public static final field Companion Lcom/facebook/react/modules/statusbar/StatusBarModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun setColor (DZ)V
	public fun setHidden (Z)V
	public fun setStyle (Ljava/lang/String;)V
	public fun setTranslucent (Z)V
}

public final class com/facebook/react/modules/statusbar/StatusBarModule$Companion {
}

public final class com/facebook/react/modules/systeminfo/AndroidInfoHelpers {
	public static final field DEVICE_LOCALHOST Ljava/lang/String;
	public static final field EMULATOR_LOCALHOST Ljava/lang/String;
	public static final field GENYMOTION_LOCALHOST Ljava/lang/String;
	public static final field INSTANCE Lcom/facebook/react/modules/systeminfo/AndroidInfoHelpers;
	public static final field METRO_HOST_PROP_NAME Ljava/lang/String;
	public static final fun getAdbReverseTcpCommand (I)Ljava/lang/String;
	public static final fun getAdbReverseTcpCommand (Landroid/content/Context;)Ljava/lang/String;
	public static final fun getFriendlyDeviceName ()Ljava/lang/String;
	public static final fun getInspectorHostMetadata (Landroid/content/Context;)Ljava/util/Map;
	public static final fun getServerHost (I)Ljava/lang/String;
	public static final fun getServerHost (Landroid/content/Context;)Ljava/lang/String;
}

public final class com/facebook/react/modules/systeminfo/AndroidInfoModule : com/facebook/fbreact/specs/NativePlatformConstantsAndroidSpec {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun getAndroidID ()Ljava/lang/String;
	public fun invalidate ()V
}

public class com/facebook/react/modules/systeminfo/ReactNativeVersion {
	public static final field VERSION Ljava/util/Map;
	public fun <init> ()V
}

public final class com/facebook/react/modules/toast/ToastModule : com/facebook/fbreact/specs/NativeToastAndroidSpec {
	public static final field Companion Lcom/facebook/react/modules/toast/ToastModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun getTypedExportedConstants ()Ljava/util/Map;
	public fun show (Ljava/lang/String;D)V
	public fun showWithGravity (Ljava/lang/String;DD)V
	public fun showWithGravityAndOffset (Ljava/lang/String;DDDD)V
}

public final class com/facebook/react/modules/toast/ToastModule$Companion {
}

public final class com/facebook/react/modules/websocket/WebSocketModule : com/facebook/fbreact/specs/NativeWebSocketModuleSpec {
	public static final field Companion Lcom/facebook/react/modules/websocket/WebSocketModule$Companion;
	public static final field NAME Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun addListener (Ljava/lang/String;)V
	public fun close (DLjava/lang/String;D)V
	public fun connect (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableMap;D)V
	public fun invalidate ()V
	public fun ping (D)V
	public fun removeListeners (D)V
	public fun send (Ljava/lang/String;D)V
	public fun sendBinary (Ljava/lang/String;D)V
	public final fun sendBinary (Lokio/ByteString;I)V
	public final fun setContentHandler (ILcom/facebook/react/modules/websocket/WebSocketModule$ContentHandler;)V
	public static final fun setCustomClientBuilder (Lcom/facebook/react/modules/network/CustomClientBuilder;)V
}

public final class com/facebook/react/modules/websocket/WebSocketModule$Companion {
	public final fun setCustomClientBuilder (Lcom/facebook/react/modules/network/CustomClientBuilder;)V
}

public abstract interface class com/facebook/react/modules/websocket/WebSocketModule$ContentHandler {
	public abstract fun onMessage (Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun onMessage (Lokio/ByteString;Lcom/facebook/react/bridge/WritableMap;)V
}

public class com/facebook/react/packagerconnection/FileIoHandler : java/lang/Runnable {
	public fun <init> ()V
	public fun handlers ()Ljava/util/Map;
	public fun run ()V
}

public final class com/facebook/react/packagerconnection/JSPackagerClient : com/facebook/react/packagerconnection/ReconnectingWebSocket$MessageCallback {
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/packagerconnection/PackagerConnectionSettings;Ljava/util/Map;)V
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/packagerconnection/PackagerConnectionSettings;Ljava/util/Map;Lcom/facebook/react/packagerconnection/ReconnectingWebSocket$ConnectionCallback;)V
	public fun close ()V
	public fun init ()V
	public fun onMessage (Ljava/lang/String;)V
	public fun onMessage (Lokio/ByteString;)V
}

public abstract class com/facebook/react/packagerconnection/NotificationOnlyHandler : com/facebook/react/packagerconnection/RequestHandler {
	public fun <init> ()V
	public abstract fun onNotification (Ljava/lang/Object;)V
	public final fun onRequest (Ljava/lang/Object;Lcom/facebook/react/packagerconnection/Responder;)V
}

public class com/facebook/react/packagerconnection/PackagerConnectionSettings {
	public fun <init> (Landroid/content/Context;)V
	public final fun getAdditionalOptionsForPackager ()Ljava/util/Map;
	public fun getDebugServerHost ()Ljava/lang/String;
	public final fun getPackageName ()Ljava/lang/String;
	public final fun setAdditionalOptionForPackager (Ljava/lang/String;Ljava/lang/String;)V
	public fun setDebugServerHost (Ljava/lang/String;)V
}

public final class com/facebook/react/packagerconnection/ReconnectingWebSocket : okhttp3/WebSocketListener {
	public fun <init> (Ljava/lang/String;Lcom/facebook/react/packagerconnection/ReconnectingWebSocket$MessageCallback;Lcom/facebook/react/packagerconnection/ReconnectingWebSocket$ConnectionCallback;)V
	public fun closeQuietly ()V
	public fun connect ()V
	public fun onClosed (Lokhttp3/WebSocket;ILjava/lang/String;)V
	public fun onFailure (Lokhttp3/WebSocket;Ljava/lang/Throwable;Lokhttp3/Response;)V
	public fun onMessage (Lokhttp3/WebSocket;Ljava/lang/String;)V
	public fun onMessage (Lokhttp3/WebSocket;Lokio/ByteString;)V
	public fun onOpen (Lokhttp3/WebSocket;Lokhttp3/Response;)V
	public fun sendMessage (Ljava/lang/String;)V
	public fun sendMessage (Lokio/ByteString;)V
}

public abstract interface class com/facebook/react/packagerconnection/ReconnectingWebSocket$ConnectionCallback {
	public abstract fun onConnected ()V
	public abstract fun onDisconnected ()V
}

public abstract interface class com/facebook/react/packagerconnection/ReconnectingWebSocket$MessageCallback {
	public abstract fun onMessage (Ljava/lang/String;)V
	public abstract fun onMessage (Lokio/ByteString;)V
}

public abstract interface class com/facebook/react/packagerconnection/RequestHandler {
	public abstract fun onNotification (Ljava/lang/Object;)V
	public abstract fun onRequest (Ljava/lang/Object;Lcom/facebook/react/packagerconnection/Responder;)V
}

public abstract class com/facebook/react/packagerconnection/RequestOnlyHandler : com/facebook/react/packagerconnection/RequestHandler {
	public fun <init> ()V
	public final fun onNotification (Ljava/lang/Object;)V
	public abstract fun onRequest (Ljava/lang/Object;Lcom/facebook/react/packagerconnection/Responder;)V
}

public abstract interface class com/facebook/react/packagerconnection/Responder {
	public abstract fun error (Ljava/lang/Object;)V
	public abstract fun respond (Ljava/lang/Object;)V
}

public abstract class com/facebook/react/runtime/BindingsInstaller {
	public fun <init> (Lcom/facebook/jni/HybridData;)V
}

public final class com/facebook/react/runtime/BridgelessCatalystInstance : com/facebook/react/bridge/CatalystInstance {
	public fun <init> (Lcom/facebook/react/runtime/ReactHostImpl;)V
	public fun addBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)V
	public fun callFunction (Ljava/lang/String;Ljava/lang/String;Lcom/facebook/react/bridge/NativeArray;)V
	public fun destroy ()V
	public fun extendNativeModules (Lcom/facebook/react/bridge/NativeModuleRegistry;)V
	public fun getFabricUIManager ()Lcom/facebook/react/bridge/UIManager;
	public fun getJSCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/CallInvokerHolder;
	public fun getJSModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/JavaScriptModule;
	public fun getJavaScriptContextHolder ()Lcom/facebook/react/bridge/JavaScriptContextHolder;
	public fun getNativeMethodCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/NativeMethodCallInvokerHolder;
	public fun getNativeModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/NativeModule;
	public fun getNativeModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public fun getNativeModules ()Ljava/util/Collection;
	public fun getReactQueueConfiguration ()Lcom/facebook/react/bridge/queue/ReactQueueConfiguration;
	public fun getRuntimeExecutor ()Lcom/facebook/react/bridge/RuntimeExecutor;
	public fun getRuntimeScheduler ()Lcom/facebook/react/bridge/RuntimeScheduler;
	public fun getSourceURL ()Ljava/lang/String;
	public fun handleMemoryPressure (I)V
	public fun hasNativeModule (Ljava/lang/Class;)Z
	public fun hasRunJSBundle ()Z
	public fun invokeCallback (ILcom/facebook/react/bridge/NativeArrayInterface;)V
	public fun isDestroyed ()Z
	public fun loadScriptFromAssets (Landroid/content/res/AssetManager;Ljava/lang/String;Z)V
	public fun loadScriptFromFile (Ljava/lang/String;Ljava/lang/String;Z)V
	public fun loadSplitBundleFromFile (Ljava/lang/String;Ljava/lang/String;)V
	public fun registerSegment (ILjava/lang/String;)V
	public fun removeBridgeIdleDebugListener (Lcom/facebook/react/bridge/NotThreadSafeBridgeIdleDebugListener;)V
	public fun runJSBundle ()V
	public fun setFabricUIManager (Lcom/facebook/react/bridge/UIManager;)V
	public fun setSourceURLs (Ljava/lang/String;Ljava/lang/String;)V
	public fun setTurboModuleRegistry (Lcom/facebook/react/internal/turbomodule/core/interfaces/TurboModuleRegistry;)V
}

public class com/facebook/react/runtime/CoreReactPackage$$ReactModuleInfoProvider : com/facebook/react/module/model/ReactModuleInfoProvider {
	public fun <init> ()V
	public fun getReactModuleInfos ()Ljava/util/Map;
}

public final class com/facebook/react/runtime/JSCInstance : com/facebook/react/runtime/JSRuntimeFactory {
	public fun <init> ()V
}

public abstract class com/facebook/react/runtime/JSRuntimeFactory {
	public fun <init> (Lcom/facebook/jni/HybridData;)V
}

public class com/facebook/react/runtime/ReactHostImpl : com/facebook/react/ReactHost {
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/runtime/ReactHostDelegate;Lcom/facebook/react/fabric/ComponentFactory;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;ZZ)V
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/runtime/ReactHostDelegate;Lcom/facebook/react/fabric/ComponentFactory;Ljava/util/concurrent/Executor;Ljava/util/concurrent/Executor;ZZLcom/facebook/react/devsupport/DevSupportManagerFactory;)V
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/runtime/ReactHostDelegate;Lcom/facebook/react/fabric/ComponentFactory;ZZ)V
	public fun addBeforeDestroyListener (Lkotlin/jvm/functions/Function0;)V
	public fun addReactInstanceEventListener (Lcom/facebook/react/ReactInstanceEventListener;)V
	public fun createSurface (Landroid/content/Context;Ljava/lang/String;Landroid/os/Bundle;)Lcom/facebook/react/interfaces/fabric/ReactSurface;
	public fun destroy (Ljava/lang/String;Ljava/lang/Exception;)Lcom/facebook/react/interfaces/TaskInterface;
	public fun destroy (Ljava/lang/String;Ljava/lang/Exception;Lkotlin/jvm/functions/Function1;)Lcom/facebook/react/interfaces/TaskInterface;
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public fun getDevSupportManager ()Lcom/facebook/react/devsupport/interfaces/DevSupportManager;
	public fun getLifecycleState ()Lcom/facebook/react/common/LifecycleState;
	public fun getMemoryPressureRouter ()Lcom/facebook/react/MemoryPressureRouter;
	public fun getReactQueueConfiguration ()Lcom/facebook/react/bridge/queue/ReactQueueConfiguration;
	public fun invalidate ()V
	public fun onActivityResult (Landroid/app/Activity;IILandroid/content/Intent;)V
	public fun onBackPressed ()Z
	public fun onConfigurationChanged (Landroid/content/Context;)V
	public fun onHostDestroy ()V
	public fun onHostDestroy (Landroid/app/Activity;)V
	public fun onHostLeaveHint (Landroid/app/Activity;)V
	public fun onHostPause ()V
	public fun onHostPause (Landroid/app/Activity;)V
	public fun onHostResume (Landroid/app/Activity;)V
	public fun onHostResume (Landroid/app/Activity;Lcom/facebook/react/modules/core/DefaultHardwareBackBtnHandler;)V
	public fun onNewIntent (Landroid/content/Intent;)V
	public fun onWindowFocusChange (Z)V
	public fun reload (Ljava/lang/String;)Lcom/facebook/react/interfaces/TaskInterface;
	public fun removeBeforeDestroyListener (Lkotlin/jvm/functions/Function0;)V
	public fun removeReactInstanceEventListener (Lcom/facebook/react/ReactInstanceEventListener;)V
	public fun start ()Lcom/facebook/react/interfaces/TaskInterface;
}

public class com/facebook/react/runtime/ReactSurfaceImpl : com/facebook/react/interfaces/fabric/ReactSurface {
	public fun <init> (Landroid/content/Context;Ljava/lang/String;Landroid/os/Bundle;)V
	public fun attach (Lcom/facebook/react/ReactHost;)V
	public fun attachView (Lcom/facebook/react/runtime/ReactSurfaceView;)V
	public fun clear ()V
	public static fun createWithView (Landroid/content/Context;Ljava/lang/String;Landroid/os/Bundle;)Lcom/facebook/react/runtime/ReactSurfaceImpl;
	public fun detach ()V
	public fun getContext ()Landroid/content/Context;
	public fun getModuleName ()Ljava/lang/String;
	public fun getSurfaceID ()I
	public fun getView ()Landroid/view/ViewGroup;
	public fun isRunning ()Z
	public fun prerender ()Lcom/facebook/react/interfaces/TaskInterface;
	public fun start ()Lcom/facebook/react/interfaces/TaskInterface;
	public fun stop ()Lcom/facebook/react/interfaces/TaskInterface;
	public fun updateInitProps (Landroid/os/Bundle;)V
}

public final class com/facebook/react/runtime/ReactSurfaceView : com/facebook/react/ReactRootView {
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/runtime/ReactSurfaceImpl;)V
	public fun getCurrentReactContext ()Lcom/facebook/react/bridge/ReactContext;
	public fun getJSModuleName ()Ljava/lang/String;
	public fun getUIManagerType ()I
	public fun handleException (Ljava/lang/Throwable;)V
	public fun hasActiveReactContext ()Z
	public fun hasActiveReactInstance ()Z
	public fun isViewAttachedToReactInstance ()Z
	public fun onChildEndedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	public fun onChildStartedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	public fun requestDisallowInterceptTouchEvent (Z)V
	public fun setIsFabric (Z)V
}

public abstract class com/facebook/react/runtime/cxxreactpackage/CxxReactPackage {
	protected fun <init> (Lcom/facebook/jni/HybridData;)V
}

public final class com/facebook/react/runtime/hermes/HermesInstance : com/facebook/react/runtime/JSRuntimeFactory {
	public static final field Companion Lcom/facebook/react/runtime/hermes/HermesInstance$Companion;
	public fun <init> ()V
	public fun <init> (Z)V
}

public final class com/facebook/react/runtime/hermes/HermesInstance$Companion {
}

public final class com/facebook/react/shell/MainPackageConfig {
	public fun <init> (Lcom/facebook/imagepipeline/core/ImagePipelineConfig;)V
	public final fun getFrescoConfig ()Lcom/facebook/imagepipeline/core/ImagePipelineConfig;
}

public final class com/facebook/react/shell/MainReactPackage : com/facebook/react/BaseReactPackage, com/facebook/react/ViewManagerOnDemandReactPackage {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/shell/MainPackageConfig;)V
	public synthetic fun <init> (Lcom/facebook/react/shell/MainPackageConfig;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun createViewManager (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public fun createViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public fun getModule (Ljava/lang/String;Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/bridge/NativeModule;
	public fun getReactModuleInfoProvider ()Lcom/facebook/react/module/model/ReactModuleInfoProvider;
	public fun getViewManagerNames (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/Collection;
	public fun getViewManagers (Lcom/facebook/react/bridge/ReactApplicationContext;)Ljava/util/List;
	public final fun getViewManagersMap ()Ljava/util/Map;
}

public class com/facebook/react/shell/MainReactPackage$$ReactModuleInfoProvider : com/facebook/react/module/model/ReactModuleInfoProvider {
	public fun <init> ()V
	public fun getReactModuleInfos ()Ljava/util/Map;
}

public final class com/facebook/react/soloader/OpenSourceMergedSoMapping : com/facebook/soloader/ExternalSoMapping {
	public static final field INSTANCE Lcom/facebook/react/soloader/OpenSourceMergedSoMapping;
	public fun invokeJniOnload (Ljava/lang/String;)V
	public final fun libfabricjni_so ()I
	public final fun libhermes_executor_so ()I
	public final fun libhermesinstancejni_so ()I
	public final fun libhermestooling_so ()I
	public final fun libjscexecutor_so ()I
	public final fun libjscinstance_so ()I
	public final fun libjscruntime_so ()I
	public final fun libjsctooling_so ()I
	public final fun libjsijniprofiler_so ()I
	public final fun libjsinspector_so ()I
	public final fun libmapbufferjni_so ()I
	public final fun libreact_devsupportjni_so ()I
	public final fun libreact_featureflagsjni_so ()I
	public final fun libreact_newarchdefaults_so ()I
	public final fun libreactnative_so ()I
	public final fun libreactnativeblob_so ()I
	public final fun libreactnativejni_so ()I
	public final fun librninstance_so ()I
	public final fun libturbomodulejsijni_so ()I
	public final fun libuimanagerjni_so ()I
	public final fun libyoga_so ()I
	public fun mapLibName (Ljava/lang/String;)Ljava/lang/String;
}

public final class com/facebook/react/touch/JSResponderHandler : com/facebook/react/touch/OnInterceptTouchEventListener {
	public fun <init> ()V
	public final fun clearJSResponder ()V
	public fun onInterceptTouchEvent (Landroid/view/ViewGroup;Landroid/view/MotionEvent;)Z
	public final fun setJSResponder (ILandroid/view/ViewParent;)V
}

public abstract interface class com/facebook/react/touch/OnInterceptTouchEventListener {
	public abstract fun onInterceptTouchEvent (Landroid/view/ViewGroup;Landroid/view/MotionEvent;)Z
}

public abstract interface class com/facebook/react/touch/ReactHitSlopView {
	public abstract fun getHitSlopRect ()Landroid/graphics/Rect;
}

public abstract interface class com/facebook/react/touch/ReactInterceptingViewGroup {
	public abstract fun setOnInterceptTouchEventListener (Lcom/facebook/react/touch/OnInterceptTouchEventListener;)V
}

public final class com/facebook/react/turbomodule/core/CallInvokerHolderImpl : com/facebook/react/turbomodule/core/interfaces/CallInvokerHolder {
}

public final class com/facebook/react/turbomodule/core/NativeMethodCallInvokerHolderImpl : com/facebook/react/turbomodule/core/interfaces/NativeMethodCallInvokerHolder {
}

public final class com/facebook/react/turbomodule/core/interfaces/BindingsInstallerHolder {
	public fun <init> (Lcom/facebook/jni/HybridData;)V
}

public abstract interface class com/facebook/react/turbomodule/core/interfaces/CallInvokerHolder {
}

public abstract interface class com/facebook/react/turbomodule/core/interfaces/NativeMethodCallInvokerHolder {
}

public abstract interface class com/facebook/react/turbomodule/core/interfaces/TurboModule {
	public abstract fun initialize ()V
	public abstract fun invalidate ()V
}

public abstract interface class com/facebook/react/turbomodule/core/interfaces/TurboModuleWithJSIBindings {
	public abstract fun getBindingsInstaller ()Lcom/facebook/react/turbomodule/core/interfaces/BindingsInstallerHolder;
}

public final class com/facebook/react/uimanager/BackgroundStyleApplicator {
	public static final field INSTANCE Lcom/facebook/react/uimanager/BackgroundStyleApplicator;
	public static final fun clipToPaddingBox (Landroid/view/View;Landroid/graphics/Canvas;)V
	public static final fun getBackgroundColor (Landroid/view/View;)Ljava/lang/Integer;
	public static final fun getBorderColor (Landroid/view/View;Lcom/facebook/react/uimanager/style/LogicalEdge;)Ljava/lang/Integer;
	public static final fun getBorderRadius (Landroid/view/View;Lcom/facebook/react/uimanager/style/BorderRadiusProp;)Lcom/facebook/react/uimanager/LengthPercentage;
	public static final fun getBorderStyle (Landroid/view/View;)Lcom/facebook/react/uimanager/style/BorderStyle;
	public static final fun getBorderWidth (Landroid/view/View;Lcom/facebook/react/uimanager/style/LogicalEdge;)Ljava/lang/Float;
	public static final fun getOutlineColor (Landroid/view/View;)Ljava/lang/Integer;
	public final fun getOutlineOffset (Landroid/view/View;)Ljava/lang/Float;
	public final fun getOutlineStyle (Landroid/view/View;)Lcom/facebook/react/uimanager/style/OutlineStyle;
	public final fun getOutlineWidth (Landroid/view/View;)Ljava/lang/Float;
	public static final fun reset (Landroid/view/View;)V
	public static final fun setBackgroundColor (Landroid/view/View;Ljava/lang/Integer;)V
	public static final fun setBackgroundImage (Landroid/view/View;Ljava/util/List;)V
	public static final fun setBorderColor (Landroid/view/View;Lcom/facebook/react/uimanager/style/LogicalEdge;Ljava/lang/Integer;)V
	public static final fun setBorderRadius (Landroid/view/View;Lcom/facebook/react/uimanager/style/BorderRadiusProp;Lcom/facebook/react/uimanager/LengthPercentage;)V
	public static final fun setBorderStyle (Landroid/view/View;Lcom/facebook/react/uimanager/style/BorderStyle;)V
	public static final fun setBorderWidth (Landroid/view/View;Lcom/facebook/react/uimanager/style/LogicalEdge;Ljava/lang/Float;)V
	public static final fun setBoxShadow (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public static final fun setBoxShadow (Landroid/view/View;Ljava/util/List;)V
	public static final fun setFeedbackUnderlay (Landroid/view/View;Landroid/graphics/drawable/Drawable;)V
	public static final fun setOutlineColor (Landroid/view/View;Ljava/lang/Integer;)V
	public static final fun setOutlineOffset (Landroid/view/View;F)V
	public static final fun setOutlineStyle (Landroid/view/View;Lcom/facebook/react/uimanager/style/OutlineStyle;)V
	public static final fun setOutlineWidth (Landroid/view/View;F)V
}

public abstract class com/facebook/react/uimanager/BaseViewManager : com/facebook/react/uimanager/ViewManager, android/view/View$OnLayoutChangeListener {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun getExportedCustomBubblingEventTypeConstants ()Ljava/util/Map;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	protected fun onAfterUpdateTransaction (Landroid/view/View;)V
	public fun onLayoutChange (Landroid/view/View;IIIIIIII)V
	protected fun prepareToRecycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)Landroid/view/View;
	public fun setAccessibilityActions (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setAccessibilityCollection (Landroid/view/View;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setAccessibilityCollectionItem (Landroid/view/View;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setAccessibilityHint (Landroid/view/View;Ljava/lang/String;)V
	public fun setAccessibilityLabel (Landroid/view/View;Ljava/lang/String;)V
	public fun setAccessibilityLabelledBy (Landroid/view/View;Lcom/facebook/react/bridge/Dynamic;)V
	public fun setAccessibilityLiveRegion (Landroid/view/View;Ljava/lang/String;)V
	public fun setAccessibilityRole (Landroid/view/View;Ljava/lang/String;)V
	public fun setAccessibilityValue (Landroid/view/View;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setBackgroundColor (Landroid/view/View;I)V
	public fun setBorderBottomLeftRadius (Landroid/view/View;F)V
	public fun setBorderBottomRightRadius (Landroid/view/View;F)V
	public fun setBorderRadius (Landroid/view/View;F)V
	public fun setBorderTopLeftRadius (Landroid/view/View;F)V
	public fun setBorderTopRightRadius (Landroid/view/View;F)V
	public fun setBoxShadow (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setClick (Landroid/view/View;Z)V
	public fun setClickCapture (Landroid/view/View;Z)V
	public fun setElevation (Landroid/view/View;F)V
	public fun setFilter (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setImportantForAccessibility (Landroid/view/View;Ljava/lang/String;)V
	public fun setMixBlendMode (Landroid/view/View;Ljava/lang/String;)V
	public fun setMoveShouldSetResponder (Landroid/view/View;Z)V
	public fun setMoveShouldSetResponderCapture (Landroid/view/View;Z)V
	public fun setNativeId (Landroid/view/View;Ljava/lang/String;)V
	public fun setOpacity (Landroid/view/View;F)V
	public fun setOutlineColor (Landroid/view/View;Ljava/lang/Integer;)V
	public fun setOutlineOffset (Landroid/view/View;F)V
	public fun setOutlineStyle (Landroid/view/View;Ljava/lang/String;)V
	public fun setOutlineWidth (Landroid/view/View;F)V
	public fun setPointerEnter (Landroid/view/View;Z)V
	public fun setPointerEnterCapture (Landroid/view/View;Z)V
	public fun setPointerLeave (Landroid/view/View;Z)V
	public fun setPointerLeaveCapture (Landroid/view/View;Z)V
	public fun setPointerMove (Landroid/view/View;Z)V
	public fun setPointerMoveCapture (Landroid/view/View;Z)V
	public fun setPointerOut (Landroid/view/View;Z)V
	public fun setPointerOutCapture (Landroid/view/View;Z)V
	public fun setPointerOver (Landroid/view/View;Z)V
	public fun setPointerOverCapture (Landroid/view/View;Z)V
	public fun setRenderToHardwareTexture (Landroid/view/View;Z)V
	public fun setResponderEnd (Landroid/view/View;Z)V
	public fun setResponderGrant (Landroid/view/View;Z)V
	public fun setResponderMove (Landroid/view/View;Z)V
	public fun setResponderReject (Landroid/view/View;Z)V
	public fun setResponderRelease (Landroid/view/View;Z)V
	public fun setResponderStart (Landroid/view/View;Z)V
	public fun setResponderTerminate (Landroid/view/View;Z)V
	public fun setResponderTerminationRequest (Landroid/view/View;Z)V
	public fun setRole (Landroid/view/View;Ljava/lang/String;)V
	public fun setRotation (Landroid/view/View;F)V
	public fun setScaleX (Landroid/view/View;F)V
	public fun setScaleY (Landroid/view/View;F)V
	public fun setShadowColor (Landroid/view/View;I)V
	public fun setShouldBlockNativeResponder (Landroid/view/View;Z)V
	public fun setStartShouldSetResponder (Landroid/view/View;Z)V
	public fun setStartShouldSetResponderCapture (Landroid/view/View;Z)V
	public fun setTestId (Landroid/view/View;Ljava/lang/String;)V
	public fun setTouchCancel (Landroid/view/View;Z)V
	public fun setTouchEnd (Landroid/view/View;Z)V
	public fun setTouchMove (Landroid/view/View;Z)V
	public fun setTouchStart (Landroid/view/View;Z)V
	public fun setTransform (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setTransformOrigin (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	protected fun setTransformProperty (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setTranslateX (Landroid/view/View;F)V
	public fun setTranslateY (Landroid/view/View;F)V
	public fun setViewState (Landroid/view/View;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setZIndex (Landroid/view/View;F)V
	protected fun updateViewAccessibility (Landroid/view/View;)V
}

public abstract class com/facebook/react/uimanager/BaseViewManagerDelegate : com/facebook/react/uimanager/ViewManagerDelegate {
	protected final field mViewManager Lcom/facebook/react/uimanager/BaseViewManager;
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public synthetic fun kotlinCompat$receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun kotlinCompat$setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public final class com/facebook/react/uimanager/DisplayMetricsHolder {
	public static final field INSTANCE Lcom/facebook/react/uimanager/DisplayMetricsHolder;
	public static final fun getDisplayMetricsWritableMap (D)Lcom/facebook/react/bridge/WritableMap;
	public static final fun getScreenDisplayMetrics ()Landroid/util/DisplayMetrics;
	public static final fun getWindowDisplayMetrics ()Landroid/util/DisplayMetrics;
	public static final fun initDisplayMetrics (Landroid/content/Context;)V
	public static final fun initDisplayMetricsIfNotInitialized (Landroid/content/Context;)V
	public static final fun setScreenDisplayMetrics (Landroid/util/DisplayMetrics;)V
	public static final fun setWindowDisplayMetrics (Landroid/util/DisplayMetrics;)V
}

public final class com/facebook/react/uimanager/FloatUtil {
	public static final field INSTANCE Lcom/facebook/react/uimanager/FloatUtil;
	public static final fun floatsEqual (FF)Z
	public static final fun floatsEqual (Ljava/lang/Float;Ljava/lang/Float;)Z
}

public abstract class com/facebook/react/uimanager/GuardedFrameCallback : android/view/Choreographer$FrameCallback {
	protected fun <init> (Lcom/facebook/react/bridge/JSExceptionHandler;)V
	protected fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public fun doFrame (J)V
	protected abstract fun doFrameGuarded (J)V
}

public abstract interface class com/facebook/react/uimanager/IViewGroupManager : com/facebook/react/uimanager/IViewManagerWithChildren {
	public abstract fun addView (Landroid/view/View;Landroid/view/View;I)V
	public abstract fun getChildAt (Landroid/view/View;I)Landroid/view/View;
	public abstract fun getChildCount (Landroid/view/View;)I
	public fun removeAllViews (Landroid/view/View;)V
	public abstract fun removeViewAt (Landroid/view/View;I)V
}

public abstract interface class com/facebook/react/uimanager/IViewManagerWithChildren {
	public abstract fun needsCustomLayoutForChildren ()Z
}

public class com/facebook/react/uimanager/IllegalViewOperationException : com/facebook/react/bridge/JSApplicationCausedNativeException {
	public fun <init> (Ljava/lang/String;)V
	public fun <init> (Ljava/lang/String;Landroid/view/View;Ljava/lang/Throwable;)V
	public final fun getView ()Landroid/view/View;
}

public class com/facebook/react/uimanager/JSPointerDispatcher {
	public fun <init> (Landroid/view/ViewGroup;)V
	public fun handleMotionEvent (Landroid/view/MotionEvent;Lcom/facebook/react/uimanager/events/EventDispatcher;Z)V
	public fun onChildEndedNativeGesture ()V
	public fun onChildStartedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;Lcom/facebook/react/uimanager/events/EventDispatcher;)V
}

public class com/facebook/react/uimanager/JSTouchDispatcher {
	public fun <init> (Landroid/view/ViewGroup;)V
	public fun handleTouchEvent (Landroid/view/MotionEvent;Lcom/facebook/react/uimanager/events/EventDispatcher;)V
	public fun handleTouchEvent (Landroid/view/MotionEvent;Lcom/facebook/react/uimanager/events/EventDispatcher;Lcom/facebook/react/bridge/ReactContext;)V
	public fun onChildEndedNativeGesture (Landroid/view/MotionEvent;Lcom/facebook/react/uimanager/events/EventDispatcher;)V
	public fun onChildStartedNativeGesture (Landroid/view/MotionEvent;Lcom/facebook/react/uimanager/events/EventDispatcher;)V
}

public class com/facebook/react/uimanager/LayoutShadowNode : com/facebook/react/uimanager/ReactShadowNodeImpl {
	public fun <init> ()V
	public fun setAlignContent (Ljava/lang/String;)V
	public fun setAlignItems (Ljava/lang/String;)V
	public fun setAlignSelf (Ljava/lang/String;)V
	public fun setAspectRatio (F)V
	public fun setBorderWidths (IF)V
	public fun setCollapsable (Z)V
	public fun setCollapsableChildren (Z)V
	public fun setColumnGap (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setDisplay (Ljava/lang/String;)V
	public fun setFlex (F)V
	public fun setFlexBasis (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setFlexDirection (Ljava/lang/String;)V
	public fun setFlexGrow (F)V
	public fun setFlexShrink (F)V
	public fun setFlexWrap (Ljava/lang/String;)V
	public fun setGap (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setHeight (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setInset (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setInsetBlock (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setInsetInline (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setJustifyContent (Ljava/lang/String;)V
	public fun setMarginBlock (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setMarginInline (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setMargins (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setMaxHeight (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setMaxWidth (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setMinHeight (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setMinWidth (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setOverflow (Ljava/lang/String;)V
	public fun setPaddingBlock (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setPaddingInline (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setPaddings (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setPosition (Ljava/lang/String;)V
	public fun setPositionValues (ILcom/facebook/react/bridge/Dynamic;)V
	public fun setRowGap (Lcom/facebook/react/bridge/Dynamic;)V
	public fun setShouldNotifyOnLayout (Z)V
	public fun setShouldNotifyPointerEnter (Z)V
	public fun setShouldNotifyPointerLeave (Z)V
	public fun setShouldNotifyPointerMove (Z)V
	public fun setWidth (Lcom/facebook/react/bridge/Dynamic;)V
}

public final class com/facebook/react/uimanager/LengthPercentage {
	public static final field Companion Lcom/facebook/react/uimanager/LengthPercentage$Companion;
	public fun <init> ()V
	public fun <init> (FLcom/facebook/react/uimanager/LengthPercentageType;)V
	public final fun component2 ()Lcom/facebook/react/uimanager/LengthPercentageType;
	public final fun copy (FLcom/facebook/react/uimanager/LengthPercentageType;)Lcom/facebook/react/uimanager/LengthPercentage;
	public static synthetic fun copy$default (Lcom/facebook/react/uimanager/LengthPercentage;FLcom/facebook/react/uimanager/LengthPercentageType;ILjava/lang/Object;)Lcom/facebook/react/uimanager/LengthPercentage;
	public fun equals (Ljava/lang/Object;)Z
	public final fun getType ()Lcom/facebook/react/uimanager/LengthPercentageType;
	public fun hashCode ()I
	public final fun resolve (F)F
	public final fun resolve (FF)Lcom/facebook/react/uimanager/style/CornerRadii;
	public static final fun setFromDynamic (Lcom/facebook/react/bridge/Dynamic;)Lcom/facebook/react/uimanager/LengthPercentage;
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/uimanager/LengthPercentage$Companion {
	public final fun setFromDynamic (Lcom/facebook/react/bridge/Dynamic;)Lcom/facebook/react/uimanager/LengthPercentage;
}

public final class com/facebook/react/uimanager/LengthPercentageType : java/lang/Enum {
	public static final field PERCENT Lcom/facebook/react/uimanager/LengthPercentageType;
	public static final field POINT Lcom/facebook/react/uimanager/LengthPercentageType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/LengthPercentageType;
	public static fun values ()[Lcom/facebook/react/uimanager/LengthPercentageType;
}

public final class com/facebook/react/uimanager/MatrixMathHelper {
	public static final field INSTANCE Lcom/facebook/react/uimanager/MatrixMathHelper;
	public static final fun applyPerspective ([DD)V
	public static final fun applyRotateX ([DD)V
	public static final fun applyRotateY ([DD)V
	public static final fun applyRotateZ ([DD)V
	public static final fun applyScaleX ([DD)V
	public static final fun applyScaleY ([DD)V
	public final fun applyScaleZ ([DD)V
	public static final fun applySkewX ([DD)V
	public static final fun applySkewY ([DD)V
	public static final fun applyTranslate2D ([DDD)V
	public static final fun applyTranslate3D ([DDDD)V
	public static final fun createIdentityMatrix ()[D
	public static final fun decomposeMatrix ([DLcom/facebook/react/uimanager/MatrixMathHelper$MatrixDecompositionContext;)V
	public static final fun degreesToRadians (D)D
	public static final fun determinant ([D)D
	public static final fun inverse ([D)[D
	public static final fun multiplyInto ([D[D[D)V
	public static final fun multiplyVectorByMatrix ([D[D[D)V
	public static final fun resetIdentityMatrix ([D)V
	public static final fun roundTo3Places (D)D
	public static final fun transpose ([D)[D
	public static final fun v3Combine ([D[DDD)[D
	public static final fun v3Cross ([D[D)[D
	public static final fun v3Dot ([D[D)D
	public static final fun v3Length ([D)D
	public static final fun v3Normalize ([DD)[D
}

public class com/facebook/react/uimanager/MatrixMathHelper$MatrixDecompositionContext {
	public field perspective [D
	public field rotationDegrees [D
	public field scale [D
	public field skew [D
	public field translation [D
	public fun <init> ()V
	public final fun reset ()V
}

public final class com/facebook/react/uimanager/MeasureSpecAssertions {
	public static final field INSTANCE Lcom/facebook/react/uimanager/MeasureSpecAssertions;
	public static final fun assertExplicitMeasureSpec (II)V
}

public class com/facebook/react/uimanager/NativeViewHierarchyManager {
	public fun <init> (Lcom/facebook/react/uimanager/ViewManagerRegistry;)V
	public fun <init> (Lcom/facebook/react/uimanager/ViewManagerRegistry;Lcom/facebook/react/uimanager/RootViewManager;)V
	public fun addRootView (ILandroid/view/View;)V
	protected final fun addRootViewGroup (ILandroid/view/View;)V
	public fun clearJSResponder ()V
	public fun createView (Lcom/facebook/react/uimanager/ThemedReactContext;ILjava/lang/String;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun dispatchCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	protected fun dropView (Landroid/view/View;)V
	public fun findTargetTagForTouch (IFF)I
	public fun getInstanceHandle (I)J
	public fun getRootViewNum ()I
	public fun manageChildren (I[I[Lcom/facebook/react/uimanager/ViewAtIndex;[I)V
	public fun measure (I[I)V
	public fun measureInWindow (I[I)V
	public fun removeRootView (I)V
	public final fun resolveView (I)Landroid/view/View;
	public final fun resolveViewManager (I)Lcom/facebook/react/uimanager/ViewManager;
	public fun sendAccessibilityEvent (II)V
	public fun setChildren (ILcom/facebook/react/bridge/ReadableArray;)V
	public fun setJSResponder (IIZ)V
	public fun setLayoutAnimationEnabled (Z)V
	public fun updateInstanceHandle (IJ)V
	public fun updateLayout (IIIII)V
	public fun updateLayout (IIIIIILcom/facebook/yoga/YogaDirection;)V
	public fun updateProperties (ILcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun updateViewExtraData (ILjava/lang/Object;)V
}

public class com/facebook/react/uimanager/NativeViewHierarchyOptimizer {
	public fun <init> (Lcom/facebook/react/uimanager/UIViewOperationQueue;Lcom/facebook/react/uimanager/ShadowNodeRegistry;)V
	public static fun assertNodeSupportedWithoutOptimizer (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public fun handleCreateView (Lcom/facebook/react/uimanager/ReactShadowNode;Lcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun handleForceViewToBeNonLayoutOnly (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public fun handleManageChildren (Lcom/facebook/react/uimanager/ReactShadowNode;[I[I[Lcom/facebook/react/uimanager/ViewAtIndex;[I)V
	public static fun handleRemoveNode (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public fun handleSetChildren (Lcom/facebook/react/uimanager/ReactShadowNode;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun handleUpdateLayout (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public fun handleUpdateView (Lcom/facebook/react/uimanager/ReactShadowNode;Ljava/lang/String;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun onBatchComplete ()V
}

public class com/facebook/react/uimanager/OnLayoutEvent : com/facebook/react/uimanager/events/Event {
	protected fun getEventData ()Lcom/facebook/react/bridge/WritableMap;
	public fun getEventName ()Ljava/lang/String;
	protected fun init (IIIII)V
	protected fun init (IIIIII)V
	public static fun obtain (IIIII)Lcom/facebook/react/uimanager/OnLayoutEvent;
	public static fun obtain (IIIIII)Lcom/facebook/react/uimanager/OnLayoutEvent;
	public fun onDispose ()V
}

public final class com/facebook/react/uimanager/PixelUtil {
	public static final field INSTANCE Lcom/facebook/react/uimanager/PixelUtil;
	public final fun dpToPx (D)F
	public final fun dpToPx (F)F
	public final fun dpToPx (I)F
	public final fun dpToPx (J)F
	public static final fun getDisplayMetricDensity ()F
	public final fun pxToDp (D)F
	public final fun pxToDp (F)F
	public final fun pxToDp (I)F
	public final fun pxToDp (J)F
	public static final fun toDIPFromPixel (F)F
	public static final fun toPixelFromDIP (D)F
	public static final fun toPixelFromDIP (F)F
	public static final fun toPixelFromSP (D)F
	public static final fun toPixelFromSP (F)F
	public static final fun toPixelFromSP (FF)F
	public static synthetic fun toPixelFromSP$default (FFILjava/lang/Object;)F
}

public final class com/facebook/react/uimanager/PointerEvents : java/lang/Enum {
	public static final field AUTO Lcom/facebook/react/uimanager/PointerEvents;
	public static final field BOX_NONE Lcom/facebook/react/uimanager/PointerEvents;
	public static final field BOX_ONLY Lcom/facebook/react/uimanager/PointerEvents;
	public static final field Companion Lcom/facebook/react/uimanager/PointerEvents$Companion;
	public static final field NONE Lcom/facebook/react/uimanager/PointerEvents;
	public static final fun canBeTouchTarget (Lcom/facebook/react/uimanager/PointerEvents;)Z
	public static final fun canChildrenBeTouchTarget (Lcom/facebook/react/uimanager/PointerEvents;)Z
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static final fun parsePointerEvents (Ljava/lang/String;)Lcom/facebook/react/uimanager/PointerEvents;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/PointerEvents;
	public static fun values ()[Lcom/facebook/react/uimanager/PointerEvents;
}

public final class com/facebook/react/uimanager/PointerEvents$Companion {
	public final fun canBeTouchTarget (Lcom/facebook/react/uimanager/PointerEvents;)Z
	public final fun canChildrenBeTouchTarget (Lcom/facebook/react/uimanager/PointerEvents;)Z
	public final fun parsePointerEvents (Ljava/lang/String;)Lcom/facebook/react/uimanager/PointerEvents;
}

public class com/facebook/react/uimanager/ReactAccessibilityDelegate : androidx/customview/widget/ExploreByTouchHelper {
	public static final field TOP_ACCESSIBILITY_ACTION_EVENT Ljava/lang/String;
	public static final field sActionIdMap Ljava/util/HashMap;
	public fun <init> (Landroid/view/View;ZI)V
	public static fun createNodeInfoFromView (Landroid/view/View;)Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;
	public fun getAccessibilityNodeProvider (Landroid/view/View;)Landroidx/core/view/accessibility/AccessibilityNodeProviderCompat;
	protected fun getHostView ()Landroid/view/View;
	public static fun getTalkbackDescription (Landroid/view/View;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)Ljava/lang/CharSequence;
	protected fun getVirtualViewAt (FF)I
	protected fun getVisibleVirtualViews (Ljava/util/List;)V
	public static fun hasNonActionableSpeakingDescendants (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;Landroid/view/View;)Z
	public static fun hasText (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)Z
	public static fun hasValidRangeInfo (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)Z
	public static fun isAccessibilityFocusable (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;Landroid/view/View;)Z
	public static fun isActionableForAccessibility (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)Z
	public static fun isSpeakingNode (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;Landroid/view/View;)Z
	public fun onInitializeAccessibilityEvent (Landroid/view/View;Landroid/view/accessibility/AccessibilityEvent;)V
	public fun onInitializeAccessibilityNodeInfo (Landroid/view/View;Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
	protected fun onPerformActionForVirtualView (IILandroid/os/Bundle;)Z
	protected fun onPopulateNodeForVirtualView (ILandroidx/core/view/accessibility/AccessibilityNodeInfoCompat;)V
	public fun performAccessibilityAction (Landroid/view/View;ILandroid/os/Bundle;)Z
	public static fun resetDelegate (Landroid/view/View;ZI)V
	public static fun setDelegate (Landroid/view/View;ZI)V
	public static fun setRole (Landroidx/core/view/accessibility/AccessibilityNodeInfoCompat;Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;Landroid/content/Context;)V
	public fun superGetAccessibilityNodeProvider (Landroid/view/View;)Landroidx/core/view/accessibility/AccessibilityNodeProviderCompat;
}

public final class com/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole : java/lang/Enum {
	public static final field ADJUSTABLE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field ALERT Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field BUTTON Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field CHECKBOX Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field COMBOBOX Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field DRAWERLAYOUT Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field DROPDOWNLIST Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field GRID Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field HEADER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field HORIZONTALSCROLLVIEW Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field ICONMENU Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field IMAGE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field IMAGEBUTTON Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field KEYBOARDKEY Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field LINK Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field LIST Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field MENU Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field MENUBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field MENUITEM Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field NONE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field PAGER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field PROGRESSBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field RADIO Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field RADIOGROUP Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SCROLLBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SCROLLVIEW Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SEARCH Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SLIDINGDRAWER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SPINBUTTON Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SUMMARY Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field SWITCH Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field TAB Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field TABLIST Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field TEXT Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field TIMER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field TOGGLEBUTTON Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field TOOLBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field VIEWGROUP Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static final field WEBVIEW Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static fun fromRole (Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;)Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static fun fromValue (Ljava/lang/String;)Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static fun fromViewTag (Landroid/view/View;)Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static fun getValue (Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;)Ljava/lang/String;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public static fun values ()[Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
}

public final class com/facebook/react/uimanager/ReactAccessibilityDelegate$Role : java/lang/Enum {
	public static final field ALERT Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field ALERTDIALOG Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field APPLICATION Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field ARTICLE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field BANNER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field BUTTON Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field CELL Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field CHECKBOX Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field COLUMNHEADER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field COMBOBOX Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field COMPLEMENTARY Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field CONTENTINFO Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field DEFINITION Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field DIALOG Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field DIRECTORY Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field DOCUMENT Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field FEED Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field FIGURE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field FORM Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field GRID Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field GROUP Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field HEADING Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field IMG Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field LINK Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field LIST Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field LISTITEM Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field LOG Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field MAIN Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field MARQUEE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field MATH Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field MENU Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field MENUBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field MENUITEM Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field METER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field NAVIGATION Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field NONE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field NOTE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field OPTION Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field PRESENTATION Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field PROGRESSBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field RADIO Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field RADIOGROUP Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field REGION Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field ROW Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field ROWGROUP Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field ROWHEADER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SCROLLBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SEARCHBOX Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SEPARATOR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SLIDER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SPINBUTTON Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field STATUS Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SUMMARY Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field SWITCH Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TAB Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TABLE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TABLIST Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TABPANEL Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TERM Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TIMER Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TOOLBAR Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TOOLTIP Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TREE Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TREEGRID Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static final field TREEITEM Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static fun fromValue (Ljava/lang/String;)Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static fun values ()[Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
}

public abstract interface class com/facebook/react/uimanager/ReactClippingProhibitedView {
}

public abstract interface class com/facebook/react/uimanager/ReactClippingViewGroup {
	public abstract fun getClippingRect (Landroid/graphics/Rect;)V
	public abstract fun getRemoveClippedSubviews ()Z
	public abstract fun setRemoveClippedSubviews (Z)V
	public abstract fun updateClippingRect ()V
}

public class com/facebook/react/uimanager/ReactClippingViewGroupHelper {
	public static final field PROP_REMOVE_CLIPPED_SUBVIEWS Ljava/lang/String;
	public fun <init> ()V
	public static fun calculateClippingRect (Landroid/view/View;Landroid/graphics/Rect;)V
}

public abstract interface class com/facebook/react/uimanager/ReactCompoundView {
	public abstract fun reactTagForTouch (FF)I
}

public abstract interface class com/facebook/react/uimanager/ReactCompoundViewGroup : com/facebook/react/uimanager/ReactCompoundView {
	public abstract fun interceptsTouchEvent (FF)Z
}

public final class com/facebook/react/uimanager/ReactInvalidPropertyException : java/lang/RuntimeException {
	public fun <init> (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/uimanager/ReactOverflowView {
	public abstract fun getOverflow ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/uimanager/ReactPointerEventsView {
	public abstract fun getPointerEvents ()Lcom/facebook/react/uimanager/PointerEvents;
}

public abstract interface class com/facebook/react/uimanager/ReactRoot {
	public static final field STATE_STARTED I
	public static final field STATE_STOPPED I
	public abstract fun getAppProperties ()Landroid/os/Bundle;
	public abstract fun getHeightMeasureSpec ()I
	public abstract fun getJSModuleName ()Ljava/lang/String;
	public abstract fun getRootViewGroup ()Landroid/view/ViewGroup;
	public abstract fun getRootViewTag ()I
	public abstract fun getState ()Ljava/util/concurrent/atomic/AtomicInteger;
	public abstract fun getSurfaceID ()Ljava/lang/String;
	public abstract fun getUIManagerType ()I
	public abstract fun getWidthMeasureSpec ()I
	public abstract fun onStage (I)V
	public abstract fun runApplication ()V
	public abstract fun setRootViewTag (I)V
	public abstract fun setShouldLogContentAppeared (Z)V
}

public final class com/facebook/react/uimanager/ReactRootViewTagGenerator {
	public static final field INSTANCE Lcom/facebook/react/uimanager/ReactRootViewTagGenerator;
	public static final fun getNextRootViewTag ()I
}

public abstract interface class com/facebook/react/uimanager/ReactShadowNode {
	public abstract fun addChildAt (Lcom/facebook/react/uimanager/ReactShadowNode;I)V
	public abstract fun addNativeChildAt (Lcom/facebook/react/uimanager/ReactShadowNode;I)V
	public abstract fun calculateLayout ()V
	public abstract fun calculateLayout (FF)V
	public abstract fun calculateLayoutOnChildren ()Ljava/lang/Iterable;
	public abstract fun dirty ()V
	public abstract fun dispatchUpdates (FFLcom/facebook/react/uimanager/UIViewOperationQueue;Lcom/facebook/react/uimanager/NativeViewHierarchyOptimizer;)V
	public abstract fun dispatchUpdatesWillChangeLayout (FF)Z
	public abstract fun dispose ()V
	public abstract fun getChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	public abstract fun getChildCount ()I
	public abstract fun getFlex ()F
	public abstract fun getHeightMeasureSpec ()Ljava/lang/Integer;
	public abstract fun getHierarchyInfo ()Ljava/lang/String;
	public abstract fun getLayoutDirection ()Lcom/facebook/yoga/YogaDirection;
	public abstract fun getLayoutHeight ()F
	public abstract fun getLayoutParent ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public abstract fun getLayoutWidth ()F
	public abstract fun getLayoutX ()F
	public abstract fun getLayoutY ()F
	public abstract fun getNativeChildCount ()I
	public abstract fun getNativeKind ()Lcom/facebook/react/uimanager/NativeKind;
	public abstract fun getNativeOffsetForChild (Lcom/facebook/react/uimanager/ReactShadowNode;)I
	public abstract fun getNativeParent ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public abstract fun getPadding (I)F
	public abstract fun getParent ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public abstract fun getReactTag ()I
	public abstract fun getRootTag ()I
	public abstract fun getScreenHeight ()I
	public abstract fun getScreenWidth ()I
	public abstract fun getScreenX ()I
	public abstract fun getScreenY ()I
	public abstract fun getStyleHeight ()Lcom/facebook/yoga/YogaValue;
	public abstract fun getStylePadding (I)Lcom/facebook/yoga/YogaValue;
	public abstract fun getStyleWidth ()Lcom/facebook/yoga/YogaValue;
	public abstract fun getThemedContext ()Lcom/facebook/react/uimanager/ThemedReactContext;
	public abstract fun getTotalNativeChildren ()I
	public abstract fun getViewClass ()Ljava/lang/String;
	public abstract fun getWidthMeasureSpec ()Ljava/lang/Integer;
	public abstract fun hasNewLayout ()Z
	public abstract fun hasUnseenUpdates ()Z
	public abstract fun hasUpdates ()Z
	public abstract fun hoistNativeChildren ()Z
	public abstract fun indexOf (Lcom/facebook/react/uimanager/ReactShadowNode;)I
	public abstract fun indexOfNativeChild (Lcom/facebook/react/uimanager/ReactShadowNode;)I
	public abstract fun isDescendantOf (Lcom/facebook/react/uimanager/ReactShadowNode;)Z
	public abstract fun isDirty ()Z
	public abstract fun isLayoutOnly ()Z
	public abstract fun isMeasureDefined ()Z
	public abstract fun isVirtual ()Z
	public abstract fun isVirtualAnchor ()Z
	public abstract fun isYogaLeafNode ()Z
	public abstract fun markLayoutSeen ()V
	public abstract fun markUpdateSeen ()V
	public abstract fun markUpdated ()V
	public abstract fun onAfterUpdateTransaction ()V
	public abstract fun onBeforeLayout (Lcom/facebook/react/uimanager/NativeViewHierarchyOptimizer;)V
	public abstract fun onCollectExtraUpdates (Lcom/facebook/react/uimanager/UIViewOperationQueue;)V
	public abstract fun removeAllNativeChildren ()V
	public abstract fun removeAndDisposeAllChildren ()V
	public abstract fun removeChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	public abstract fun removeNativeChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	public abstract fun setAlignContent (Lcom/facebook/yoga/YogaAlign;)V
	public abstract fun setAlignItems (Lcom/facebook/yoga/YogaAlign;)V
	public abstract fun setAlignSelf (Lcom/facebook/yoga/YogaAlign;)V
	public abstract fun setBaselineFunction (Lcom/facebook/yoga/YogaBaselineFunction;)V
	public abstract fun setBorder (IF)V
	public abstract fun setColumnGap (F)V
	public abstract fun setColumnGapPercent (F)V
	public abstract fun setDefaultPadding (IF)V
	public abstract fun setDisplay (Lcom/facebook/yoga/YogaDisplay;)V
	public abstract fun setFlex (F)V
	public abstract fun setFlexBasis (F)V
	public abstract fun setFlexBasisAuto ()V
	public abstract fun setFlexBasisPercent (F)V
	public abstract fun setFlexDirection (Lcom/facebook/yoga/YogaFlexDirection;)V
	public abstract fun setFlexGrow (F)V
	public abstract fun setFlexShrink (F)V
	public abstract fun setFlexWrap (Lcom/facebook/yoga/YogaWrap;)V
	public abstract fun setGap (F)V
	public abstract fun setGapPercent (F)V
	public abstract fun setIsLayoutOnly (Z)V
	public abstract fun setJustifyContent (Lcom/facebook/yoga/YogaJustify;)V
	public abstract fun setLayoutDirection (Lcom/facebook/yoga/YogaDirection;)V
	public abstract fun setLayoutParent (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public abstract fun setLocalData (Ljava/lang/Object;)V
	public abstract fun setMargin (IF)V
	public abstract fun setMarginAuto (I)V
	public abstract fun setMarginPercent (IF)V
	public abstract fun setMeasureFunction (Lcom/facebook/yoga/YogaMeasureFunction;)V
	public abstract fun setMeasureSpecs (II)V
	public abstract fun setOverflow (Lcom/facebook/yoga/YogaOverflow;)V
	public abstract fun setPadding (IF)V
	public abstract fun setPaddingPercent (IF)V
	public abstract fun setPosition (IF)V
	public abstract fun setPositionPercent (IF)V
	public abstract fun setPositionType (Lcom/facebook/yoga/YogaPositionType;)V
	public abstract fun setReactTag (I)V
	public abstract fun setRootTag (I)V
	public abstract fun setRowGap (F)V
	public abstract fun setRowGapPercent (F)V
	public abstract fun setShouldNotifyOnLayout (Z)V
	public abstract fun setStyleAspectRatio (F)V
	public abstract fun setStyleHeight (F)V
	public abstract fun setStyleHeightAuto ()V
	public abstract fun setStyleHeightPercent (F)V
	public abstract fun setStyleMaxHeight (F)V
	public abstract fun setStyleMaxHeightPercent (F)V
	public abstract fun setStyleMaxWidth (F)V
	public abstract fun setStyleMaxWidthPercent (F)V
	public abstract fun setStyleMinHeight (F)V
	public abstract fun setStyleMinHeightPercent (F)V
	public abstract fun setStyleMinWidth (F)V
	public abstract fun setStyleMinWidthPercent (F)V
	public abstract fun setStyleWidth (F)V
	public abstract fun setStyleWidthAuto ()V
	public abstract fun setStyleWidthPercent (F)V
	public abstract fun setThemedContext (Lcom/facebook/react/uimanager/ThemedReactContext;)V
	public abstract fun setViewClassName (Ljava/lang/String;)V
	public abstract fun shouldNotifyOnLayout ()Z
	public abstract fun updateProperties (Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
}

public class com/facebook/react/uimanager/ReactShadowNodeImpl : com/facebook/react/uimanager/ReactShadowNode {
	public fun <init> ()V
	public synthetic fun addChildAt (Lcom/facebook/react/uimanager/ReactShadowNode;I)V
	public fun addChildAt (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;I)V
	public synthetic fun addNativeChildAt (Lcom/facebook/react/uimanager/ReactShadowNode;I)V
	public final fun addNativeChildAt (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;I)V
	public fun calculateLayout ()V
	public fun calculateLayout (FF)V
	public fun calculateLayoutOnChildren ()Ljava/lang/Iterable;
	public fun dirty ()V
	public fun dispatchUpdates (FFLcom/facebook/react/uimanager/UIViewOperationQueue;Lcom/facebook/react/uimanager/NativeViewHierarchyOptimizer;)V
	public fun dispatchUpdatesWillChangeLayout (FF)Z
	public fun dispose ()V
	public synthetic fun getChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	public final fun getChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNodeImpl;
	public final fun getChildCount ()I
	public fun getFlex ()F
	public fun getHeightMeasureSpec ()Ljava/lang/Integer;
	public fun getHierarchyInfo ()Ljava/lang/String;
	public final fun getLayoutDirection ()Lcom/facebook/yoga/YogaDirection;
	public final fun getLayoutHeight ()F
	public synthetic fun getLayoutParent ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public final fun getLayoutParent ()Lcom/facebook/react/uimanager/ReactShadowNodeImpl;
	public final fun getLayoutWidth ()F
	public final fun getLayoutX ()F
	public final fun getLayoutY ()F
	public final fun getNativeChildCount ()I
	public fun getNativeKind ()Lcom/facebook/react/uimanager/NativeKind;
	public synthetic fun getNativeOffsetForChild (Lcom/facebook/react/uimanager/ReactShadowNode;)I
	public final fun getNativeOffsetForChild (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;)I
	public synthetic fun getNativeParent ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public final fun getNativeParent ()Lcom/facebook/react/uimanager/ReactShadowNodeImpl;
	public final fun getPadding (I)F
	public synthetic fun getParent ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public final fun getParent ()Lcom/facebook/react/uimanager/ReactShadowNodeImpl;
	public final fun getReactTag ()I
	public final fun getRootTag ()I
	public fun getScreenHeight ()I
	public fun getScreenWidth ()I
	public fun getScreenX ()I
	public fun getScreenY ()I
	public final fun getStyleHeight ()Lcom/facebook/yoga/YogaValue;
	public final fun getStylePadding (I)Lcom/facebook/yoga/YogaValue;
	public final fun getStyleWidth ()Lcom/facebook/yoga/YogaValue;
	public final fun getThemedContext ()Lcom/facebook/react/uimanager/ThemedReactContext;
	public final fun getTotalNativeChildren ()I
	public final fun getViewClass ()Ljava/lang/String;
	public fun getWidthMeasureSpec ()Ljava/lang/Integer;
	public final fun hasNewLayout ()Z
	public final fun hasUnseenUpdates ()Z
	public final fun hasUpdates ()Z
	public fun hoistNativeChildren ()Z
	public synthetic fun indexOf (Lcom/facebook/react/uimanager/ReactShadowNode;)I
	public final fun indexOf (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;)I
	public synthetic fun indexOfNativeChild (Lcom/facebook/react/uimanager/ReactShadowNode;)I
	public final fun indexOfNativeChild (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;)I
	public synthetic fun isDescendantOf (Lcom/facebook/react/uimanager/ReactShadowNode;)Z
	public fun isDescendantOf (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;)Z
	public final fun isDirty ()Z
	public final fun isLayoutOnly ()Z
	public fun isMeasureDefined ()Z
	public fun isVirtual ()Z
	public fun isVirtualAnchor ()Z
	public fun isYogaLeafNode ()Z
	public final fun markLayoutSeen ()V
	public final fun markUpdateSeen ()V
	public fun markUpdated ()V
	public fun onAfterUpdateTransaction ()V
	public fun onBeforeLayout (Lcom/facebook/react/uimanager/NativeViewHierarchyOptimizer;)V
	public fun onCollectExtraUpdates (Lcom/facebook/react/uimanager/UIViewOperationQueue;)V
	public final fun removeAllNativeChildren ()V
	public fun removeAndDisposeAllChildren ()V
	public synthetic fun removeChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun removeChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNodeImpl;
	public synthetic fun removeNativeChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	public final fun removeNativeChildAt (I)Lcom/facebook/react/uimanager/ReactShadowNodeImpl;
	public fun setAlignContent (Lcom/facebook/yoga/YogaAlign;)V
	public fun setAlignItems (Lcom/facebook/yoga/YogaAlign;)V
	public fun setAlignSelf (Lcom/facebook/yoga/YogaAlign;)V
	public fun setBaselineFunction (Lcom/facebook/yoga/YogaBaselineFunction;)V
	public fun setBorder (IF)V
	public fun setColumnGap (F)V
	public fun setColumnGapPercent (F)V
	public fun setDefaultPadding (IF)V
	public fun setDisplay (Lcom/facebook/yoga/YogaDisplay;)V
	public fun setFlex (F)V
	public fun setFlexBasis (F)V
	public fun setFlexBasisAuto ()V
	public fun setFlexBasisPercent (F)V
	public fun setFlexDirection (Lcom/facebook/yoga/YogaFlexDirection;)V
	public fun setFlexGrow (F)V
	public fun setFlexShrink (F)V
	public fun setFlexWrap (Lcom/facebook/yoga/YogaWrap;)V
	public fun setGap (F)V
	public fun setGapPercent (F)V
	public final fun setIsLayoutOnly (Z)V
	public fun setJustifyContent (Lcom/facebook/yoga/YogaJustify;)V
	public fun setLayoutDirection (Lcom/facebook/yoga/YogaDirection;)V
	public synthetic fun setLayoutParent (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public final fun setLayoutParent (Lcom/facebook/react/uimanager/ReactShadowNodeImpl;)V
	public fun setLocalData (Ljava/lang/Object;)V
	public fun setMargin (IF)V
	public fun setMarginAuto (I)V
	public fun setMarginPercent (IF)V
	public fun setMeasureFunction (Lcom/facebook/yoga/YogaMeasureFunction;)V
	public fun setMeasureSpecs (II)V
	public fun setOverflow (Lcom/facebook/yoga/YogaOverflow;)V
	public fun setPadding (IF)V
	public fun setPaddingPercent (IF)V
	public fun setPosition (IF)V
	public fun setPositionPercent (IF)V
	public fun setPositionType (Lcom/facebook/yoga/YogaPositionType;)V
	public fun setReactTag (I)V
	public final fun setRootTag (I)V
	public fun setRowGap (F)V
	public fun setRowGapPercent (F)V
	public fun setShouldNotifyOnLayout (Z)V
	public fun setStyleAspectRatio (F)V
	public fun setStyleHeight (F)V
	public fun setStyleHeightAuto ()V
	public fun setStyleHeightPercent (F)V
	public fun setStyleMaxHeight (F)V
	public fun setStyleMaxHeightPercent (F)V
	public fun setStyleMaxWidth (F)V
	public fun setStyleMaxWidthPercent (F)V
	public fun setStyleMinHeight (F)V
	public fun setStyleMinHeightPercent (F)V
	public fun setStyleMinWidth (F)V
	public fun setStyleMinWidthPercent (F)V
	public fun setStyleWidth (F)V
	public fun setStyleWidthAuto ()V
	public fun setStyleWidthPercent (F)V
	public fun setThemedContext (Lcom/facebook/react/uimanager/ThemedReactContext;)V
	public final fun setViewClassName (Ljava/lang/String;)V
	public final fun shouldNotifyOnLayout ()Z
	public fun toString ()Ljava/lang/String;
	public final fun updateProperties (Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
}

public abstract interface annotation class com/facebook/react/uimanager/ReactStage : java/lang/annotation/Annotation {
	public static final field BRIDGE_DID_LOAD I
	public static final field Companion Lcom/facebook/react/uimanager/ReactStage$Companion;
	public static final field MODULE_DID_LOAD I
	public static final field ON_ATTACH_TO_INSTANCE I
	public static final field SURFACE_DID_INITIALIZE I
	public static final field SURFACE_DID_INITIAL_LAYOUT I
	public static final field SURFACE_DID_INITIAL_MOUNTING I
	public static final field SURFACE_DID_INITIAL_RENDERING I
	public static final field SURFACE_DID_RUN I
	public static final field SURFACE_DID_STOP I
}

public final class com/facebook/react/uimanager/ReactStage$Companion {
	public static final field BRIDGE_DID_LOAD I
	public static final field MODULE_DID_LOAD I
	public static final field ON_ATTACH_TO_INSTANCE I
	public static final field SURFACE_DID_INITIALIZE I
	public static final field SURFACE_DID_INITIAL_LAYOUT I
	public static final field SURFACE_DID_INITIAL_MOUNTING I
	public static final field SURFACE_DID_INITIAL_RENDERING I
	public static final field SURFACE_DID_RUN I
	public static final field SURFACE_DID_STOP I
}

public class com/facebook/react/uimanager/ReactStylesDiffMap {
	public fun <init> (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun getArray (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableArray;
	public fun getBoolean (Ljava/lang/String;Z)Z
	public fun getDouble (Ljava/lang/String;D)D
	public fun getDynamic (Ljava/lang/String;)Lcom/facebook/react/bridge/Dynamic;
	public fun getFloat (Ljava/lang/String;F)F
	public fun getInt (Ljava/lang/String;I)I
	public fun getMap (Ljava/lang/String;)Lcom/facebook/react/bridge/ReadableMap;
	public fun getString (Ljava/lang/String;)Ljava/lang/String;
	public fun hasKey (Ljava/lang/String;)Z
	public fun isNull (Ljava/lang/String;)Z
	public fun toMap ()Ljava/util/Map;
	public fun toString ()Ljava/lang/String;
}

public abstract interface class com/facebook/react/uimanager/ReactZIndexedViewGroup {
	public abstract fun getZIndexMappedChildIndex (I)I
	public abstract fun updateDrawingOrder ()V
}

public abstract interface class com/facebook/react/uimanager/RootView {
	public abstract fun handleException (Ljava/lang/Throwable;)V
	public abstract fun onChildEndedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	public fun onChildStartedNativeGesture (Landroid/view/MotionEvent;)V
	public abstract fun onChildStartedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
}

public final class com/facebook/react/uimanager/RootViewUtil {
	public static final field INSTANCE Lcom/facebook/react/uimanager/RootViewUtil;
	public static final fun getRootView (Landroid/view/View;)Lcom/facebook/react/uimanager/RootView;
	public static final fun getViewportOffset (Landroid/view/View;)Landroid/graphics/Point;
}

public abstract class com/facebook/react/uimanager/SimpleViewManager : com/facebook/react/uimanager/BaseViewManager {
	public fun <init> ()V
	public fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/LayoutShadowNode;
	public synthetic fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun getShadowNodeClass ()Ljava/lang/Class;
	public fun updateExtraData (Landroid/view/View;Ljava/lang/Object;)V
}

public final class com/facebook/react/uimanager/Spacing {
	public static final field ALL I
	public static final field BLOCK I
	public static final field BLOCK_END I
	public static final field BLOCK_START I
	public static final field BOTTOM I
	public static final field Companion Lcom/facebook/react/uimanager/Spacing$Companion;
	public static final field END I
	public static final field HORIZONTAL I
	public static final field LEFT I
	public static final field RIGHT I
	public static final field START I
	public static final field TOP I
	public static final field VERTICAL I
	public fun <init> ()V
	public fun <init> (F)V
	public fun <init> (F[F)V
	public fun <init> (Lcom/facebook/react/uimanager/Spacing;)V
	public final fun get (I)F
	public final fun getRaw (I)F
	public final fun getWithFallback (II)F
	public final fun reset ()V
	public final fun set (IF)Z
}

public final class com/facebook/react/uimanager/Spacing$Companion {
}

public abstract interface class com/facebook/react/uimanager/StateWrapper {
	public abstract fun destroyState ()V
	public abstract fun getStateData ()Lcom/facebook/react/bridge/ReadableNativeMap;
	public abstract fun getStateDataMapBuffer ()Lcom/facebook/react/common/mapbuffer/ReadableMapBuffer;
	public abstract fun updateState (Lcom/facebook/react/bridge/WritableMap;)V
}

public class com/facebook/react/uimanager/ThemedReactContext : com/facebook/react/bridge/ReactContext {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Landroid/content/Context;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Landroid/content/Context;Ljava/lang/String;)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Landroid/content/Context;Ljava/lang/String;I)V
	public fun addLifecycleEventListener (Lcom/facebook/react/bridge/LifecycleEventListener;)V
	public fun destroy ()V
	public fun getCatalystInstance ()Lcom/facebook/react/bridge/CatalystInstance;
	public fun getCurrentActivity ()Landroid/app/Activity;
	public fun getFabricUIManager ()Lcom/facebook/react/bridge/UIManager;
	public fun getJSCallInvokerHolder ()Lcom/facebook/react/turbomodule/core/interfaces/CallInvokerHolder;
	public fun getJSModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/JavaScriptModule;
	public fun getJavaScriptContextHolder ()Lcom/facebook/react/bridge/JavaScriptContextHolder;
	public fun getModuleName ()Ljava/lang/String;
	public fun getNativeModule (Ljava/lang/Class;)Lcom/facebook/react/bridge/NativeModule;
	public fun getNativeModule (Ljava/lang/String;)Lcom/facebook/react/bridge/NativeModule;
	public fun getNativeModules ()Ljava/util/Collection;
	public fun getReactApplicationContext ()Lcom/facebook/react/bridge/ReactApplicationContext;
	public fun getSourceURL ()Ljava/lang/String;
	public fun getSurfaceID ()Ljava/lang/String;
	public fun getSurfaceId ()I
	public fun handleException (Ljava/lang/Exception;)V
	public fun hasActiveCatalystInstance ()Z
	public fun hasActiveReactInstance ()Z
	public fun hasCatalystInstance ()Z
	public fun hasCurrentActivity ()Z
	public fun hasNativeModule (Ljava/lang/Class;)Z
	public fun hasReactInstance ()Z
	public fun isBridgeless ()Z
	public fun registerSegment (ILjava/lang/String;Lcom/facebook/react/bridge/Callback;)V
	public fun removeLifecycleEventListener (Lcom/facebook/react/bridge/LifecycleEventListener;)V
}

public class com/facebook/react/uimanager/TouchTargetHelper {
	public fun <init> ()V
	public static fun findTargetPathAndCoordinatesForTouch (FFLandroid/view/ViewGroup;[F)Ljava/util/List;
	public static fun findTargetTagAndCoordinatesForTouch (FFLandroid/view/ViewGroup;[F[I)I
	public static fun findTargetTagForTouch (FFLandroid/view/ViewGroup;)I
	public static fun findTargetTagForTouch (FFLandroid/view/ViewGroup;[I)I
}

public class com/facebook/react/uimanager/TouchTargetHelper$ViewTarget {
	public fun equals (Ljava/lang/Object;)Z
	public fun getView ()Landroid/view/View;
	public fun getViewId ()I
	public fun hashCode ()I
}

public class com/facebook/react/uimanager/TransformHelper {
	public fun <init> ()V
	public static fun processTransform (Lcom/facebook/react/bridge/ReadableArray;[D)V
	public static fun processTransform (Lcom/facebook/react/bridge/ReadableArray;[DFFLcom/facebook/react/bridge/ReadableArray;)V
	public static fun processTransform (Lcom/facebook/react/bridge/ReadableArray;[DFFLcom/facebook/react/bridge/ReadableArray;Z)V
}

public abstract interface class com/facebook/react/uimanager/UIBlock {
	public abstract fun execute (Lcom/facebook/react/uimanager/NativeViewHierarchyManager;)V
}

public class com/facebook/react/uimanager/UIImplementation {
	protected final field mEventDispatcher Lcom/facebook/react/uimanager/events/EventDispatcher;
	protected field mLayoutUpdateListener Lcom/facebook/react/uimanager/UIImplementation$LayoutUpdateListener;
	protected final field mReactContext Lcom/facebook/react/bridge/ReactApplicationContext;
	protected final field mShadowNodeRegistry Lcom/facebook/react/uimanager/ShadowNodeRegistry;
	protected field uiImplementationThreadLock Ljava/lang/Object;
	protected fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/uimanager/ViewManagerRegistry;Lcom/facebook/react/uimanager/UIViewOperationQueue;Lcom/facebook/react/uimanager/events/EventDispatcher;)V
	public fun addUIBlock (Lcom/facebook/react/uimanager/UIBlock;)V
	protected fun applyUpdatesRecursive (Lcom/facebook/react/uimanager/ReactShadowNode;FFLjava/util/List;)V
	protected fun calculateRootLayout (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public fun clearJSResponder ()V
	public fun configureNextLayoutAnimation (Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V
	protected fun createRootShadowNode ()Lcom/facebook/react/uimanager/ReactShadowNode;
	protected fun createShadowNode (Ljava/lang/String;)Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun createView (ILjava/lang/String;ILcom/facebook/react/bridge/ReadableMap;)V
	public fun dispatchViewManagerCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchViewManagerCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchViewUpdates (I)V
	public fun findSubviewIn (IFFLcom/facebook/react/bridge/Callback;)V
	public fun getProfiledBatchPerfCounters ()Ljava/util/Map;
	public fun getRootViewNum ()I
	protected fun handleCreateView (Lcom/facebook/react/uimanager/ReactShadowNode;ILcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	protected fun handleUpdateView (Lcom/facebook/react/uimanager/ReactShadowNode;Ljava/lang/String;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun manageChildren (ILcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun measure (ILcom/facebook/react/bridge/Callback;)V
	public fun measureInWindow (ILcom/facebook/react/bridge/Callback;)V
	public fun measureLayout (IILcom/facebook/react/bridge/Callback;Lcom/facebook/react/bridge/Callback;)V
	public fun measureLayoutRelativeToParent (ILcom/facebook/react/bridge/Callback;Lcom/facebook/react/bridge/Callback;)V
	public fun onCatalystInstanceDestroyed ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun prependUIBlock (Lcom/facebook/react/uimanager/UIBlock;)V
	public fun profileNextBatch ()V
	public fun registerRootView (Landroid/view/View;ILcom/facebook/react/uimanager/ThemedReactContext;)V
	public fun removeLayoutUpdateListener ()V
	public fun removeRootShadowNode (I)V
	public fun removeRootView (I)V
	protected final fun removeShadowNode (Lcom/facebook/react/uimanager/ReactShadowNode;)V
	public fun replaceExistingNonRootView (II)V
	public fun resolveRootTagFromReactTag (I)I
	public final fun resolveShadowNode (I)Lcom/facebook/react/uimanager/ReactShadowNode;
	protected final fun resolveViewManager (Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public fun sendAccessibilityEvent (II)V
	public fun setChildren (ILcom/facebook/react/bridge/ReadableArray;)V
	public fun setJSResponder (IZ)V
	public fun setLayoutAnimationEnabledExperimental (Z)V
	public fun setLayoutUpdateListener (Lcom/facebook/react/uimanager/UIImplementation$LayoutUpdateListener;)V
	public fun setViewHierarchyUpdateDebugListener (Lcom/facebook/react/uimanager/debug/NotThreadSafeViewHierarchyUpdateDebugListener;)V
	public fun setViewLocalData (ILjava/lang/Object;)V
	public fun synchronouslyUpdateViewOnUIThread (ILcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun updateInsetsPadding (IIIII)V
	public fun updateNodeSize (III)V
	public fun updateRootView (III)V
	public fun updateRootView (Lcom/facebook/react/uimanager/ReactShadowNode;II)V
	public fun updateView (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	protected fun updateViewHierarchy ()V
	public fun viewIsDescendantOf (IILcom/facebook/react/bridge/Callback;)V
}

public abstract interface class com/facebook/react/uimanager/UIImplementation$LayoutUpdateListener {
	public abstract fun onLayoutUpdated (Lcom/facebook/react/uimanager/ReactShadowNode;)V
}

public class com/facebook/react/uimanager/UIManagerHelper {
	public static final field PADDING_BOTTOM_INDEX I
	public static final field PADDING_END_INDEX I
	public static final field PADDING_START_INDEX I
	public static final field PADDING_TOP_INDEX I
	public fun <init> ()V
	public static fun getDefaultTextInputPadding (Landroid/content/Context;)[F
	public static fun getEventDispatcher (Lcom/facebook/react/bridge/ReactContext;I)Lcom/facebook/react/uimanager/events/EventDispatcher;
	public static fun getEventDispatcherForReactTag (Lcom/facebook/react/bridge/ReactContext;I)Lcom/facebook/react/uimanager/events/EventDispatcher;
	public static fun getReactContext (Landroid/view/View;)Lcom/facebook/react/bridge/ReactContext;
	public static fun getSurfaceId (Landroid/content/Context;)I
	public static fun getSurfaceId (Landroid/view/View;)I
	public static fun getUIManager (Lcom/facebook/react/bridge/ReactContext;I)Lcom/facebook/react/bridge/UIManager;
	public static fun getUIManagerForReactTag (Lcom/facebook/react/bridge/ReactContext;I)Lcom/facebook/react/bridge/UIManager;
}

public class com/facebook/react/uimanager/UIManagerModule : com/facebook/react/bridge/ReactContextBaseJavaModule, com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/bridge/OnBatchCompleteListener, com/facebook/react/bridge/UIManager {
	public static final field NAME Ljava/lang/String;
	public static final field TAG Ljava/lang/String;
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/uimanager/ViewManagerResolver;I)V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Ljava/util/List;I)V
	public fun addRootView (Landroid/view/View;)I
	public fun addRootView (Landroid/view/View;Lcom/facebook/react/bridge/WritableMap;)I
	public fun addUIBlock (Lcom/facebook/react/uimanager/UIBlock;)V
	public fun addUIManagerEventListener (Lcom/facebook/react/bridge/UIManagerListener;)V
	public fun addUIManagerListener (Lcom/facebook/react/uimanager/UIManagerModuleListener;)V
	public fun clearJSResponder ()V
	public fun configureNextLayoutAnimation (Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;Lcom/facebook/react/bridge/Callback;)V
	public static fun createConstants (Ljava/util/List;Ljava/util/Map;Ljava/util/Map;)Ljava/util/Map;
	public fun createView (ILjava/lang/String;ILcom/facebook/react/bridge/ReadableMap;)V
	public fun dispatchCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun dispatchViewManagerCommand (ILcom/facebook/react/bridge/Dynamic;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun findSubviewIn (ILcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/Callback;)V
	public fun getConstants ()Ljava/util/Map;
	public static fun getConstantsForViewManager (Lcom/facebook/react/uimanager/ViewManager;Ljava/util/Map;)Lcom/facebook/react/bridge/WritableMap;
	public fun getConstantsForViewManager (Ljava/lang/String;)Lcom/facebook/react/bridge/WritableMap;
	public fun getDefaultEventTypes ()Lcom/facebook/react/bridge/WritableMap;
	public fun getDirectEventNamesResolver ()Lcom/facebook/react/uimanager/UIManagerModule$CustomEventNamesResolver;
	public fun getEventDispatcher ()Lcom/facebook/react/uimanager/events/EventDispatcher;
	public fun getName ()Ljava/lang/String;
	public fun getPerformanceCounters ()Ljava/util/Map;
	public fun getUIImplementation ()Lcom/facebook/react/uimanager/UIImplementation;
	public fun getViewManagerRegistry_DO_NOT_USE ()Lcom/facebook/react/uimanager/ViewManagerRegistry;
	public fun initialize ()V
	public fun invalidate ()V
	public fun invalidateNodeLayout (I)V
	public fun manageChildren (ILcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun markActiveTouchForTag (II)V
	public fun measure (ILcom/facebook/react/bridge/Callback;)V
	public fun measureInWindow (ILcom/facebook/react/bridge/Callback;)V
	public fun measureLayout (IILcom/facebook/react/bridge/Callback;Lcom/facebook/react/bridge/Callback;)V
	public fun onBatchComplete ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun prependUIBlock (Lcom/facebook/react/uimanager/UIBlock;)V
	public fun profileNextBatch ()V
	public fun receiveEvent (IILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun receiveEvent (ILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public fun removeRootView (I)V
	public fun removeUIManagerEventListener (Lcom/facebook/react/bridge/UIManagerListener;)V
	public fun removeUIManagerListener (Lcom/facebook/react/uimanager/UIManagerModuleListener;)V
	public fun resolveCustomDirectEventName (Ljava/lang/String;)Ljava/lang/String;
	public fun resolveRootTagFromReactTag (I)I
	public fun resolveView (I)Landroid/view/View;
	public fun sendAccessibilityEvent (II)V
	public fun setChildren (ILcom/facebook/react/bridge/ReadableArray;)V
	public fun setJSResponder (IZ)V
	public fun setLayoutAnimationEnabledExperimental (Z)V
	public fun setViewHierarchyUpdateDebugListener (Lcom/facebook/react/uimanager/debug/NotThreadSafeViewHierarchyUpdateDebugListener;)V
	public fun setViewLocalData (ILjava/lang/Object;)V
	public fun startSurface (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/WritableMap;II)I
	public fun stopSurface (I)V
	public fun sweepActiveTouchForTag (II)V
	public fun synchronouslyUpdateViewOnUIThread (ILcom/facebook/react/bridge/ReadableMap;)V
	public fun updateInsetsPadding (IIIII)V
	public fun updateNodeSize (III)V
	public fun updateRootLayoutSpecs (IIIII)V
	public fun updateView (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun viewIsDescendantOf (IILcom/facebook/react/bridge/Callback;)V
}

public abstract interface class com/facebook/react/uimanager/UIManagerModule$CustomEventNamesResolver {
	public abstract fun resolveCustomEventName (Ljava/lang/String;)Ljava/lang/String;
}

public class com/facebook/react/uimanager/UIManagerModuleConstantsHelper {
	public fun <init> ()V
	public static fun getDefaultExportableEventTypes ()Ljava/util/Map;
}

public abstract interface class com/facebook/react/uimanager/UIManagerModuleListener {
	public abstract fun willDispatchViewUpdates (Lcom/facebook/react/uimanager/UIManagerModule;)V
}

public class com/facebook/react/uimanager/UIViewOperationQueue {
	public static final field DEFAULT_MIN_TIME_LEFT_IN_FRAME_FOR_NONBATCHED_OPERATION_MS I
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;Lcom/facebook/react/uimanager/NativeViewHierarchyManager;I)V
	public fun addRootView (ILandroid/view/View;)V
	public fun dispatchViewUpdates (IJJ)V
	public fun enqueueClearJSResponder ()V
	public fun enqueueConfigureLayoutAnimation (Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V
	public fun enqueueCreateView (Lcom/facebook/react/uimanager/ThemedReactContext;ILjava/lang/String;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun enqueueDispatchCommand (IILcom/facebook/react/bridge/ReadableArray;)V
	public fun enqueueDispatchCommand (ILjava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun enqueueFindTargetForTouch (IFFLcom/facebook/react/bridge/Callback;)V
	public fun enqueueLayoutUpdateFinished (Lcom/facebook/react/uimanager/ReactShadowNode;Lcom/facebook/react/uimanager/UIImplementation$LayoutUpdateListener;)V
	public fun enqueueManageChildren (I[I[Lcom/facebook/react/uimanager/ViewAtIndex;[I)V
	public fun enqueueMeasure (ILcom/facebook/react/bridge/Callback;)V
	public fun enqueueMeasureInWindow (ILcom/facebook/react/bridge/Callback;)V
	public fun enqueueRemoveRootView (I)V
	public fun enqueueSendAccessibilityEvent (II)V
	public fun enqueueSetChildren (ILcom/facebook/react/bridge/ReadableArray;)V
	public fun enqueueSetJSResponder (IIZ)V
	public fun enqueueSetLayoutAnimationEnabled (Z)V
	public fun enqueueUIBlock (Lcom/facebook/react/uimanager/UIBlock;)V
	protected fun enqueueUIOperation (Lcom/facebook/react/uimanager/UIViewOperationQueue$UIOperation;)V
	public fun enqueueUpdateExtraData (ILjava/lang/Object;)V
	public fun enqueueUpdateInstanceHandle (IJ)V
	public fun enqueueUpdateLayout (IIIIII)V
	public fun enqueueUpdateLayout (IIIIIILcom/facebook/yoga/YogaDirection;)V
	public fun enqueueUpdateProperties (ILjava/lang/String;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun getProfiledBatchPerfCounters ()Ljava/util/Map;
	public fun isEmpty ()Z
	public fun prependUIBlock (Lcom/facebook/react/uimanager/UIBlock;)V
	public fun profileNextBatch ()V
	public fun setViewHierarchyUpdateDebugListener (Lcom/facebook/react/uimanager/debug/NotThreadSafeViewHierarchyUpdateDebugListener;)V
}

public abstract interface class com/facebook/react/uimanager/UIViewOperationQueue$UIOperation {
	public abstract fun execute ()V
}

public class com/facebook/react/uimanager/ViewAtIndex {
	public static field COMPARATOR Ljava/util/Comparator;
	public final field mIndex I
	public final field mTag I
	public fun <init> (II)V
	public fun equals (Ljava/lang/Object;)Z
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/uimanager/ViewDefaults {
	public static final field FONT_SIZE_SP F
	public static final field INSTANCE Lcom/facebook/react/uimanager/ViewDefaults;
	public static final field LINE_HEIGHT I
	public static final field NUMBER_OF_LINES I
}

public class com/facebook/react/uimanager/ViewGroupDrawingOrderHelper {
	public fun <init> (Landroid/view/ViewGroup;)V
	public fun getChildDrawingOrder (II)I
	public fun handleAddView (Landroid/view/View;)V
	public fun handleRemoveView (Landroid/view/View;)V
	public fun shouldEnableCustomDrawingOrder ()Z
	public fun update ()V
}

public abstract class com/facebook/react/uimanager/ViewGroupManager : com/facebook/react/uimanager/BaseViewManager, com/facebook/react/uimanager/IViewGroupManager {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public synthetic fun addView (Landroid/view/View;Landroid/view/View;I)V
	public fun addView (Landroid/view/ViewGroup;Landroid/view/View;I)V
	public fun addViews (Landroid/view/ViewGroup;Ljava/util/List;)V
	public fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/LayoutShadowNode;
	public synthetic fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public synthetic fun getChildAt (Landroid/view/View;I)Landroid/view/View;
	public fun getChildAt (Landroid/view/ViewGroup;I)Landroid/view/View;
	public synthetic fun getChildCount (Landroid/view/View;)I
	public fun getChildCount (Landroid/view/ViewGroup;)I
	public fun getShadowNodeClass ()Ljava/lang/Class;
	public static fun getViewZIndex (Landroid/view/View;)Ljava/lang/Integer;
	public fun needsCustomLayoutForChildren ()Z
	public fun removeView (Landroid/view/ViewGroup;Landroid/view/View;)V
	public synthetic fun removeViewAt (Landroid/view/View;I)V
	public fun removeViewAt (Landroid/view/ViewGroup;I)V
	public static fun setViewZIndex (Landroid/view/View;I)V
	public synthetic fun updateExtraData (Landroid/view/View;Ljava/lang/Object;)V
	public fun updateExtraData (Landroid/view/ViewGroup;Ljava/lang/Object;)V
}

public abstract class com/facebook/react/uimanager/ViewManager : com/facebook/react/bridge/BaseJavaModule {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	protected fun addEventEmitters (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)V
	public fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun createShadowNodeInstance (Lcom/facebook/react/bridge/ReactApplicationContext;)Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun createView (ILcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;Lcom/facebook/react/touch/JSResponderHandler;)Landroid/view/View;
	protected fun createViewInstance (ILcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Landroid/view/View;
	protected abstract fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun getCommandsMap ()Ljava/util/Map;
	protected fun getDelegate ()Lcom/facebook/react/uimanager/ViewManagerDelegate;
	public fun getExportedCustomBubblingEventTypeConstants ()Ljava/util/Map;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getExportedViewConstants ()Ljava/util/Map;
	public abstract fun getName ()Ljava/lang/String;
	public fun getNativeProps ()Ljava/util/Map;
	public abstract fun getShadowNodeClass ()Ljava/lang/Class;
	public fun measure (Landroid/content/Context;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/ReadableMap;FLcom/facebook/yoga/YogaMeasureMode;FLcom/facebook/yoga/YogaMeasureMode;[F)J
	public fun measure (Landroid/content/Context;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;FLcom/facebook/yoga/YogaMeasureMode;FLcom/facebook/yoga/YogaMeasureMode;[F)J
	protected fun onAfterUpdateTransaction (Landroid/view/View;)V
	public fun onDropViewInstance (Landroid/view/View;)V
	public fun onSurfaceStopped (I)V
	protected abstract fun prepareToRecycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)Landroid/view/View;
	public fun receiveCommand (Landroid/view/View;ILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	protected fun recycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)Landroid/view/View;
	public fun setPadding (Landroid/view/View;IIII)V
	protected fun setupViewRecycling ()V
	public abstract fun updateExtraData (Landroid/view/View;Ljava/lang/Object;)V
	public fun updateProperties (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public fun updateState (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
}

public abstract interface class com/facebook/react/uimanager/ViewManagerDelegate {
	public abstract synthetic fun kotlinCompat$receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract synthetic fun kotlinCompat$setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public final class com/facebook/react/uimanager/ViewManagerPropertyUpdater {
	public static final field INSTANCE Lcom/facebook/react/uimanager/ViewManagerPropertyUpdater;
	public static final fun clear ()V
	public static final fun getNativeProps (Ljava/lang/Class;Ljava/lang/Class;)Ljava/util/Map;
	public static final fun updateProps (Lcom/facebook/react/uimanager/ReactShadowNode;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public static final fun updateProps (Lcom/facebook/react/uimanager/ViewManager;Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
	public static final fun updateProps (Lcom/facebook/react/uimanager/ViewManagerDelegate;Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;)V
}

public abstract interface class com/facebook/react/uimanager/ViewManagerPropertyUpdater$Settable {
	public abstract fun getProperties (Ljava/util/Map;)V
}

public abstract interface class com/facebook/react/uimanager/ViewManagerPropertyUpdater$ShadowNodeSetter : com/facebook/react/uimanager/ViewManagerPropertyUpdater$Settable {
	public abstract fun setProperty (Lcom/facebook/react/uimanager/ReactShadowNode;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/uimanager/ViewManagerPropertyUpdater$ViewManagerSetter : com/facebook/react/uimanager/ViewManagerPropertyUpdater$Settable {
	public abstract fun setProperty (Lcom/facebook/react/uimanager/ViewManager;Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public final class com/facebook/react/uimanager/ViewManagerRegistry : android/content/ComponentCallbacks2 {
	public fun <init> (Lcom/facebook/react/uimanager/ViewManagerResolver;)V
	public fun <init> (Ljava/util/List;)V
	public fun <init> (Ljava/util/Map;)V
	public fun get (Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public fun invalidate ()V
	public fun onConfigurationChanged (Landroid/content/res/Configuration;)V
	public fun onLowMemory ()V
	public fun onSurfaceStopped (I)V
	public fun onTrimMemory (I)V
}

public abstract interface class com/facebook/react/uimanager/ViewManagerResolver {
	public abstract fun getViewManager (Ljava/lang/String;)Lcom/facebook/react/uimanager/ViewManager;
	public abstract fun getViewManagerNames ()Ljava/util/Collection;
}

public abstract interface class com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
}

public final class com/facebook/react/uimanager/ViewProps {
	public static final field ACCESSIBILITY_ACTIONS Ljava/lang/String;
	public static final field ACCESSIBILITY_COLLECTION Ljava/lang/String;
	public static final field ACCESSIBILITY_COLLECTION_ITEM Ljava/lang/String;
	public static final field ACCESSIBILITY_HINT Ljava/lang/String;
	public static final field ACCESSIBILITY_LABEL Ljava/lang/String;
	public static final field ACCESSIBILITY_LABELLED_BY Ljava/lang/String;
	public static final field ACCESSIBILITY_LIVE_REGION Ljava/lang/String;
	public static final field ACCESSIBILITY_ROLE Ljava/lang/String;
	public static final field ACCESSIBILITY_STATE Ljava/lang/String;
	public static final field ACCESSIBILITY_VALUE Ljava/lang/String;
	public static final field ADJUSTS_FONT_SIZE_TO_FIT Ljava/lang/String;
	public static final field ALIGN_CONTENT Ljava/lang/String;
	public static final field ALIGN_ITEMS Ljava/lang/String;
	public static final field ALIGN_SELF Ljava/lang/String;
	public static final field ALLOW_FONT_SCALING Ljava/lang/String;
	public static final field ASPECT_RATIO Ljava/lang/String;
	public static final field AUTO Ljava/lang/String;
	public static final field BACKGROUND_COLOR Ljava/lang/String;
	public static final field BACKGROUND_IMAGE Ljava/lang/String;
	public static final field BORDER_BLOCK_COLOR Ljava/lang/String;
	public static final field BORDER_BLOCK_END_COLOR Ljava/lang/String;
	public static final field BORDER_BLOCK_START_COLOR Ljava/lang/String;
	public static final field BORDER_BOTTOM_COLOR Ljava/lang/String;
	public static final field BORDER_BOTTOM_END_RADIUS Ljava/lang/String;
	public static final field BORDER_BOTTOM_LEFT_RADIUS Ljava/lang/String;
	public static final field BORDER_BOTTOM_RIGHT_RADIUS Ljava/lang/String;
	public static final field BORDER_BOTTOM_START_RADIUS Ljava/lang/String;
	public static final field BORDER_BOTTOM_WIDTH Ljava/lang/String;
	public static final field BORDER_COLOR Ljava/lang/String;
	public static final field BORDER_END_COLOR Ljava/lang/String;
	public static final field BORDER_END_END_RADIUS Ljava/lang/String;
	public static final field BORDER_END_START_RADIUS Ljava/lang/String;
	public static final field BORDER_END_WIDTH Ljava/lang/String;
	public static final field BORDER_LEFT_COLOR Ljava/lang/String;
	public static final field BORDER_LEFT_WIDTH Ljava/lang/String;
	public static final field BORDER_RADIUS Ljava/lang/String;
	public static final field BORDER_RIGHT_COLOR Ljava/lang/String;
	public static final field BORDER_RIGHT_WIDTH Ljava/lang/String;
	public static final field BORDER_SPACING_TYPES [I
	public static final field BORDER_START_COLOR Ljava/lang/String;
	public static final field BORDER_START_END_RADIUS Ljava/lang/String;
	public static final field BORDER_START_START_RADIUS Ljava/lang/String;
	public static final field BORDER_START_WIDTH Ljava/lang/String;
	public static final field BORDER_TOP_COLOR Ljava/lang/String;
	public static final field BORDER_TOP_END_RADIUS Ljava/lang/String;
	public static final field BORDER_TOP_LEFT_RADIUS Ljava/lang/String;
	public static final field BORDER_TOP_RIGHT_RADIUS Ljava/lang/String;
	public static final field BORDER_TOP_START_RADIUS Ljava/lang/String;
	public static final field BORDER_TOP_WIDTH Ljava/lang/String;
	public static final field BORDER_WIDTH Ljava/lang/String;
	public static final field BOTTOM Ljava/lang/String;
	public static final field BOX_NONE Ljava/lang/String;
	public static final field BOX_SHADOW Ljava/lang/String;
	public static final field COLLAPSABLE Ljava/lang/String;
	public static final field COLLAPSABLE_CHILDREN Ljava/lang/String;
	public static final field COLOR Ljava/lang/String;
	public static final field COLUMN_GAP Ljava/lang/String;
	public static final field DISPLAY Ljava/lang/String;
	public static final field ELEVATION Ljava/lang/String;
	public static final field ELLIPSIZE_MODE Ljava/lang/String;
	public static final field ENABLED Ljava/lang/String;
	public static final field END Ljava/lang/String;
	public static final field FILTER Ljava/lang/String;
	public static final field FLEX Ljava/lang/String;
	public static final field FLEX_BASIS Ljava/lang/String;
	public static final field FLEX_DIRECTION Ljava/lang/String;
	public static final field FLEX_GROW Ljava/lang/String;
	public static final field FLEX_SHRINK Ljava/lang/String;
	public static final field FLEX_WRAP Ljava/lang/String;
	public static final field FONT_FAMILY Ljava/lang/String;
	public static final field FONT_SIZE Ljava/lang/String;
	public static final field FONT_STYLE Ljava/lang/String;
	public static final field FONT_VARIANT Ljava/lang/String;
	public static final field FONT_WEIGHT Ljava/lang/String;
	public static final field FOREGROUND_COLOR Ljava/lang/String;
	public static final field GAP Ljava/lang/String;
	public static final field HEIGHT Ljava/lang/String;
	public static final field HIDDEN Ljava/lang/String;
	public static final field IMPORTANT_FOR_ACCESSIBILITY Ljava/lang/String;
	public static final field INCLUDE_FONT_PADDING Ljava/lang/String;
	public static final field INSTANCE Lcom/facebook/react/uimanager/ViewProps;
	public static final field JUSTIFY_CONTENT Ljava/lang/String;
	public static final field LAYOUT_DIRECTION Ljava/lang/String;
	public static final field LEFT Ljava/lang/String;
	public static final field LETTER_SPACING Ljava/lang/String;
	public static final field LINE_HEIGHT Ljava/lang/String;
	public static final field MARGIN Ljava/lang/String;
	public static final field MARGIN_BOTTOM Ljava/lang/String;
	public static final field MARGIN_END Ljava/lang/String;
	public static final field MARGIN_HORIZONTAL Ljava/lang/String;
	public static final field MARGIN_LEFT Ljava/lang/String;
	public static final field MARGIN_RIGHT Ljava/lang/String;
	public static final field MARGIN_START Ljava/lang/String;
	public static final field MARGIN_TOP Ljava/lang/String;
	public static final field MARGIN_VERTICAL Ljava/lang/String;
	public static final field MAX_FONT_SIZE_MULTIPLIER Ljava/lang/String;
	public static final field MAX_HEIGHT Ljava/lang/String;
	public static final field MAX_WIDTH Ljava/lang/String;
	public static final field MINIMUM_FONT_SCALE Ljava/lang/String;
	public static final field MIN_HEIGHT Ljava/lang/String;
	public static final field MIN_WIDTH Ljava/lang/String;
	public static final field MIX_BLEND_MODE Ljava/lang/String;
	public static final field NATIVE_ID Ljava/lang/String;
	public static final field NEEDS_OFFSCREEN_ALPHA_COMPOSITING Ljava/lang/String;
	public static final field NONE Ljava/lang/String;
	public static final field NUMBER_OF_LINES Ljava/lang/String;
	public static final field ON Ljava/lang/String;
	public static final field OPACITY Ljava/lang/String;
	public static final field OUTLINE_COLOR Ljava/lang/String;
	public static final field OUTLINE_OFFSET Ljava/lang/String;
	public static final field OUTLINE_STYLE Ljava/lang/String;
	public static final field OUTLINE_WIDTH Ljava/lang/String;
	public static final field OVERFLOW Ljava/lang/String;
	public static final field PADDING Ljava/lang/String;
	public static final field PADDING_BOTTOM Ljava/lang/String;
	public static final field PADDING_END Ljava/lang/String;
	public static final field PADDING_HORIZONTAL Ljava/lang/String;
	public static final field PADDING_LEFT Ljava/lang/String;
	public static final field PADDING_MARGIN_SPACING_TYPES [I
	public static final field PADDING_RIGHT Ljava/lang/String;
	public static final field PADDING_START Ljava/lang/String;
	public static final field PADDING_TOP Ljava/lang/String;
	public static final field PADDING_VERTICAL Ljava/lang/String;
	public static final field POINTER_EVENTS Ljava/lang/String;
	public static final field POSITION Ljava/lang/String;
	public static final field RENDER_TO_HARDWARE_TEXTURE Ljava/lang/String;
	public static final field RESIZE_METHOD Ljava/lang/String;
	public static final field RESIZE_MODE Ljava/lang/String;
	public static final field RIGHT Ljava/lang/String;
	public static final field ROLE Ljava/lang/String;
	public static final field ROTATION Ljava/lang/String;
	public static final field ROW_GAP Ljava/lang/String;
	public static final field SCALE_X Ljava/lang/String;
	public static final field SCALE_Y Ljava/lang/String;
	public static final field SCROLL Ljava/lang/String;
	public static final field SHADOW_COLOR Ljava/lang/String;
	public static final field START Ljava/lang/String;
	public static final field TEST_ID Ljava/lang/String;
	public static final field TEXT_ALIGN Ljava/lang/String;
	public static final field TEXT_ALIGN_VERTICAL Ljava/lang/String;
	public static final field TEXT_BREAK_STRATEGY Ljava/lang/String;
	public static final field TEXT_DECORATION_LINE Ljava/lang/String;
	public static final field TOP Ljava/lang/String;
	public static final field TRANSFORM Ljava/lang/String;
	public static final field TRANSFORM_ORIGIN Ljava/lang/String;
	public static final field TRANSLATE_X Ljava/lang/String;
	public static final field TRANSLATE_Y Ljava/lang/String;
	public static final field VIEW_CLASS_NAME Ljava/lang/String;
	public static final field VISIBLE Ljava/lang/String;
	public static final field WIDTH Ljava/lang/String;
	public static final field Z_INDEX Ljava/lang/String;
	public static final fun isLayoutOnly (Lcom/facebook/react/bridge/ReadableMap;Ljava/lang/String;)Z
}

public abstract interface annotation class com/facebook/react/uimanager/annotations/ReactProp : java/lang/annotation/Annotation {
	public static final field USE_DEFAULT_TYPE Ljava/lang/String;
	public abstract fun customType ()Ljava/lang/String;
	public abstract fun defaultBoolean ()Z
	public abstract fun defaultDouble ()D
	public abstract fun defaultFloat ()F
	public abstract fun defaultInt ()I
	public abstract fun defaultLong ()J
	public abstract fun name ()Ljava/lang/String;
}

public abstract interface annotation class com/facebook/react/uimanager/annotations/ReactPropGroup : java/lang/annotation/Annotation {
	public static final field USE_DEFAULT_TYPE Ljava/lang/String;
	public abstract fun customType ()Ljava/lang/String;
	public abstract fun defaultDouble ()D
	public abstract fun defaultFloat ()F
	public abstract fun defaultInt ()I
	public abstract fun defaultLong ()J
	public abstract fun names ()[Ljava/lang/String;
}

public abstract interface annotation class com/facebook/react/uimanager/annotations/ReactPropertyHolder : java/lang/annotation/Annotation {
}

public abstract interface annotation class com/facebook/react/uimanager/common/UIManagerType : java/lang/annotation/Annotation {
	public static final field Companion Lcom/facebook/react/uimanager/common/UIManagerType$Companion;
	public static final field DEFAULT I
	public static final field FABRIC I
}

public final class com/facebook/react/uimanager/common/UIManagerType$Companion {
	public static final field DEFAULT I
	public static final field FABRIC I
}

public final class com/facebook/react/uimanager/common/ViewUtil {
	public static final field INSTANCE Lcom/facebook/react/uimanager/common/ViewUtil;
	public static final field NO_SURFACE_ID I
	public static final fun getUIManagerType (I)I
	public static final fun getUIManagerType (II)I
	public static final fun getUIManagerType (Landroid/view/View;)I
	public static final fun isRootTag (I)Z
}

public abstract interface class com/facebook/react/uimanager/events/BatchEventDispatchedListener {
	public abstract fun onBatchEventDispatched ()V
}

public final class com/facebook/react/uimanager/events/ContentSizeChangeEvent : com/facebook/react/uimanager/events/Event {
	public fun <init> (III)V
	public fun <init> (IIII)V
	public fun getEventName ()Ljava/lang/String;
}

public abstract class com/facebook/react/uimanager/events/Event {
	protected fun <init> ()V
	protected fun <init> (I)V
	protected fun <init> (II)V
	public fun canCoalesce ()Z
	public fun coalesce (Lcom/facebook/react/uimanager/events/Event;)Lcom/facebook/react/uimanager/events/Event;
	public fun dispatch (Lcom/facebook/react/uimanager/events/RCTEventEmitter;)V
	public fun dispatchModern (Lcom/facebook/react/uimanager/events/RCTModernEventEmitter;)V
	protected fun experimental_isSynchronous ()Z
	public fun getCoalescingKey ()S
	public fun getEventAnimationDriverMatchSpec ()Lcom/facebook/react/uimanager/events/Event$EventAnimationDriverMatchSpec;
	protected fun getEventCategory ()I
	protected fun getEventData ()Lcom/facebook/react/bridge/WritableMap;
	public abstract fun getEventName ()Ljava/lang/String;
	public final fun getSurfaceId ()I
	public final fun getTimestampMs ()J
	public fun getUniqueID ()I
	public final fun getViewTag ()I
	protected fun init (I)V
	protected fun init (II)V
	protected fun init (IIJ)V
	public fun onDispose ()V
}

public abstract interface class com/facebook/react/uimanager/events/Event$EventAnimationDriverMatchSpec {
	public abstract fun match (ILjava/lang/String;)Z
}

public abstract interface annotation class com/facebook/react/uimanager/events/EventCategoryDef : java/lang/annotation/Annotation {
	public static final field CONTINUOUS I
	public static final field CONTINUOUS_END I
	public static final field CONTINUOUS_START I
	public static final field Companion Lcom/facebook/react/uimanager/events/EventCategoryDef$Companion;
	public static final field DISCRETE I
	public static final field UNSPECIFIED I
}

public final class com/facebook/react/uimanager/events/EventCategoryDef$Companion {
	public static final field CONTINUOUS I
	public static final field CONTINUOUS_END I
	public static final field CONTINUOUS_START I
	public static final field DISCRETE I
	public static final field UNSPECIFIED I
}

public abstract interface class com/facebook/react/uimanager/events/EventDispatcher {
	public abstract fun addBatchEventDispatchedListener (Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public abstract fun addListener (Lcom/facebook/react/uimanager/events/EventDispatcherListener;)V
	public abstract fun dispatchAllEvents ()V
	public abstract fun dispatchEvent (Lcom/facebook/react/uimanager/events/Event;)V
	public abstract fun onCatalystInstanceDestroyed ()V
	public abstract fun registerEventEmitter (ILcom/facebook/react/uimanager/events/RCTEventEmitter;)V
	public abstract fun registerEventEmitter (ILcom/facebook/react/uimanager/events/RCTModernEventEmitter;)V
	public abstract fun removeBatchEventDispatchedListener (Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public abstract fun removeListener (Lcom/facebook/react/uimanager/events/EventDispatcherListener;)V
	public abstract fun unregisterEventEmitter (I)V
}

public class com/facebook/react/uimanager/events/EventDispatcherImpl : com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/uimanager/events/EventDispatcher {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun addBatchEventDispatchedListener (Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public fun addListener (Lcom/facebook/react/uimanager/events/EventDispatcherListener;)V
	public fun dispatchAllEvents ()V
	public fun dispatchEvent (Lcom/facebook/react/uimanager/events/Event;)V
	public fun onCatalystInstanceDestroyed ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun registerEventEmitter (ILcom/facebook/react/uimanager/events/RCTEventEmitter;)V
	public fun registerEventEmitter (ILcom/facebook/react/uimanager/events/RCTModernEventEmitter;)V
	public fun removeBatchEventDispatchedListener (Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public fun removeListener (Lcom/facebook/react/uimanager/events/EventDispatcherListener;)V
	public fun unregisterEventEmitter (I)V
}

public abstract interface class com/facebook/react/uimanager/events/EventDispatcherListener {
	public abstract fun onEventDispatch (Lcom/facebook/react/uimanager/events/Event;)V
}

public abstract interface class com/facebook/react/uimanager/events/EventDispatcherProvider {
	public abstract fun getEventDispatcher ()Lcom/facebook/react/uimanager/events/EventDispatcher;
}

public class com/facebook/react/uimanager/events/FabricEventDispatcher : com/facebook/react/bridge/LifecycleEventListener, com/facebook/react/uimanager/events/EventDispatcher {
	public fun <init> (Lcom/facebook/react/bridge/ReactApplicationContext;)V
	public fun addBatchEventDispatchedListener (Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public fun addListener (Lcom/facebook/react/uimanager/events/EventDispatcherListener;)V
	public fun dispatchAllEvents ()V
	public fun dispatchEvent (Lcom/facebook/react/uimanager/events/Event;)V
	public fun onCatalystInstanceDestroyed ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun registerEventEmitter (ILcom/facebook/react/uimanager/events/RCTEventEmitter;)V
	public fun registerEventEmitter (ILcom/facebook/react/uimanager/events/RCTModernEventEmitter;)V
	public fun removeBatchEventDispatchedListener (Lcom/facebook/react/uimanager/events/BatchEventDispatchedListener;)V
	public fun removeListener (Lcom/facebook/react/uimanager/events/EventDispatcherListener;)V
	public fun unregisterEventEmitter (I)V
}

public final class com/facebook/react/uimanager/events/NativeGestureUtil {
	public static final field INSTANCE Lcom/facebook/react/uimanager/events/NativeGestureUtil;
	public static final fun notifyNativeGestureEnded (Landroid/view/View;Landroid/view/MotionEvent;)V
	public static final fun notifyNativeGestureStarted (Landroid/view/View;Landroid/view/MotionEvent;)V
}

public class com/facebook/react/uimanager/events/PointerEvent : com/facebook/react/uimanager/events/Event {
	public fun dispatch (Lcom/facebook/react/uimanager/events/RCTEventEmitter;)V
	public fun dispatchModern (Lcom/facebook/react/uimanager/events/RCTModernEventEmitter;)V
	public fun getCoalescingKey ()S
	public fun getEventAnimationDriverMatchSpec ()Lcom/facebook/react/uimanager/events/Event$EventAnimationDriverMatchSpec;
	public fun getEventName ()Ljava/lang/String;
	public static fun obtain (Ljava/lang/String;ILcom/facebook/react/uimanager/events/PointerEvent$PointerEventState;Landroid/view/MotionEvent;)Lcom/facebook/react/uimanager/events/PointerEvent;
	public static fun obtain (Ljava/lang/String;ILcom/facebook/react/uimanager/events/PointerEvent$PointerEventState;Landroid/view/MotionEvent;S)Lcom/facebook/react/uimanager/events/PointerEvent;
	public fun onDispose ()V
}

public class com/facebook/react/uimanager/events/PointerEvent$PointerEventState {
	public fun <init> (IIIILjava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Map;Ljava/util/Set;)V
	public fun getActivePointerId ()I
	public final fun getEventCoordinatesByPointerId ()Ljava/util/Map;
	public final fun getHitPathByPointerId ()Ljava/util/Map;
	public final fun getHitPathForActivePointer ()Ljava/util/List;
	public fun getHoveringPointerIds ()Ljava/util/Set;
	public fun getLastButtonState ()I
	public final fun getOffsetByPointerId ()Ljava/util/Map;
	public fun getPrimaryPointerId ()I
	public final fun getScreenCoordinatesByPointerId ()Ljava/util/Map;
	public fun getSurfaceId ()I
	public fun supportsHover (I)Z
}

public final class com/facebook/react/uimanager/events/PointerEventHelper {
	public static final field CLICK Ljava/lang/String;
	public static final field INSTANCE Lcom/facebook/react/uimanager/events/PointerEventHelper;
	public static final field POINTER_CANCEL Ljava/lang/String;
	public static final field POINTER_DOWN Ljava/lang/String;
	public static final field POINTER_ENTER Ljava/lang/String;
	public static final field POINTER_LEAVE Ljava/lang/String;
	public static final field POINTER_MOVE Ljava/lang/String;
	public static final field POINTER_OUT Ljava/lang/String;
	public static final field POINTER_OVER Ljava/lang/String;
	public static final field POINTER_TYPE_MOUSE Ljava/lang/String;
	public static final field POINTER_TYPE_PEN Ljava/lang/String;
	public static final field POINTER_TYPE_TOUCH Ljava/lang/String;
	public static final field POINTER_TYPE_UNKNOWN Ljava/lang/String;
	public static final field POINTER_UP Ljava/lang/String;
	public static final fun getButtonChange (Ljava/lang/String;II)I
	public static final fun getButtons (Ljava/lang/String;Ljava/lang/String;I)I
	public static final fun getEventCategory (Ljava/lang/String;)I
	public static final fun getPressure (ILjava/lang/String;)D
	public static final fun getW3CPointerType (I)Ljava/lang/String;
	public static final fun isBubblingEvent (Ljava/lang/String;)Z
	public final fun isExitEvent (Ljava/lang/String;)Z
	public static final fun isListening (Landroid/view/View;Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;)Z
	public final fun supportsHover (Landroid/view/MotionEvent;)Z
}

public final class com/facebook/react/uimanager/events/PointerEventHelper$EVENT : java/lang/Enum {
	public static final field CANCEL Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field CANCEL_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field CLICK Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field CLICK_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field DOWN Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field DOWN_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field ENTER Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field ENTER_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field LEAVE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field LEAVE_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field MOVE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field MOVE_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field OUT Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field OUT_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field OVER Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field OVER_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field UP Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static final field UP_CAPTURE Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
	public static fun values ()[Lcom/facebook/react/uimanager/events/PointerEventHelper$EVENT;
}

public abstract interface class com/facebook/react/uimanager/events/RCTEventEmitter : com/facebook/react/bridge/JavaScriptModule {
	public abstract fun receiveEvent (ILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun receiveTouches (Ljava/lang/String;Lcom/facebook/react/bridge/WritableArray;Lcom/facebook/react/bridge/WritableArray;)V
}

public abstract interface class com/facebook/react/uimanager/events/RCTModernEventEmitter : com/facebook/react/uimanager/events/RCTEventEmitter {
	public abstract fun receiveEvent (IILjava/lang/String;Lcom/facebook/react/bridge/WritableMap;)V
	public abstract fun receiveEvent (IILjava/lang/String;ZILcom/facebook/react/bridge/WritableMap;I)V
	public abstract fun receiveTouches (Lcom/facebook/react/uimanager/events/TouchEvent;)V
}

public final class com/facebook/react/uimanager/events/TouchEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/uimanager/events/TouchEvent$Companion;
	public static final field UNSET J
	public fun canCoalesce ()Z
	public fun dispatch (Lcom/facebook/react/uimanager/events/RCTEventEmitter;)V
	public fun dispatchModern (Lcom/facebook/react/uimanager/events/RCTModernEventEmitter;)V
	public fun getCoalescingKey ()S
	public fun getEventCategory ()I
	public fun getEventName ()Ljava/lang/String;
	public final fun getMotionEvent ()Landroid/view/MotionEvent;
	public final fun getTouchEventType ()Lcom/facebook/react/uimanager/events/TouchEventType;
	public final fun getViewX ()F
	public final fun getViewY ()F
	public static final fun obtain (IILcom/facebook/react/uimanager/events/TouchEventType;Landroid/view/MotionEvent;JFFLcom/facebook/react/uimanager/events/TouchEventCoalescingKeyHelper;)Lcom/facebook/react/uimanager/events/TouchEvent;
	public static final fun obtain (ILcom/facebook/react/uimanager/events/TouchEventType;Landroid/view/MotionEvent;JFFLcom/facebook/react/uimanager/events/TouchEventCoalescingKeyHelper;)Lcom/facebook/react/uimanager/events/TouchEvent;
	public fun onDispose ()V
}

public final class com/facebook/react/uimanager/events/TouchEvent$Companion {
	public final fun obtain (IILcom/facebook/react/uimanager/events/TouchEventType;Landroid/view/MotionEvent;JFFLcom/facebook/react/uimanager/events/TouchEventCoalescingKeyHelper;)Lcom/facebook/react/uimanager/events/TouchEvent;
	public final fun obtain (ILcom/facebook/react/uimanager/events/TouchEventType;Landroid/view/MotionEvent;JFFLcom/facebook/react/uimanager/events/TouchEventCoalescingKeyHelper;)Lcom/facebook/react/uimanager/events/TouchEvent;
}

public final class com/facebook/react/uimanager/events/TouchEventCoalescingKeyHelper {
	public fun <init> ()V
	public final fun addCoalescingKey (J)V
	public final fun getCoalescingKey (J)S
	public final fun hasCoalescingKey (J)Z
	public final fun incrementCoalescingKey (J)V
	public final fun removeCoalescingKey (J)V
}

public final class com/facebook/react/uimanager/events/TouchEventType : java/lang/Enum {
	public static final field CANCEL Lcom/facebook/react/uimanager/events/TouchEventType;
	public static final field Companion Lcom/facebook/react/uimanager/events/TouchEventType$Companion;
	public static final field END Lcom/facebook/react/uimanager/events/TouchEventType;
	public static final field MOVE Lcom/facebook/react/uimanager/events/TouchEventType;
	public static final field START Lcom/facebook/react/uimanager/events/TouchEventType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static final fun getJSEventName (Lcom/facebook/react/uimanager/events/TouchEventType;)Ljava/lang/String;
	public final fun getJsName ()Ljava/lang/String;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/events/TouchEventType;
	public static fun values ()[Lcom/facebook/react/uimanager/events/TouchEventType;
}

public final class com/facebook/react/uimanager/events/TouchEventType$Companion {
	public final fun getJSEventName (Lcom/facebook/react/uimanager/events/TouchEventType;)Ljava/lang/String;
}

public final class com/facebook/react/uimanager/events/TouchesHelper {
	public static final field INSTANCE Lcom/facebook/react/uimanager/events/TouchesHelper;
	public static final field TARGET_KEY Ljava/lang/String;
	public static final fun sendTouchEvent (Lcom/facebook/react/uimanager/events/RCTModernEventEmitter;Lcom/facebook/react/uimanager/events/TouchEvent;)V
	public static final fun sendTouchesLegacy (Lcom/facebook/react/uimanager/events/RCTEventEmitter;Lcom/facebook/react/uimanager/events/TouchEvent;)V
}

public class com/facebook/react/uimanager/layoutanimation/LayoutAnimationController {
	public fun <init> ()V
	public fun applyLayoutUpdate (Landroid/view/View;IIII)V
	public fun deleteView (Landroid/view/View;Lcom/facebook/react/uimanager/layoutanimation/LayoutAnimationListener;)V
	public fun initializeFromConfig (Lcom/facebook/react/bridge/ReadableMap;Lcom/facebook/react/bridge/Callback;)V
	public fun reset ()V
	public fun shouldAnimateLayout (Landroid/view/View;)Z
}

public abstract interface class com/facebook/react/uimanager/layoutanimation/LayoutAnimationListener {
	public abstract fun onAnimationEnd ()V
}

public final class com/facebook/react/uimanager/style/BackgroundImageLayer {
	public fun <init> (Lcom/facebook/react/bridge/ReadableMap;Landroid/content/Context;)V
	public final fun getShader (Landroid/graphics/Rect;)Landroid/graphics/Shader;
}

public final class com/facebook/react/uimanager/style/BorderRadiusProp : java/lang/Enum {
	public static final field BORDER_BOTTOM_END_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_BOTTOM_LEFT_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_BOTTOM_RIGHT_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_BOTTOM_START_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_END_END_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_END_START_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_START_END_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_START_START_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_TOP_END_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_TOP_LEFT_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_TOP_RIGHT_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static final field BORDER_TOP_START_RADIUS Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/BorderRadiusProp;
	public static fun values ()[Lcom/facebook/react/uimanager/style/BorderRadiusProp;
}

public final class com/facebook/react/uimanager/style/BorderStyle : java/lang/Enum {
	public static final field Companion Lcom/facebook/react/uimanager/style/BorderStyle$Companion;
	public static final field DASHED Lcom/facebook/react/uimanager/style/BorderStyle;
	public static final field DOTTED Lcom/facebook/react/uimanager/style/BorderStyle;
	public static final field SOLID Lcom/facebook/react/uimanager/style/BorderStyle;
	public static final fun fromString (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/BorderStyle;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/BorderStyle;
	public static fun values ()[Lcom/facebook/react/uimanager/style/BorderStyle;
}

public final class com/facebook/react/uimanager/style/BorderStyle$Companion {
	public final fun fromString (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/BorderStyle;
}

public final class com/facebook/react/uimanager/style/BoxShadow {
	public static final field Companion Lcom/facebook/react/uimanager/style/BoxShadow$Companion;
	public fun <init> (FFLjava/lang/Integer;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Boolean;)V
	public synthetic fun <init> (FFLjava/lang/Integer;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Boolean;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public final fun component1 ()F
	public final fun component2 ()F
	public final fun component3 ()Ljava/lang/Integer;
	public final fun component4 ()Ljava/lang/Float;
	public final fun component5 ()Ljava/lang/Float;
	public final fun component6 ()Ljava/lang/Boolean;
	public final fun copy (FFLjava/lang/Integer;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Boolean;)Lcom/facebook/react/uimanager/style/BoxShadow;
	public static synthetic fun copy$default (Lcom/facebook/react/uimanager/style/BoxShadow;FFLjava/lang/Integer;Ljava/lang/Float;Ljava/lang/Float;Ljava/lang/Boolean;ILjava/lang/Object;)Lcom/facebook/react/uimanager/style/BoxShadow;
	public fun equals (Ljava/lang/Object;)Z
	public final fun getBlurRadius ()Ljava/lang/Float;
	public final fun getColor ()Ljava/lang/Integer;
	public final fun getInset ()Ljava/lang/Boolean;
	public final fun getOffsetX ()F
	public final fun getOffsetY ()F
	public final fun getSpreadDistance ()Ljava/lang/Float;
	public fun hashCode ()I
	public static final fun parse (Lcom/facebook/react/bridge/ReadableMap;Landroid/content/Context;)Lcom/facebook/react/uimanager/style/BoxShadow;
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/uimanager/style/BoxShadow$Companion {
	public final fun parse (Lcom/facebook/react/bridge/ReadableMap;Landroid/content/Context;)Lcom/facebook/react/uimanager/style/BoxShadow;
}

public final class com/facebook/react/uimanager/style/ComputedBorderRadius {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;)V
	public final fun component1 ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun component2 ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun component3 ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun component4 ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun copy (Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;)Lcom/facebook/react/uimanager/style/ComputedBorderRadius;
	public static synthetic fun copy$default (Lcom/facebook/react/uimanager/style/ComputedBorderRadius;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;Lcom/facebook/react/uimanager/style/CornerRadii;ILjava/lang/Object;)Lcom/facebook/react/uimanager/style/ComputedBorderRadius;
	public fun equals (Ljava/lang/Object;)Z
	public final fun get (Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;)Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun getBottomLeft ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun getBottomRight ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun getTopLeft ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun getTopRight ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public final fun hasRoundedBorders ()Z
	public fun hashCode ()I
	public final fun isUniform ()Z
	public fun toString ()Ljava/lang/String;
}

public final class com/facebook/react/uimanager/style/ComputedBorderRadiusProp : java/lang/Enum {
	public static final field COMPUTED_BORDER_BOTTOM_LEFT_RADIUS Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;
	public static final field COMPUTED_BORDER_BOTTOM_RIGHT_RADIUS Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;
	public static final field COMPUTED_BORDER_TOP_LEFT_RADIUS Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;
	public static final field COMPUTED_BORDER_TOP_RIGHT_RADIUS Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;
	public static fun values ()[Lcom/facebook/react/uimanager/style/ComputedBorderRadiusProp;
}

public final class com/facebook/react/uimanager/style/CornerRadii {
	public fun <init> ()V
	public fun <init> (FF)V
	public synthetic fun <init> (FFILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public final fun component1 ()F
	public final fun component2 ()F
	public final fun copy (FF)Lcom/facebook/react/uimanager/style/CornerRadii;
	public static synthetic fun copy$default (Lcom/facebook/react/uimanager/style/CornerRadii;FFILjava/lang/Object;)Lcom/facebook/react/uimanager/style/CornerRadii;
	public fun equals (Ljava/lang/Object;)Z
	public final fun getHorizontal ()F
	public final fun getVertical ()F
	public fun hashCode ()I
	public final fun toPixelFromDIP ()Lcom/facebook/react/uimanager/style/CornerRadii;
	public fun toString ()Ljava/lang/String;
}

public abstract class com/facebook/react/uimanager/style/LogicalEdge : java/lang/Enum {
	public static final field ALL Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field BLOCK Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field BLOCK_END Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field BLOCK_START Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field BOTTOM Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field Companion Lcom/facebook/react/uimanager/style/LogicalEdge$Companion;
	public static final field END Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field HORIZONTAL Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field LEFT Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field RIGHT Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field START Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field TOP Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static final field VERTICAL Lcom/facebook/react/uimanager/style/LogicalEdge;
	public synthetic fun <init> (Ljava/lang/String;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun fromSpacingType (I)Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public abstract fun toSpacingType ()I
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/LogicalEdge;
	public static fun values ()[Lcom/facebook/react/uimanager/style/LogicalEdge;
}

public final class com/facebook/react/uimanager/style/LogicalEdge$Companion {
	public final fun fromSpacingType (I)Lcom/facebook/react/uimanager/style/LogicalEdge;
}

public final class com/facebook/react/uimanager/style/OutlineStyle : java/lang/Enum {
	public static final field Companion Lcom/facebook/react/uimanager/style/OutlineStyle$Companion;
	public static final field DASHED Lcom/facebook/react/uimanager/style/OutlineStyle;
	public static final field DOTTED Lcom/facebook/react/uimanager/style/OutlineStyle;
	public static final field SOLID Lcom/facebook/react/uimanager/style/OutlineStyle;
	public static final fun fromString (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/OutlineStyle;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/OutlineStyle;
	public static fun values ()[Lcom/facebook/react/uimanager/style/OutlineStyle;
}

public final class com/facebook/react/uimanager/style/OutlineStyle$Companion {
	public final fun fromString (Ljava/lang/String;)Lcom/facebook/react/uimanager/style/OutlineStyle;
}

public final class com/facebook/react/uimanager/util/ReactFindViewUtil {
	public static final field INSTANCE Lcom/facebook/react/uimanager/util/ReactFindViewUtil;
	public static final fun addViewListener (Lcom/facebook/react/uimanager/util/ReactFindViewUtil$OnViewFoundListener;)V
	public static final fun addViewsListener (Lcom/facebook/react/uimanager/util/ReactFindViewUtil$OnMultipleViewsFoundListener;Ljava/util/Set;)V
	public static final fun findView (Landroid/view/View;Lcom/facebook/react/uimanager/util/ReactFindViewUtil$OnViewFoundListener;)V
	public static final fun findView (Landroid/view/View;Ljava/lang/String;)Landroid/view/View;
	public static final fun notifyViewRendered (Landroid/view/View;)V
	public static final fun removeViewListener (Lcom/facebook/react/uimanager/util/ReactFindViewUtil$OnViewFoundListener;)V
	public static final fun removeViewsListener (Lcom/facebook/react/uimanager/util/ReactFindViewUtil$OnMultipleViewsFoundListener;)V
}

public abstract interface class com/facebook/react/uimanager/util/ReactFindViewUtil$OnMultipleViewsFoundListener {
	public abstract fun onViewFound (Landroid/view/View;Ljava/lang/String;)V
}

public abstract interface class com/facebook/react/uimanager/util/ReactFindViewUtil$OnViewFoundListener {
	public abstract fun getNativeId ()Ljava/lang/String;
	public abstract fun onViewFound (Landroid/view/View;)V
}

public final class com/facebook/react/util/JSStackTrace {
	public static final field COLUMN_KEY Ljava/lang/String;
	public static final field FILE_KEY Ljava/lang/String;
	public static final field INSTANCE Lcom/facebook/react/util/JSStackTrace;
	public static final field LINE_NUMBER_KEY Ljava/lang/String;
	public static final field METHOD_NAME_KEY Ljava/lang/String;
	public static final fun format (Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)Ljava/lang/String;
}

public final class com/facebook/react/util/RNLog {
	public static final field ADVICE I
	public static final field ERROR I
	public static final field INSTANCE Lcom/facebook/react/util/RNLog;
	public static final field LOG I
	public static final field MINIMUM_LEVEL_FOR_UI I
	public static final field TRACE I
	public static final field WARN I
	public static final fun a (Ljava/lang/String;)V
	public static final fun e (Lcom/facebook/react/bridge/ReactContext;Ljava/lang/String;)V
	public static final fun e (Ljava/lang/String;)V
	public static final fun l (Ljava/lang/String;)V
	public static final fun t (Ljava/lang/String;)V
	public static final fun w (Lcom/facebook/react/bridge/ReactContext;Ljava/lang/String;)V
}

public class com/facebook/react/viewmanagers/ActivityIndicatorViewManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/ActivityIndicatorViewManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setAnimating (Landroid/view/View;Z)V
	public abstract fun setColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setHidesWhenStopped (Landroid/view/View;Z)V
	public abstract fun setSize (Landroid/view/View;Ljava/lang/String;)V
}

public class com/facebook/react/viewmanagers/AndroidDrawerLayoutManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/AndroidDrawerLayoutManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun closeDrawer (Landroid/view/View;)V
	public abstract fun openDrawer (Landroid/view/View;)V
	public abstract fun setDrawerBackgroundColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setDrawerLockMode (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setDrawerPosition (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setDrawerWidth (Landroid/view/View;Ljava/lang/Float;)V
	public abstract fun setKeyboardDismissMode (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setStatusBarBackgroundColor (Landroid/view/View;Ljava/lang/Integer;)V
}

public class com/facebook/react/viewmanagers/AndroidHorizontalScrollContentViewManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/AndroidHorizontalScrollContentViewManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setRemoveClippedSubviews (Landroid/view/View;Z)V
}

public class com/facebook/react/viewmanagers/AndroidProgressBarManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/AndroidProgressBarManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setAnimating (Landroid/view/View;Z)V
	public abstract fun setColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setIndeterminate (Landroid/view/View;Z)V
	public abstract fun setProgress (Landroid/view/View;D)V
	public abstract fun setStyleAttr (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setTestID (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setTypeAttr (Landroid/view/View;Ljava/lang/String;)V
}

public class com/facebook/react/viewmanagers/AndroidSwipeRefreshLayoutManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/AndroidSwipeRefreshLayoutManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setColors (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun setEnabled (Landroid/view/View;Z)V
	public abstract fun setNativeRefreshing (Landroid/view/View;Z)V
	public abstract fun setProgressBackgroundColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setProgressViewOffset (Landroid/view/View;F)V
	public abstract fun setRefreshing (Landroid/view/View;Z)V
	public abstract fun setSize (Landroid/view/View;Ljava/lang/String;)V
}

public class com/facebook/react/viewmanagers/AndroidSwitchManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/AndroidSwitchManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setDisabled (Landroid/view/View;Z)V
	public abstract fun setEnabled (Landroid/view/View;Z)V
	public abstract fun setNativeValue (Landroid/view/View;Z)V
	public abstract fun setOn (Landroid/view/View;Z)V
	public abstract fun setThumbColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setThumbTintColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setTrackColorForFalse (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setTrackColorForTrue (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setTrackTintColor (Landroid/view/View;Ljava/lang/Integer;)V
	public abstract fun setValue (Landroid/view/View;Z)V
}

public class com/facebook/react/viewmanagers/DebuggingOverlayManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/DebuggingOverlayManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun clearElementsHighlights (Landroid/view/View;)V
	public abstract fun highlightElements (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun highlightTraceUpdates (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
}

public class com/facebook/react/viewmanagers/ModalHostViewManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/ModalHostViewManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setAnimated (Landroid/view/View;Z)V
	public abstract fun setAnimationType (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setHardwareAccelerated (Landroid/view/View;Z)V
	public abstract fun setIdentifier (Landroid/view/View;I)V
	public abstract fun setNavigationBarTranslucent (Landroid/view/View;Z)V
	public abstract fun setPresentationStyle (Landroid/view/View;Ljava/lang/String;)V
	public abstract fun setStatusBarTranslucent (Landroid/view/View;Z)V
	public abstract fun setSupportedOrientations (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public abstract fun setTransparent (Landroid/view/View;Z)V
	public abstract fun setVisible (Landroid/view/View;Z)V
}

public class com/facebook/react/viewmanagers/SafeAreaViewManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/SafeAreaViewManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
}

public class com/facebook/react/viewmanagers/UnimplementedNativeViewManagerDelegate : com/facebook/react/uimanager/BaseViewManagerDelegate {
	public fun <init> (Lcom/facebook/react/uimanager/BaseViewManager;)V
	public fun setProperty (Landroid/view/View;Ljava/lang/String;Ljava/lang/Object;)V
}

public abstract interface class com/facebook/react/viewmanagers/UnimplementedNativeViewManagerInterface : com/facebook/react/uimanager/ViewManagerWithGeneratedInterface {
	public abstract fun setName (Landroid/view/View;Ljava/lang/String;)V
}

public final class com/facebook/react/views/common/ContextUtils {
	public static final field INSTANCE Lcom/facebook/react/views/common/ContextUtils;
	public static final fun findContextOfType (Landroid/content/Context;Ljava/lang/Class;)Ljava/lang/Object;
}

public final class com/facebook/react/views/drawer/ReactDrawerLayout : androidx/drawerlayout/widget/DrawerLayout {
	public fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
}

public final class com/facebook/react/views/drawer/ReactDrawerLayoutManager : com/facebook/react/uimanager/ViewGroupManager, com/facebook/react/viewmanagers/AndroidDrawerLayoutManagerInterface {
	public static final field CLOSE_DRAWER I
	public static final field COMMAND_CLOSE_DRAWER Ljava/lang/String;
	public static final field COMMAND_OPEN_DRAWER Ljava/lang/String;
	public static final field Companion Lcom/facebook/react/views/drawer/ReactDrawerLayoutManager$Companion;
	public static final field OPEN_DRAWER I
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public synthetic fun addEventEmitters (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)V
	public synthetic fun addView (Landroid/view/View;Landroid/view/View;I)V
	public synthetic fun addView (Landroid/view/ViewGroup;Landroid/view/View;I)V
	public fun addView (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Landroid/view/View;I)V
	public synthetic fun closeDrawer (Landroid/view/View;)V
	public fun closeDrawer (Lcom/facebook/react/views/drawer/ReactDrawerLayout;)V
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun getCommandsMap ()Ljava/util/Map;
	public fun getDelegate ()Lcom/facebook/react/uimanager/ViewManagerDelegate;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getExportedViewConstants ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public fun needsCustomLayoutForChildren ()Z
	public synthetic fun openDrawer (Landroid/view/View;)V
	public fun openDrawer (Lcom/facebook/react/views/drawer/ReactDrawerLayout;)V
	public synthetic fun receiveCommand (Landroid/view/View;ILcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/drawer/ReactDrawerLayout;ILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun setDrawerBackgroundColor (Landroid/view/View;Ljava/lang/Integer;)V
	public fun setDrawerBackgroundColor (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/Integer;)V
	public synthetic fun setDrawerLockMode (Landroid/view/View;Ljava/lang/String;)V
	public fun setDrawerLockMode (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/String;)V
	public synthetic fun setDrawerPosition (Landroid/view/View;Ljava/lang/String;)V
	public final fun setDrawerPosition (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Lcom/facebook/react/bridge/Dynamic;)V
	public fun setDrawerPosition (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/String;)V
	public synthetic fun setDrawerWidth (Landroid/view/View;Ljava/lang/Float;)V
	public final fun setDrawerWidth (Lcom/facebook/react/views/drawer/ReactDrawerLayout;F)V
	public fun setDrawerWidth (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/Float;)V
	public synthetic fun setElevation (Landroid/view/View;F)V
	public fun setElevation (Lcom/facebook/react/views/drawer/ReactDrawerLayout;F)V
	public synthetic fun setKeyboardDismissMode (Landroid/view/View;Ljava/lang/String;)V
	public fun setKeyboardDismissMode (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/String;)V
	public synthetic fun setStatusBarBackgroundColor (Landroid/view/View;Ljava/lang/Integer;)V
	public fun setStatusBarBackgroundColor (Lcom/facebook/react/views/drawer/ReactDrawerLayout;Ljava/lang/Integer;)V
}

public final class com/facebook/react/views/drawer/ReactDrawerLayoutManager$Companion {
}

public final class com/facebook/react/views/drawer/events/DrawerClosedEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/views/drawer/events/DrawerClosedEvent$Companion;
	public static final field EVENT_NAME Ljava/lang/String;
	public fun <init> (I)V
	public fun <init> (II)V
	public fun getEventName ()Ljava/lang/String;
}

public final class com/facebook/react/views/drawer/events/DrawerClosedEvent$Companion {
}

public final class com/facebook/react/views/drawer/events/DrawerOpenedEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/views/drawer/events/DrawerOpenedEvent$Companion;
	public static final field EVENT_NAME Ljava/lang/String;
	public fun <init> (I)V
	public fun <init> (II)V
	public fun getEventName ()Ljava/lang/String;
}

public final class com/facebook/react/views/drawer/events/DrawerOpenedEvent$Companion {
}

public final class com/facebook/react/views/drawer/events/DrawerSlideEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/views/drawer/events/DrawerSlideEvent$Companion;
	public static final field EVENT_NAME Ljava/lang/String;
	public fun <init> (IF)V
	public fun <init> (IIF)V
	public fun getEventName ()Ljava/lang/String;
	public final fun getOffset ()F
}

public final class com/facebook/react/views/drawer/events/DrawerSlideEvent$Companion {
}

public final class com/facebook/react/views/drawer/events/DrawerStateChangedEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/views/drawer/events/DrawerStateChangedEvent$Companion;
	public static final field EVENT_NAME Ljava/lang/String;
	public fun <init> (II)V
	public fun <init> (III)V
	public final fun getDrawerState ()I
	public fun getEventName ()Ljava/lang/String;
}

public final class com/facebook/react/views/drawer/events/DrawerStateChangedEvent$Companion {
}

public abstract interface class com/facebook/react/views/image/GlobalImageLoadListener {
	public abstract fun onLoadAttempt (Landroid/net/Uri;)V
}

public final class com/facebook/react/views/image/ImageLoadEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/views/image/ImageLoadEvent$Companion;
	public static final field ON_ERROR I
	public static final field ON_LOAD I
	public static final field ON_LOAD_END I
	public static final field ON_LOAD_START I
	public static final field ON_PROGRESS I
	public synthetic fun <init> (IIILjava/lang/String;Ljava/lang/String;IIIILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public static final fun createErrorEvent (IILjava/lang/Throwable;)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createErrorEvent (ILjava/lang/Throwable;)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createLoadEndEvent (I)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createLoadEndEvent (II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createLoadEvent (IILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createLoadEvent (ILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createLoadStartEvent (I)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createLoadStartEvent (II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createProgressEvent (IILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun createProgressEvent (ILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public static final fun eventNameForType (I)Ljava/lang/String;
	public fun getCoalescingKey ()S
	public fun getEventName ()Ljava/lang/String;
}

public final class com/facebook/react/views/image/ImageLoadEvent$Companion {
	public final fun createErrorEvent (IILjava/lang/Throwable;)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createErrorEvent (ILjava/lang/Throwable;)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createLoadEndEvent (I)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createLoadEndEvent (II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createLoadEvent (IILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createLoadEvent (ILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createLoadStartEvent (I)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createLoadStartEvent (II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createProgressEvent (IILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun createProgressEvent (ILjava/lang/String;II)Lcom/facebook/react/views/image/ImageLoadEvent;
	public final fun eventNameForType (I)Ljava/lang/String;
}

public final class com/facebook/react/views/image/ImageResizeMethod : java/lang/Enum {
	public static final field AUTO Lcom/facebook/react/views/image/ImageResizeMethod;
	public static final field NONE Lcom/facebook/react/views/image/ImageResizeMethod;
	public static final field RESIZE Lcom/facebook/react/views/image/ImageResizeMethod;
	public static final field SCALE Lcom/facebook/react/views/image/ImageResizeMethod;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/views/image/ImageResizeMethod;
	public static fun values ()[Lcom/facebook/react/views/image/ImageResizeMethod;
}

public final class com/facebook/react/views/image/ImageResizeMode {
	public static final field INSTANCE Lcom/facebook/react/views/image/ImageResizeMode;
	public static final fun defaultTileMode ()Landroid/graphics/Shader$TileMode;
	public static final fun defaultValue ()Lcom/facebook/drawee/drawable/ScalingUtils$ScaleType;
	public static final fun toScaleType (Ljava/lang/String;)Lcom/facebook/drawee/drawable/ScalingUtils$ScaleType;
	public static final fun toTileMode (Ljava/lang/String;)Landroid/graphics/Shader$TileMode;
}

public abstract interface class com/facebook/react/views/image/ReactCallerContextFactory {
	public abstract fun getOrCreateCallerContext (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/Object;
}

public final class com/facebook/react/views/image/ReactImageManager : com/facebook/react/uimanager/SimpleViewManager {
	public static final field Companion Lcom/facebook/react/views/image/ReactImageManager$Companion;
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public fun <init> (Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;)V
	public fun <init> (Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;Lcom/facebook/react/views/image/GlobalImageLoadListener;)V
	public fun <init> (Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;Lcom/facebook/react/views/image/GlobalImageLoadListener;Lcom/facebook/react/views/image/ReactCallerContextFactory;)V
	public synthetic fun <init> (Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;Lcom/facebook/react/views/image/GlobalImageLoadListener;Lcom/facebook/react/views/image/ReactCallerContextFactory;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun <init> (Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;Lcom/facebook/react/views/image/GlobalImageLoadListener;Ljava/lang/Object;)V
	public fun <init> (Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;Ljava/lang/Object;)V
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/image/ReactImageView;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public synthetic fun onAfterUpdateTransaction (Landroid/view/View;)V
	public final fun setAccessible (Lcom/facebook/react/views/image/ReactImageView;Z)V
	public final fun setBlurRadius (Lcom/facebook/react/views/image/ReactImageView;F)V
	public final fun setBorderColor (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/Integer;)V
	public final fun setBorderRadius (Lcom/facebook/react/views/image/ReactImageView;IF)V
	public final fun setBorderWidth (Lcom/facebook/react/views/image/ReactImageView;F)V
	public final fun setDefaultSource (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/String;)V
	public final fun setFadeDuration (Lcom/facebook/react/views/image/ReactImageView;I)V
	public final fun setHeaders (Lcom/facebook/react/views/image/ReactImageView;Lcom/facebook/react/bridge/ReadableMap;)V
	public final fun setInternal_AnalyticsTag (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/String;)V
	public final fun setLoadHandlersRegistered (Lcom/facebook/react/views/image/ReactImageView;Z)V
	public final fun setLoadingIndicatorSource (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/String;)V
	public final fun setOverlayColor (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/Integer;)V
	public final fun setProgressiveRenderingEnabled (Lcom/facebook/react/views/image/ReactImageView;Z)V
	public final fun setResizeMethod (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/String;)V
	public final fun setResizeMode (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/String;)V
	public final fun setResizeMultiplier (Lcom/facebook/react/views/image/ReactImageView;F)V
	public final fun setSource (Lcom/facebook/react/views/image/ReactImageView;Lcom/facebook/react/bridge/ReadableArray;)V
	public final fun setSrc (Lcom/facebook/react/views/image/ReactImageView;Lcom/facebook/react/bridge/ReadableArray;)V
	public final fun setTintColor (Lcom/facebook/react/views/image/ReactImageView;Ljava/lang/Integer;)V
}

public final class com/facebook/react/views/image/ReactImageManager$Companion {
}

public final class com/facebook/react/views/image/ReactImageView : com/facebook/drawee/view/GenericDraweeView {
	public static final field Companion Lcom/facebook/react/views/image/ReactImageView$Companion;
	public static final field REMOTE_IMAGE_FADE_DURATION_MS I
	public fun <init> (Landroid/content/Context;Lcom/facebook/drawee/controller/AbstractDraweeControllerBuilder;Lcom/facebook/react/views/image/GlobalImageLoadListener;Ljava/lang/Object;)V
	public fun hasOverlappingRendering ()Z
	public final fun maybeUpdateView ()V
	public fun onDraw (Landroid/graphics/Canvas;)V
	public fun setBackgroundColor (I)V
	public final fun setBlurRadius (F)V
	public final fun setBorderColor (I)V
	public final fun setBorderRadius (F)V
	public final fun setBorderRadius (FI)V
	public final fun setBorderWidth (F)V
	public final fun setDefaultSource (Ljava/lang/String;)V
	public final fun setFadeDuration (I)V
	public final fun setHeaders (Lcom/facebook/react/bridge/ReadableMap;)V
	public final fun setLoadingIndicatorSource (Ljava/lang/String;)V
	public final fun setOverlayColor (I)V
	public final fun setProgressiveRenderingEnabled (Z)V
	public final fun setResizeMethod (Lcom/facebook/react/views/image/ImageResizeMethod;)V
	public final fun setResizeMultiplier (F)V
	public final fun setScaleType (Lcom/facebook/drawee/drawable/ScalingUtils$ScaleType;)V
	public final fun setShouldNotifyLoadEvents (Z)V
	public final fun setSource (Lcom/facebook/react/bridge/ReadableArray;)V
	public final fun setTileMode (Landroid/graphics/Shader$TileMode;)V
	public final fun updateCallerContext (Ljava/lang/Object;)V
}

public final class com/facebook/react/views/image/ReactImageView$Companion {
}

public class com/facebook/react/views/imagehelper/ImageSource {
	public static final field Companion Lcom/facebook/react/views/imagehelper/ImageSource$Companion;
	public fun <init> (Landroid/content/Context;Ljava/lang/String;)V
	public fun <init> (Landroid/content/Context;Ljava/lang/String;D)V
	public fun <init> (Landroid/content/Context;Ljava/lang/String;DD)V
	public fun <init> (Landroid/content/Context;Ljava/lang/String;DDLcom/facebook/react/modules/fresco/ImageCacheControl;)V
	public synthetic fun <init> (Landroid/content/Context;Ljava/lang/String;DDLcom/facebook/react/modules/fresco/ImageCacheControl;ILkotlin/jvm/internal/DefaultConstructorMarker;)V
	public fun equals (Ljava/lang/Object;)Z
	public final fun getCacheControl ()Lcom/facebook/react/modules/fresco/ImageCacheControl;
	public final fun getSize ()D
	public final fun getSource ()Ljava/lang/String;
	public static final fun getTransparentBitmapImageSource (Landroid/content/Context;)Lcom/facebook/react/views/imagehelper/ImageSource;
	public fun getUri ()Landroid/net/Uri;
	public fun hashCode ()I
	public fun isResource ()Z
}

public final class com/facebook/react/views/imagehelper/ImageSource$Companion {
	public final fun getTransparentBitmapImageSource (Landroid/content/Context;)Lcom/facebook/react/views/imagehelper/ImageSource;
}

public final class com/facebook/react/views/imagehelper/MultiSourceHelper {
	public static final field INSTANCE Lcom/facebook/react/views/imagehelper/MultiSourceHelper;
	public static final fun getBestSourceForSize (IILjava/util/List;)Lcom/facebook/react/views/imagehelper/MultiSourceHelper$MultiSourceResult;
	public static final fun getBestSourceForSize (IILjava/util/List;D)Lcom/facebook/react/views/imagehelper/MultiSourceHelper$MultiSourceResult;
}

public final class com/facebook/react/views/imagehelper/MultiSourceHelper$MultiSourceResult {
	public final field bestResult Lcom/facebook/react/views/imagehelper/ImageSource;
	public final field bestResultInCache Lcom/facebook/react/views/imagehelper/ImageSource;
	public fun <init> (Lcom/facebook/react/views/imagehelper/ImageSource;Lcom/facebook/react/views/imagehelper/ImageSource;)V
}

public final class com/facebook/react/views/imagehelper/ResourceDrawableIdHelper {
	public static final field Companion Lcom/facebook/react/views/imagehelper/ResourceDrawableIdHelper$Companion;
	public final fun clear ()V
	public static final fun getInstance ()Lcom/facebook/react/views/imagehelper/ResourceDrawableIdHelper;
	public final fun getResourceDrawable (Landroid/content/Context;Ljava/lang/String;)Landroid/graphics/drawable/Drawable;
	public final fun getResourceDrawableId (Landroid/content/Context;Ljava/lang/String;)I
	public final fun getResourceDrawableUri (Landroid/content/Context;Ljava/lang/String;)Landroid/net/Uri;
}

public final class com/facebook/react/views/imagehelper/ResourceDrawableIdHelper$Companion {
	public final fun DEPRECATED$getInstance ()Lcom/facebook/react/views/imagehelper/ResourceDrawableIdHelper;
	public final fun getInstance ()Lcom/facebook/react/views/imagehelper/ResourceDrawableIdHelper;
}

public final class com/facebook/react/views/modal/ReactModalHostManager : com/facebook/react/uimanager/ViewGroupManager, com/facebook/react/viewmanagers/ModalHostViewManagerInterface {
	public static final field Companion Lcom/facebook/react/views/modal/ReactModalHostManager$Companion;
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public synthetic fun addEventEmitters (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)V
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun getDelegate ()Lcom/facebook/react/uimanager/ViewManagerDelegate;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public synthetic fun onAfterUpdateTransaction (Landroid/view/View;)V
	public synthetic fun onDropViewInstance (Landroid/view/View;)V
	public fun onDropViewInstance (Lcom/facebook/react/views/modal/ReactModalHostView;)V
	public synthetic fun setAnimated (Landroid/view/View;Z)V
	public fun setAnimated (Lcom/facebook/react/views/modal/ReactModalHostView;Z)V
	public synthetic fun setAnimationType (Landroid/view/View;Ljava/lang/String;)V
	public fun setAnimationType (Lcom/facebook/react/views/modal/ReactModalHostView;Ljava/lang/String;)V
	public synthetic fun setHardwareAccelerated (Landroid/view/View;Z)V
	public fun setHardwareAccelerated (Lcom/facebook/react/views/modal/ReactModalHostView;Z)V
	public synthetic fun setIdentifier (Landroid/view/View;I)V
	public fun setIdentifier (Lcom/facebook/react/views/modal/ReactModalHostView;I)V
	public synthetic fun setNavigationBarTranslucent (Landroid/view/View;Z)V
	public fun setNavigationBarTranslucent (Lcom/facebook/react/views/modal/ReactModalHostView;Z)V
	public synthetic fun setPresentationStyle (Landroid/view/View;Ljava/lang/String;)V
	public fun setPresentationStyle (Lcom/facebook/react/views/modal/ReactModalHostView;Ljava/lang/String;)V
	public synthetic fun setStatusBarTranslucent (Landroid/view/View;Z)V
	public fun setStatusBarTranslucent (Lcom/facebook/react/views/modal/ReactModalHostView;Z)V
	public synthetic fun setSupportedOrientations (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setSupportedOrientations (Lcom/facebook/react/views/modal/ReactModalHostView;Lcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun setTestId (Landroid/view/View;Ljava/lang/String;)V
	public fun setTestId (Lcom/facebook/react/views/modal/ReactModalHostView;Ljava/lang/String;)V
	public synthetic fun setTransparent (Landroid/view/View;Z)V
	public fun setTransparent (Lcom/facebook/react/views/modal/ReactModalHostView;Z)V
	public synthetic fun setVisible (Landroid/view/View;Z)V
	public fun setVisible (Lcom/facebook/react/views/modal/ReactModalHostView;Z)V
	public synthetic fun updateState (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
	public fun updateState (Lcom/facebook/react/views/modal/ReactModalHostView;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
}

public final class com/facebook/react/views/modal/ReactModalHostManager$Companion {
}

public final class com/facebook/react/views/modal/ReactModalHostView : android/view/ViewGroup, com/facebook/react/bridge/LifecycleEventListener {
	public fun <init> (Lcom/facebook/react/uimanager/ThemedReactContext;)V
	public fun addChildrenForAccessibility (Ljava/util/ArrayList;)V
	public fun addView (Landroid/view/View;I)V
	public fun dispatchPopulateAccessibilityEvent (Landroid/view/accessibility/AccessibilityEvent;)Z
	public fun dispatchProvideStructure (Landroid/view/ViewStructure;)V
	public final fun getAnimationType ()Ljava/lang/String;
	public fun getChildAt (I)Landroid/view/View;
	public fun getChildCount ()I
	public final fun getEventDispatcher ()Lcom/facebook/react/uimanager/events/EventDispatcher;
	public final fun getHardwareAccelerated ()Z
	public final fun getNavigationBarTranslucent ()Z
	public final fun getOnRequestCloseListener ()Lcom/facebook/react/views/modal/ReactModalHostView$OnRequestCloseListener;
	public final fun getOnShowListener ()Landroid/content/DialogInterface$OnShowListener;
	public final fun getStateWrapper ()Lcom/facebook/react/uimanager/StateWrapper;
	public final fun getStatusBarTranslucent ()Z
	public final fun getTransparent ()Z
	public final fun onDropInstance ()V
	public fun onHostDestroy ()V
	public fun onHostPause ()V
	public fun onHostResume ()V
	public fun removeView (Landroid/view/View;)V
	public fun removeViewAt (I)V
	public final fun setAnimationType (Ljava/lang/String;)V
	public final fun setDialogRootViewGroupTestId (Ljava/lang/String;)V
	public final fun setEventDispatcher (Lcom/facebook/react/uimanager/events/EventDispatcher;)V
	public final fun setHardwareAccelerated (Z)V
	public fun setId (I)V
	public final fun setNavigationBarTranslucent (Z)V
	public final fun setOnRequestCloseListener (Lcom/facebook/react/views/modal/ReactModalHostView$OnRequestCloseListener;)V
	public final fun setOnShowListener (Landroid/content/DialogInterface$OnShowListener;)V
	public final fun setStateWrapper (Lcom/facebook/react/uimanager/StateWrapper;)V
	public final fun setStatusBarTranslucent (Z)V
	public final fun setTransparent (Z)V
	public final fun showOrUpdate ()V
}

public final class com/facebook/react/views/modal/ReactModalHostView$DialogRootViewGroup : com/facebook/react/views/view/ReactViewGroup, com/facebook/react/uimanager/RootView {
	public fun handleException (Ljava/lang/Throwable;)V
	public fun onChildEndedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	public fun onChildStartedNativeGesture (Landroid/view/View;Landroid/view/MotionEvent;)V
	public fun onHoverEvent (Landroid/view/MotionEvent;)Z
	public fun onInitializeAccessibilityNodeInfo (Landroid/view/accessibility/AccessibilityNodeInfo;)V
	public fun onInterceptHoverEvent (Landroid/view/MotionEvent;)Z
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun requestDisallowInterceptTouchEvent (Z)V
	public final fun updateState (II)V
}

public abstract interface class com/facebook/react/views/modal/ReactModalHostView$OnRequestCloseListener {
	public abstract fun onRequestClose (Landroid/content/DialogInterface;)V
}

public abstract interface class com/facebook/react/views/scroll/FpsListener {
	public abstract fun disable (Ljava/lang/String;)V
	public abstract fun enable (Ljava/lang/String;)V
	public abstract fun isEnabled ()Z
}

public final class com/facebook/react/views/scroll/OnScrollDispatchHelper {
	public fun <init> ()V
	public final fun getXFlingVelocity ()F
	public final fun getYFlingVelocity ()F
	public final fun onScrollChanged (II)Z
}

public final class com/facebook/react/views/scroll/ReactHorizontalScrollContainerViewManager : com/facebook/react/views/view/ReactViewManager {
	public static final field Companion Lcom/facebook/react/views/scroll/ReactHorizontalScrollContainerViewManager$Companion;
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public synthetic fun createViewInstance (ILcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Landroid/view/View;
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/view/ReactViewGroup;
	public fun getName ()Ljava/lang/String;
}

public final class com/facebook/react/views/scroll/ReactHorizontalScrollContainerViewManager$Companion {
}

public class com/facebook/react/views/scroll/ReactHorizontalScrollView : android/widget/HorizontalScrollView, android/view/View$OnLayoutChangeListener, android/view/ViewGroup$OnHierarchyChangeListener, com/facebook/react/uimanager/ReactClippingViewGroup, com/facebook/react/uimanager/ReactOverflowViewWithInset, com/facebook/react/views/scroll/ReactAccessibleScrollView, com/facebook/react/views/scroll/ReactScrollViewHelper$HasFlingAnimator, com/facebook/react/views/scroll/ReactScrollViewHelper$HasScrollEventThrottle, com/facebook/react/views/scroll/ReactScrollViewHelper$HasScrollState, com/facebook/react/views/scroll/ReactScrollViewHelper$HasSmoothScroll, com/facebook/react/views/scroll/ReactScrollViewHelper$HasStateWrapper {
	public fun <init> (Landroid/content/Context;)V
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/views/scroll/FpsListener;)V
	public fun abortAnimation ()V
	public fun addFocusables (Ljava/util/ArrayList;II)V
	public fun arrowScroll (I)Z
	public fun canScrollHorizontally (I)Z
	public fun dispatchGenericMotionEvent (Landroid/view/MotionEvent;)Z
	public fun draw (Landroid/graphics/Canvas;)V
	public fun executeKeyEvent (Landroid/view/KeyEvent;)Z
	public fun flashScrollIndicators ()V
	public fun fling (I)V
	public fun getChildVisibleRect (Landroid/view/View;Landroid/graphics/Rect;Landroid/graphics/Point;)Z
	public fun getClippingRect (Landroid/graphics/Rect;)V
	public fun getFlingAnimator ()Landroid/animation/ValueAnimator;
	public fun getFlingExtrapolatedDistance (I)I
	public fun getLastScrollDispatchTime ()J
	public fun getOverflow ()Ljava/lang/String;
	public fun getOverflowInset ()Landroid/graphics/Rect;
	public fun getPointerEvents ()Lcom/facebook/react/uimanager/PointerEvents;
	public fun getReactScrollViewScrollState ()Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ReactScrollViewScrollState;
	public fun getRemoveClippedSubviews ()Z
	public fun getScrollEnabled ()Z
	public fun getScrollEventThrottle ()I
	public fun getStateWrapper ()Lcom/facebook/react/uimanager/StateWrapper;
	protected fun handleInterceptedTouchEvent (Landroid/view/MotionEvent;)V
	public fun isPartiallyScrolledInView (Landroid/view/View;)Z
	protected fun onAttachedToWindow ()V
	public fun onChildViewAdded (Landroid/view/View;Landroid/view/View;)V
	public fun onChildViewRemoved (Landroid/view/View;Landroid/view/View;)V
	protected fun onDetachedFromWindow ()V
	public fun onDraw (Landroid/graphics/Canvas;)V
	public fun onInitializeAccessibilityNodeInfo (Landroid/view/accessibility/AccessibilityNodeInfo;)V
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	protected fun onLayout (ZIIII)V
	public fun onLayoutChange (Landroid/view/View;IIIIIIII)V
	protected fun onMeasure (II)V
	protected fun onOverScrolled (IIZZ)V
	protected fun onScrollChanged (IIII)V
	protected fun onSizeChanged (IIII)V
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun pageScroll (I)Z
	public fun reactSmoothScrollTo (II)V
	public fun requestChildFocus (Landroid/view/View;Landroid/view/View;)V
	public fun scrollTo (II)V
	public fun scrollToPreservingMomentum (II)V
	public fun setBackgroundColor (I)V
	public fun setBorderColor (ILjava/lang/Integer;)V
	public fun setBorderRadius (F)V
	public fun setBorderRadius (FI)V
	public fun setBorderStyle (Ljava/lang/String;)V
	public fun setBorderWidth (IF)V
	public fun setDecelerationRate (F)V
	public fun setDisableIntervalMomentum (Z)V
	public fun setEndFillColor (I)V
	public fun setLastScrollDispatchTime (J)V
	public fun setMaintainVisibleContentPosition (Lcom/facebook/react/views/scroll/MaintainVisibleScrollPositionHelper$Config;)V
	public fun setOverflow (Ljava/lang/String;)V
	public fun setOverflowInset (IIII)V
	public fun setPagingEnabled (Z)V
	public fun setPointerEvents (Lcom/facebook/react/uimanager/PointerEvents;)V
	public fun setRemoveClippedSubviews (Z)V
	public fun setScrollEnabled (Z)V
	public fun setScrollEventThrottle (I)V
	public fun setScrollPerfTag (Ljava/lang/String;)V
	public fun setSendMomentumEvents (Z)V
	public fun setSnapInterval (I)V
	public fun setSnapOffsets (Ljava/util/List;)V
	public fun setSnapToAlignment (I)V
	public fun setSnapToEnd (Z)V
	public fun setSnapToStart (Z)V
	public fun setStateWrapper (Lcom/facebook/react/uimanager/StateWrapper;)V
	public fun startFlingAnimator (II)V
	public fun updateClippingRect ()V
}

public class com/facebook/react/views/scroll/ReactHorizontalScrollViewManager : com/facebook/react/uimanager/ViewGroupManager, com/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler {
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/views/scroll/FpsListener;)V
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;
	public fun flashScrollIndicators (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;)V
	public synthetic fun flashScrollIndicators (Ljava/lang/Object;)V
	public fun getName ()Ljava/lang/String;
	public synthetic fun receiveCommand (Landroid/view/View;ILcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;ILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun scrollTo (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToCommandData;)V
	public synthetic fun scrollTo (Ljava/lang/Object;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToCommandData;)V
	public fun scrollToEnd (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToEndCommandData;)V
	public synthetic fun scrollToEnd (Ljava/lang/Object;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToEndCommandData;)V
	public fun setBorderColor (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;ILjava/lang/Integer;)V
	public fun setBorderRadius (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;IF)V
	public fun setBorderStyle (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;)V
	public fun setBorderWidth (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;IF)V
	public fun setBottomFillColor (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;I)V
	public fun setContentOffset (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setDecelerationRate (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;F)V
	public fun setDisableIntervalMomentum (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setFadingEdgeLength (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;I)V
	public fun setHorizontal (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setMaintainVisibleContentPosition (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setNestedScrollEnabled (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setOverScrollMode (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;)V
	public fun setOverflow (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;)V
	public fun setPagingEnabled (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setPersistentScrollbar (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setPointerEvents (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;)V
	public fun setRemoveClippedSubviews (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setScrollEnabled (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setScrollEventThrottle (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;I)V
	public fun setScrollPerfTag (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;)V
	public fun setSendMomentumEvents (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setShowsHorizontalScrollIndicator (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setSnapToAlignment (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Ljava/lang/String;)V
	public fun setSnapToEnd (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public fun setSnapToInterval (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;F)V
	public fun setSnapToOffsets (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setSnapToStart (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Z)V
	public synthetic fun updateState (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
	public fun updateState (Lcom/facebook/react/views/scroll/ReactHorizontalScrollView;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
}

public class com/facebook/react/views/scroll/ReactScrollView : android/widget/ScrollView, android/view/View$OnLayoutChangeListener, android/view/ViewGroup$OnHierarchyChangeListener, com/facebook/react/uimanager/ReactClippingViewGroup, com/facebook/react/uimanager/ReactOverflowViewWithInset, com/facebook/react/views/scroll/ReactAccessibleScrollView, com/facebook/react/views/scroll/ReactScrollViewHelper$HasFlingAnimator, com/facebook/react/views/scroll/ReactScrollViewHelper$HasScrollEventThrottle, com/facebook/react/views/scroll/ReactScrollViewHelper$HasScrollState, com/facebook/react/views/scroll/ReactScrollViewHelper$HasSmoothScroll, com/facebook/react/views/scroll/ReactScrollViewHelper$HasStateWrapper {
	public fun <init> (Landroid/content/Context;)V
	public fun <init> (Landroid/content/Context;Lcom/facebook/react/views/scroll/FpsListener;)V
	public fun abortAnimation ()V
	public fun dispatchGenericMotionEvent (Landroid/view/MotionEvent;)Z
	public fun draw (Landroid/graphics/Canvas;)V
	public fun executeKeyEvent (Landroid/view/KeyEvent;)Z
	public fun flashScrollIndicators ()V
	public fun fling (I)V
	public fun getChildVisibleRect (Landroid/view/View;Landroid/graphics/Rect;Landroid/graphics/Point;)Z
	public fun getClippingRect (Landroid/graphics/Rect;)V
	public fun getFlingAnimator ()Landroid/animation/ValueAnimator;
	public fun getFlingExtrapolatedDistance (I)I
	public fun getLastScrollDispatchTime ()J
	public fun getOverflow ()Ljava/lang/String;
	public fun getOverflowInset ()Landroid/graphics/Rect;
	public fun getPointerEvents ()Lcom/facebook/react/uimanager/PointerEvents;
	public fun getReactScrollViewScrollState ()Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ReactScrollViewScrollState;
	public fun getRemoveClippedSubviews ()Z
	public fun getScrollEnabled ()Z
	public fun getScrollEventThrottle ()I
	public fun getStateWrapper ()Lcom/facebook/react/uimanager/StateWrapper;
	protected fun handleInterceptedTouchEvent (Landroid/view/MotionEvent;)V
	public fun isPartiallyScrolledInView (Landroid/view/View;)Z
	protected fun onAttachedToWindow ()V
	public fun onChildViewAdded (Landroid/view/View;Landroid/view/View;)V
	public fun onChildViewRemoved (Landroid/view/View;Landroid/view/View;)V
	protected fun onDetachedFromWindow ()V
	public fun onDraw (Landroid/graphics/Canvas;)V
	public fun onInitializeAccessibilityNodeInfo (Landroid/view/accessibility/AccessibilityNodeInfo;)V
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	protected fun onLayout (ZIIII)V
	public fun onLayoutChange (Landroid/view/View;IIIIIIII)V
	protected fun onMeasure (II)V
	protected fun onOverScrolled (IIZZ)V
	protected fun onScrollChanged (IIII)V
	protected fun onSizeChanged (IIII)V
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun reactSmoothScrollTo (II)V
	public fun requestChildFocus (Landroid/view/View;Landroid/view/View;)V
	public fun scrollTo (II)V
	public fun scrollToPreservingMomentum (II)V
	public fun setBackgroundColor (I)V
	public fun setBorderColor (ILjava/lang/Integer;)V
	public fun setBorderRadius (F)V
	public fun setBorderRadius (FI)V
	public fun setBorderStyle (Ljava/lang/String;)V
	public fun setBorderWidth (IF)V
	public fun setContentOffset (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setDecelerationRate (F)V
	public fun setDisableIntervalMomentum (Z)V
	public fun setEndFillColor (I)V
	public fun setLastScrollDispatchTime (J)V
	public fun setMaintainVisibleContentPosition (Lcom/facebook/react/views/scroll/MaintainVisibleScrollPositionHelper$Config;)V
	public fun setOverflow (Ljava/lang/String;)V
	public fun setOverflowInset (IIII)V
	public fun setPagingEnabled (Z)V
	public fun setPointerEvents (Lcom/facebook/react/uimanager/PointerEvents;)V
	public fun setRemoveClippedSubviews (Z)V
	public fun setScrollAwayTopPaddingEnabledUnstable (I)V
	public fun setScrollEnabled (Z)V
	public fun setScrollEventThrottle (I)V
	public fun setScrollPerfTag (Ljava/lang/String;)V
	public fun setSendMomentumEvents (Z)V
	public fun setSnapInterval (I)V
	public fun setSnapOffsets (Ljava/util/List;)V
	public fun setSnapToAlignment (I)V
	public fun setSnapToEnd (Z)V
	public fun setSnapToStart (Z)V
	public fun setStateWrapper (Lcom/facebook/react/uimanager/StateWrapper;)V
	public fun startFlingAnimator (II)V
	public fun updateClippingRect ()V
}

public final class com/facebook/react/views/scroll/ReactScrollViewCommandHelper {
	public static final field COMMAND_FLASH_SCROLL_INDICATORS I
	public static final field COMMAND_SCROLL_TO I
	public static final field COMMAND_SCROLL_TO_END I
	public static final field Companion Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$Companion;
	public fun <init> ()V
	public static final fun getCommandsMap ()Ljava/util/Map;
	public static final fun receiveCommand (Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler;Ljava/lang/Object;ILcom/facebook/react/bridge/ReadableArray;)V
	public static final fun receiveCommand (Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler;Ljava/lang/Object;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
}

public final class com/facebook/react/views/scroll/ReactScrollViewCommandHelper$Companion {
	public final fun getCommandsMap ()Ljava/util/Map;
	public final fun receiveCommand (Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler;Ljava/lang/Object;ILcom/facebook/react/bridge/ReadableArray;)V
	public final fun receiveCommand (Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler;Ljava/lang/Object;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler {
	public abstract fun flashScrollIndicators (Ljava/lang/Object;)V
	public abstract fun scrollTo (Ljava/lang/Object;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToCommandData;)V
	public abstract fun scrollToEnd (Ljava/lang/Object;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToEndCommandData;)V
}

public final class com/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToCommandData {
	public final field mAnimated Z
	public final field mDestX I
	public final field mDestY I
	public fun <init> (IIZ)V
}

public final class com/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToEndCommandData {
	public final field mAnimated Z
	public fun <init> (Z)V
}

public final class com/facebook/react/views/scroll/ReactScrollViewHelper {
	public static final field AUTO Ljava/lang/String;
	public static final field INSTANCE Lcom/facebook/react/views/scroll/ReactScrollViewHelper;
	public static final field MOMENTUM_DELAY J
	public static final field OVER_SCROLL_ALWAYS Ljava/lang/String;
	public static final field OVER_SCROLL_NEVER Ljava/lang/String;
	public static final field SNAP_ALIGNMENT_CENTER I
	public static final field SNAP_ALIGNMENT_DISABLED I
	public static final field SNAP_ALIGNMENT_END I
	public static final field SNAP_ALIGNMENT_START I
	public static final fun addLayoutChangeListener (Lcom/facebook/react/views/scroll/ReactScrollViewHelper$LayoutChangeListener;)V
	public static final fun addScrollListener (Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ScrollListener;)V
	public static final fun dispatchMomentumEndOnAnimationEnd (Landroid/view/ViewGroup;)V
	public static final fun emitLayoutChangeEvent (Landroid/view/ViewGroup;)V
	public static final fun emitLayoutEvent (Landroid/view/ViewGroup;)V
	public static final fun emitScrollBeginDragEvent (Landroid/view/ViewGroup;)V
	public static final fun emitScrollEndDragEvent (Landroid/view/ViewGroup;FF)V
	public static final fun emitScrollEvent (Landroid/view/ViewGroup;FF)V
	public static final fun emitScrollMomentumBeginEvent (Landroid/view/ViewGroup;II)V
	public static final fun emitScrollMomentumEndEvent (Landroid/view/ViewGroup;)V
	public static final fun forceUpdateState (Landroid/view/ViewGroup;)V
	public static final fun getDefaultScrollAnimationDuration (Landroid/content/Context;)I
	public static final fun getNextFlingStartValue (Landroid/view/ViewGroup;III)I
	public static final fun parseOverScrollMode (Ljava/lang/String;)I
	public static final fun parseSnapToAlignment (Ljava/lang/String;)I
	public static final fun predictFinalScrollPosition (Landroid/view/ViewGroup;IIII)Landroid/graphics/Point;
	public final fun registerFlingAnimator (Landroid/view/ViewGroup;)V
	public static final fun removeLayoutChangeListener (Lcom/facebook/react/views/scroll/ReactScrollViewHelper$LayoutChangeListener;)V
	public static final fun removeScrollListener (Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ScrollListener;)V
	public static final fun smoothScrollTo (Landroid/view/ViewGroup;II)V
	public static final fun updateFabricScrollState (Landroid/view/ViewGroup;)V
	public final fun updateFabricScrollState (Landroid/view/ViewGroup;II)V
	public static final fun updateStateOnScrollChanged (Landroid/view/ViewGroup;FF)V
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$HasFlingAnimator {
	public abstract fun getFlingAnimator ()Landroid/animation/ValueAnimator;
	public abstract fun getFlingExtrapolatedDistance (I)I
	public abstract fun startFlingAnimator (II)V
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$HasScrollEventThrottle {
	public abstract fun getLastScrollDispatchTime ()J
	public abstract fun getScrollEventThrottle ()I
	public abstract fun setLastScrollDispatchTime (J)V
	public abstract fun setScrollEventThrottle (I)V
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$HasScrollState {
	public abstract fun getReactScrollViewScrollState ()Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ReactScrollViewScrollState;
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$HasSmoothScroll {
	public abstract fun reactSmoothScrollTo (II)V
	public abstract fun scrollToPreservingMomentum (II)V
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$HasStateWrapper {
	public abstract fun getStateWrapper ()Lcom/facebook/react/uimanager/StateWrapper;
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$LayoutChangeListener {
	public abstract fun onLayoutChange (Landroid/view/ViewGroup;)V
}

public final class com/facebook/react/views/scroll/ReactScrollViewHelper$ReactScrollViewScrollState {
	public fun <init> ()V
	public final fun getDecelerationRate ()F
	public final fun getFinalAnimatedPositionScroll ()Landroid/graphics/Point;
	public final fun getLastStateUpdateScroll ()Landroid/graphics/Point;
	public final fun getScrollAwayPaddingTop ()I
	public final fun isCanceled ()Z
	public final fun isFinished ()Z
	public final fun setCanceled (Z)V
	public final fun setDecelerationRate (F)V
	public final fun setFinalAnimatedPositionScroll (II)Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ReactScrollViewScrollState;
	public final fun setFinished (Z)V
	public final fun setLastStateUpdateScroll (II)Lcom/facebook/react/views/scroll/ReactScrollViewHelper$ReactScrollViewScrollState;
	public final fun setScrollAwayPaddingTop (I)V
}

public abstract interface class com/facebook/react/views/scroll/ReactScrollViewHelper$ScrollListener {
	public abstract fun onLayout (Landroid/view/ViewGroup;)V
	public abstract fun onScroll (Landroid/view/ViewGroup;Lcom/facebook/react/views/scroll/ScrollEventType;FF)V
}

public class com/facebook/react/views/scroll/ReactScrollViewManager : com/facebook/react/uimanager/ViewGroupManager, com/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollCommandHandler {
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/views/scroll/FpsListener;)V
	public static fun createExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/scroll/ReactScrollView;
	public fun flashScrollIndicators (Lcom/facebook/react/views/scroll/ReactScrollView;)V
	public synthetic fun flashScrollIndicators (Ljava/lang/Object;)V
	public fun getCommandsMap ()Ljava/util/Map;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public synthetic fun receiveCommand (Landroid/view/View;ILcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/scroll/ReactScrollView;ILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun scrollTo (Lcom/facebook/react/views/scroll/ReactScrollView;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToCommandData;)V
	public synthetic fun scrollTo (Ljava/lang/Object;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToCommandData;)V
	public fun scrollToEnd (Lcom/facebook/react/views/scroll/ReactScrollView;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToEndCommandData;)V
	public synthetic fun scrollToEnd (Ljava/lang/Object;Lcom/facebook/react/views/scroll/ReactScrollViewCommandHelper$ScrollToEndCommandData;)V
	public fun setBorderColor (Lcom/facebook/react/views/scroll/ReactScrollView;ILjava/lang/Integer;)V
	public fun setBorderRadius (Lcom/facebook/react/views/scroll/ReactScrollView;IF)V
	public fun setBorderStyle (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;)V
	public fun setBorderWidth (Lcom/facebook/react/views/scroll/ReactScrollView;IF)V
	public fun setBottomFillColor (Lcom/facebook/react/views/scroll/ReactScrollView;I)V
	public fun setContentOffset (Lcom/facebook/react/views/scroll/ReactScrollView;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setDecelerationRate (Lcom/facebook/react/views/scroll/ReactScrollView;F)V
	public fun setDisableIntervalMomentum (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setFadingEdgeLength (Lcom/facebook/react/views/scroll/ReactScrollView;I)V
	public fun setHorizontal (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setIsInvertedVirtualizedList (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setMaintainVisibleContentPosition (Lcom/facebook/react/views/scroll/ReactScrollView;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setNestedScrollEnabled (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setOverScrollMode (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;)V
	public fun setOverflow (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;)V
	public fun setPagingEnabled (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setPersistentScrollbar (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setPointerEvents (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;)V
	public fun setRemoveClippedSubviews (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setScrollEnabled (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setScrollEventThrottle (Lcom/facebook/react/views/scroll/ReactScrollView;I)V
	public fun setScrollPerfTag (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;)V
	public fun setSendMomentumEvents (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setShowsVerticalScrollIndicator (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setSnapToAlignment (Lcom/facebook/react/views/scroll/ReactScrollView;Ljava/lang/String;)V
	public fun setSnapToEnd (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public fun setSnapToInterval (Lcom/facebook/react/views/scroll/ReactScrollView;F)V
	public fun setSnapToOffsets (Lcom/facebook/react/views/scroll/ReactScrollView;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setSnapToStart (Lcom/facebook/react/views/scroll/ReactScrollView;Z)V
	public synthetic fun updateState (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
	public fun updateState (Lcom/facebook/react/views/scroll/ReactScrollView;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
}

public final class com/facebook/react/views/scroll/ScrollEvent : com/facebook/react/uimanager/events/Event {
	public static final field Companion Lcom/facebook/react/views/scroll/ScrollEvent$Companion;
	public fun canCoalesce ()Z
	public fun getEventName ()Ljava/lang/String;
	public static final fun obtain (IILcom/facebook/react/views/scroll/ScrollEventType;FFFFIIII)Lcom/facebook/react/views/scroll/ScrollEvent;
	public static final fun obtain (ILcom/facebook/react/views/scroll/ScrollEventType;FFFFIIII)Lcom/facebook/react/views/scroll/ScrollEvent;
	public fun onDispose ()V
}

public final class com/facebook/react/views/scroll/ScrollEvent$Companion {
	public final fun obtain (IILcom/facebook/react/views/scroll/ScrollEventType;FFFFIIII)Lcom/facebook/react/views/scroll/ScrollEvent;
	public final fun obtain (ILcom/facebook/react/views/scroll/ScrollEventType;FFFFIIII)Lcom/facebook/react/views/scroll/ScrollEvent;
}

public final class com/facebook/react/views/scroll/ScrollEventType : java/lang/Enum {
	public static final field BEGIN_DRAG Lcom/facebook/react/views/scroll/ScrollEventType;
	public static final field Companion Lcom/facebook/react/views/scroll/ScrollEventType$Companion;
	public static final field END_DRAG Lcom/facebook/react/views/scroll/ScrollEventType;
	public static final field MOMENTUM_BEGIN Lcom/facebook/react/views/scroll/ScrollEventType;
	public static final field MOMENTUM_END Lcom/facebook/react/views/scroll/ScrollEventType;
	public static final field SCROLL Lcom/facebook/react/views/scroll/ScrollEventType;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static final fun getJSEventName (Lcom/facebook/react/views/scroll/ScrollEventType;)Ljava/lang/String;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/views/scroll/ScrollEventType;
	public static fun values ()[Lcom/facebook/react/views/scroll/ScrollEventType;
}

public final class com/facebook/react/views/scroll/ScrollEventType$Companion {
	public final fun getJSEventName (Lcom/facebook/react/views/scroll/ScrollEventType;)Ljava/lang/String;
}

public final class com/facebook/react/views/swiperefresh/ReactSwipeRefreshLayout : androidx/swiperefreshlayout/widget/SwipeRefreshLayout {
	public fun <init> (Lcom/facebook/react/bridge/ReactContext;)V
	public fun canChildScrollUp ()Z
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	public fun onLayout (ZIIII)V
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun requestDisallowInterceptTouchEvent (Z)V
	public final fun setProgressViewOffset (F)V
	public fun setRefreshing (Z)V
}

public final class com/facebook/react/views/text/DefaultStyleValuesUtil {
	public static final field INSTANCE Lcom/facebook/react/views/text/DefaultStyleValuesUtil;
	public static final fun getDefaultTextColor (Landroid/content/Context;)Landroid/content/res/ColorStateList;
	public static final fun getDefaultTextColorHighlight (Landroid/content/Context;)I
	public static final fun getDefaultTextColorHint (Landroid/content/Context;)Landroid/content/res/ColorStateList;
}

public final class com/facebook/react/views/text/FontMetricsUtil {
	public static final field INSTANCE Lcom/facebook/react/views/text/FontMetricsUtil;
	public static final fun getFontMetrics (Ljava/lang/CharSequence;Landroid/text/Layout;Landroid/text/TextPaint;Landroid/content/Context;)Lcom/facebook/react/bridge/WritableArray;
}

public abstract class com/facebook/react/views/text/ReactBaseTextShadowNode : com/facebook/react/uimanager/LayoutShadowNode {
	public static final field DEFAULT_TEXT_SHADOW_COLOR I
	public static final field PROP_SHADOW_COLOR Ljava/lang/String;
	public static final field PROP_SHADOW_OFFSET Ljava/lang/String;
	public static final field PROP_SHADOW_OFFSET_HEIGHT Ljava/lang/String;
	public static final field PROP_SHADOW_OFFSET_WIDTH Ljava/lang/String;
	public static final field PROP_SHADOW_RADIUS Ljava/lang/String;
	public static final field PROP_TEXT_TRANSFORM Ljava/lang/String;
	protected field mAccessibilityRole Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	protected field mAdjustsFontSizeToFit Z
	protected field mBackgroundColor I
	protected field mColor I
	protected field mContainsImages Z
	protected field mFontFamily Ljava/lang/String;
	protected field mFontFeatureSettings Ljava/lang/String;
	protected field mFontStyle I
	protected field mFontWeight I
	protected field mHyphenationFrequency I
	protected field mIncludeFontPadding Z
	protected field mInlineViews Ljava/util/Map;
	protected field mIsBackgroundColorSet Z
	protected field mIsColorSet Z
	protected field mIsLineThroughTextDecorationSet Z
	protected field mIsUnderlineTextDecorationSet Z
	protected field mJustificationMode I
	protected field mMinimumFontScale F
	protected field mNumberOfLines I
	protected field mReactTextViewManagerCallback Lcom/facebook/react/views/text/ReactTextViewManagerCallback;
	protected field mRole Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	protected field mTextAlign I
	protected field mTextAttributes Lcom/facebook/react/views/text/TextAttributes;
	protected field mTextBreakStrategy I
	protected field mTextShadowColor I
	protected field mTextShadowOffsetDx F
	protected field mTextShadowOffsetDy F
	protected field mTextShadowRadius F
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/views/text/ReactTextViewManagerCallback;)V
	public fun setAccessibilityRole (Ljava/lang/String;)V
	public fun setAdjustFontSizeToFit (Z)V
	public fun setAllowFontScaling (Z)V
	public fun setBackgroundColor (Ljava/lang/Integer;)V
	public fun setColor (Ljava/lang/Integer;)V
	public fun setFontFamily (Ljava/lang/String;)V
	public fun setFontSize (F)V
	public fun setFontStyle (Ljava/lang/String;)V
	public fun setFontVariant (Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setFontWeight (Ljava/lang/String;)V
	public fun setIncludeFontPadding (Z)V
	public fun setLetterSpacing (F)V
	public fun setLineHeight (F)V
	public fun setMaxFontSizeMultiplier (F)V
	public fun setMinimumFontScale (F)V
	public fun setNumberOfLines (I)V
	public fun setRole (Ljava/lang/String;)V
	public fun setTextAlign (Ljava/lang/String;)V
	public fun setTextBreakStrategy (Ljava/lang/String;)V
	public fun setTextDecorationLine (Ljava/lang/String;)V
	public fun setTextShadowColor (I)V
	public fun setTextShadowOffset (Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setTextShadowRadius (F)V
	public fun setTextTransform (Ljava/lang/String;)V
	protected fun spannedFromShadowNode (Lcom/facebook/react/views/text/ReactBaseTextShadowNode;Ljava/lang/String;ZLcom/facebook/react/uimanager/NativeViewHierarchyOptimizer;)Landroid/text/Spannable;
}

public final class com/facebook/react/views/text/ReactFontManager {
	public static final field Companion Lcom/facebook/react/views/text/ReactFontManager$Companion;
	public synthetic fun <init> (Lcom/facebook/react/common/assets/ReactFontManager;Lkotlin/jvm/internal/DefaultConstructorMarker;)V
	public final fun addCustomFont (Landroid/content/Context;Ljava/lang/String;I)V
	public final fun addCustomFont (Ljava/lang/String;Landroid/graphics/Typeface;)V
	public static final fun getInstance ()Lcom/facebook/react/views/text/ReactFontManager;
	public final fun getTypeface (Ljava/lang/String;IILandroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun getTypeface (Ljava/lang/String;ILandroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun getTypeface (Ljava/lang/String;IZLandroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public final fun setTypeface (Ljava/lang/String;ILandroid/graphics/Typeface;)V
}

public final class com/facebook/react/views/text/ReactFontManager$Companion {
	public final fun getInstance ()Lcom/facebook/react/views/text/ReactFontManager;
}

public final class com/facebook/react/views/text/ReactRawTextManager : com/facebook/react/uimanager/ViewManager {
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public synthetic fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun createShadowNodeInstance ()Lcom/facebook/react/views/text/ReactRawTextShadowNode;
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/text/ReactTextView;
	public fun getName ()Ljava/lang/String;
	public fun getShadowNodeClass ()Ljava/lang/Class;
	public fun updateExtraData (Landroid/view/View;Ljava/lang/Object;)V
}

public class com/facebook/react/views/text/ReactRawTextShadowNode : com/facebook/react/uimanager/ReactShadowNodeImpl {
	public fun <init> ()V
	public fun getText ()Ljava/lang/String;
	public fun isVirtual ()Z
	public fun setText (Ljava/lang/String;)V
	public fun toString ()Ljava/lang/String;
}

public abstract class com/facebook/react/views/text/ReactTextAnchorViewManager : com/facebook/react/uimanager/BaseViewManager {
	public fun <init> ()V
	public fun setAccessible (Lcom/facebook/react/views/text/ReactTextView;Z)V
	public fun setAdjustFontSizeToFit (Lcom/facebook/react/views/text/ReactTextView;Z)V
	public fun setAndroidHyphenationFrequency (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/String;)V
	public fun setBorderColor (Lcom/facebook/react/views/text/ReactTextView;ILjava/lang/Integer;)V
	public fun setBorderRadius (Lcom/facebook/react/views/text/ReactTextView;IF)V
	public fun setBorderStyle (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/String;)V
	public fun setBorderWidth (Lcom/facebook/react/views/text/ReactTextView;IF)V
	public fun setDataDetectorType (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/String;)V
	public fun setDisabled (Lcom/facebook/react/views/text/ReactTextView;Z)V
	public fun setEllipsizeMode (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/String;)V
	public fun setFontSize (Lcom/facebook/react/views/text/ReactTextView;F)V
	public fun setIncludeFontPadding (Lcom/facebook/react/views/text/ReactTextView;Z)V
	public fun setLetterSpacing (Lcom/facebook/react/views/text/ReactTextView;F)V
	public fun setNotifyOnInlineViewLayout (Lcom/facebook/react/views/text/ReactTextView;Z)V
	public fun setNumberOfLines (Lcom/facebook/react/views/text/ReactTextView;I)V
	public fun setSelectable (Lcom/facebook/react/views/text/ReactTextView;Z)V
	public fun setSelectionColor (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/Integer;)V
	public fun setTextAlignVertical (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/String;)V
}

public class com/facebook/react/views/text/ReactTextShadowNode : com/facebook/react/views/text/ReactBaseTextShadowNode {
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/views/text/ReactTextViewManagerCallback;)V
	public fun calculateLayoutOnChildren ()Ljava/lang/Iterable;
	public fun hoistNativeChildren ()Z
	public fun isVirtualAnchor ()Z
	public fun markUpdated ()V
	public fun onBeforeLayout (Lcom/facebook/react/uimanager/NativeViewHierarchyOptimizer;)V
	public fun onCollectExtraUpdates (Lcom/facebook/react/uimanager/UIViewOperationQueue;)V
	public fun setShouldNotifyOnTextLayout (Z)V
}

public final class com/facebook/react/views/text/ReactTextUpdate {
	public static final field Companion Lcom/facebook/react/views/text/ReactTextUpdate$Companion;
	public fun <init> (Landroid/text/Spannable;IZFFFFI)V
	public fun <init> (Landroid/text/Spannable;IZFFFFIII)V
	public fun <init> (Landroid/text/Spannable;IZIII)V
	public static final fun buildReactTextUpdateFromState (Landroid/text/Spannable;IIII)Lcom/facebook/react/views/text/ReactTextUpdate;
	public final fun containsImages ()Z
	public final fun getContainsImages ()Z
	public final fun getJsEventCounter ()I
	public final fun getJustificationMode ()I
	public final fun getPaddingBottom ()F
	public final fun getPaddingLeft ()F
	public final fun getPaddingRight ()F
	public final fun getPaddingTop ()F
	public final fun getText ()Landroid/text/Spannable;
	public final fun getTextAlign ()I
	public final fun getTextBreakStrategy ()I
}

public final class com/facebook/react/views/text/ReactTextUpdate$Companion {
	public final fun buildReactTextUpdateFromState (Landroid/text/Spannable;IIII)Lcom/facebook/react/views/text/ReactTextUpdate;
}

public class com/facebook/react/views/text/ReactTextView : androidx/appcompat/widget/AppCompatTextView, com/facebook/react/uimanager/ReactCompoundView {
	public fun <init> (Landroid/content/Context;)V
	protected fun dispatchHoverEvent (Landroid/view/MotionEvent;)Z
	public fun dispatchKeyEvent (Landroid/view/KeyEvent;)Z
	public fun getSpanned ()Landroid/text/Spannable;
	public fun hasOverlappingRendering ()Z
	public fun invalidateDrawable (Landroid/graphics/drawable/Drawable;)V
	public fun onAttachedToWindow ()V
	public fun onDetachedFromWindow ()V
	protected fun onDraw (Landroid/graphics/Canvas;)V
	public fun onFinishTemporaryDetach ()V
	public final fun onFocusChanged (ZILandroid/graphics/Rect;)V
	protected fun onLayout (ZIIII)V
	protected fun onMeasure (II)V
	public fun onStartTemporaryDetach ()V
	public fun reactTagForTouch (FF)I
	public fun setAdjustFontSizeToFit (Z)V
	public fun setBackgroundColor (I)V
	public fun setBorderColor (ILjava/lang/Integer;)V
	public fun setBorderRadius (F)V
	public fun setBorderRadius (FI)V
	public fun setBorderStyle (Ljava/lang/String;)V
	public fun setBorderWidth (IF)V
	public fun setBreakStrategy (I)V
	public fun setEllipsizeLocation (Landroid/text/TextUtils$TruncateAt;)V
	public fun setFontSize (F)V
	public fun setHyphenationFrequency (I)V
	public fun setIncludeFontPadding (Z)V
	public fun setLetterSpacing (F)V
	public fun setLinkifyMask (I)V
	public fun setMinimumFontSize (F)V
	public fun setNotifyOnInlineViewLayout (Z)V
	public fun setNumberOfLines (I)V
	public fun setOverflow (Ljava/lang/String;)V
	public fun setSpanned (Landroid/text/Spannable;)V
	public fun setText (Lcom/facebook/react/views/text/ReactTextUpdate;)V
	public fun setTextIsSelectable (Z)V
	public fun updateView ()V
	protected fun verifyDrawable (Landroid/graphics/drawable/Drawable;)Z
}

public class com/facebook/react/views/text/ReactTextViewManager : com/facebook/react/views/text/ReactTextAnchorViewManager, com/facebook/react/uimanager/IViewManagerWithChildren {
	protected field mReactTextViewManagerCallback Lcom/facebook/react/views/text/ReactTextViewManagerCallback;
	public fun <init> ()V
	public fun <init> (Lcom/facebook/react/views/text/ReactTextViewManagerCallback;)V
	public synthetic fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun createShadowNodeInstance ()Lcom/facebook/react/views/text/ReactTextShadowNode;
	public fun createShadowNodeInstance (Lcom/facebook/react/views/text/ReactTextViewManagerCallback;)Lcom/facebook/react/views/text/ReactTextShadowNode;
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/text/ReactTextView;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public fun getShadowNodeClass ()Ljava/lang/Class;
	public fun measure (Landroid/content/Context;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;FLcom/facebook/yoga/YogaMeasureMode;FLcom/facebook/yoga/YogaMeasureMode;[F)J
	public fun needsCustomLayoutForChildren ()Z
	protected synthetic fun onAfterUpdateTransaction (Landroid/view/View;)V
	protected fun onAfterUpdateTransaction (Lcom/facebook/react/views/text/ReactTextView;)V
	protected synthetic fun prepareToRecycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)Landroid/view/View;
	protected fun prepareToRecycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/views/text/ReactTextView;)Lcom/facebook/react/views/text/ReactTextView;
	public fun setOverflow (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/String;)V
	public synthetic fun setPadding (Landroid/view/View;IIII)V
	public fun setPadding (Lcom/facebook/react/views/text/ReactTextView;IIII)V
	public synthetic fun updateExtraData (Landroid/view/View;Ljava/lang/Object;)V
	public fun updateExtraData (Lcom/facebook/react/views/text/ReactTextView;Ljava/lang/Object;)V
	public synthetic fun updateState (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
	public fun updateState (Lcom/facebook/react/views/text/ReactTextView;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
	protected synthetic fun updateViewAccessibility (Landroid/view/View;)V
	protected fun updateViewAccessibility (Lcom/facebook/react/views/text/ReactTextView;)V
}

public abstract interface class com/facebook/react/views/text/ReactTextViewManagerCallback {
	public abstract fun onPostProcessSpannable (Landroid/text/Spannable;)V
}

public final class com/facebook/react/views/text/ReactTypefaceUtils {
	public static final field INSTANCE Lcom/facebook/react/views/text/ReactTypefaceUtils;
	public static final fun applyStyles (Landroid/graphics/Typeface;IILjava/lang/String;Landroid/content/res/AssetManager;)Landroid/graphics/Typeface;
	public static final fun parseFontStyle (Ljava/lang/String;)I
	public static final fun parseFontVariant (Lcom/facebook/react/bridge/ReadableArray;)Ljava/lang/String;
	public static final fun parseFontWeight (Ljava/lang/String;)I
}

public class com/facebook/react/views/text/TextAttributeProps {
	public static final field TA_KEY_ACCESSIBILITY_ROLE S
	public static final field TA_KEY_ALIGNMENT S
	public static final field TA_KEY_ALLOW_FONT_SCALING S
	public static final field TA_KEY_BACKGROUND_COLOR S
	public static final field TA_KEY_BEST_WRITING_DIRECTION S
	public static final field TA_KEY_FONT_FAMILY S
	public static final field TA_KEY_FONT_SIZE S
	public static final field TA_KEY_FONT_SIZE_MULTIPLIER S
	public static final field TA_KEY_FONT_STYLE S
	public static final field TA_KEY_FONT_VARIANT S
	public static final field TA_KEY_FONT_WEIGHT S
	public static final field TA_KEY_FOREGROUND_COLOR S
	public static final field TA_KEY_IS_HIGHLIGHTED S
	public static final field TA_KEY_LAYOUT_DIRECTION S
	public static final field TA_KEY_LETTER_SPACING S
	public static final field TA_KEY_LINE_BREAK_STRATEGY S
	public static final field TA_KEY_LINE_HEIGHT S
	public static final field TA_KEY_MAX_FONT_SIZE_MULTIPLIER S
	public static final field TA_KEY_OPACITY S
	public static final field TA_KEY_ROLE S
	public static final field TA_KEY_TEXT_DECORATION_COLOR S
	public static final field TA_KEY_TEXT_DECORATION_LINE S
	public static final field TA_KEY_TEXT_DECORATION_STYLE S
	public static final field TA_KEY_TEXT_SHADOW_COLOR S
	public static final field TA_KEY_TEXT_SHADOW_OFFSET_DX S
	public static final field TA_KEY_TEXT_SHADOW_OFFSET_DY S
	public static final field TA_KEY_TEXT_SHADOW_RADIUS S
	public static final field TA_KEY_TEXT_TRANSFORM S
	public static final field UNSET I
	protected field mAccessibilityRole Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	protected field mAllowFontScaling Z
	protected field mBackgroundColor I
	protected field mColor I
	protected field mContainsImages Z
	protected field mFontFamily Ljava/lang/String;
	protected field mFontFeatureSettings Ljava/lang/String;
	protected field mFontSize I
	protected field mFontSizeInput F
	protected field mFontStyle I
	protected field mFontWeight I
	protected field mHeightOfTallestInlineImage F
	protected field mIncludeFontPadding Z
	protected field mIsBackgroundColorSet Z
	protected field mIsColorSet Z
	protected field mIsLineThroughTextDecorationSet Z
	protected field mIsUnderlineTextDecorationSet Z
	protected field mLayoutDirection I
	protected field mLetterSpacingInput F
	protected field mLineHeight F
	protected field mLineHeightInput F
	protected field mMaxFontSizeMultiplier F
	protected field mNumberOfLines I
	protected field mOpacity F
	protected field mRole Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	protected field mTextAlign I
	protected field mTextShadowColor I
	protected field mTextShadowOffsetDx F
	protected field mTextShadowOffsetDy F
	protected field mTextShadowRadius F
	protected field mTextTransform Lcom/facebook/react/views/text/TextTransform;
	public static fun fromMapBuffer (Lcom/facebook/react/common/mapbuffer/MapBuffer;)Lcom/facebook/react/views/text/TextAttributeProps;
	public static fun fromReadableMap (Lcom/facebook/react/uimanager/ReactStylesDiffMap;)Lcom/facebook/react/views/text/TextAttributeProps;
	public fun getAccessibilityRole ()Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$AccessibilityRole;
	public fun getBackgroundColor ()I
	public fun getColor ()I
	public fun getEffectiveFontSize ()I
	public fun getEffectiveLetterSpacing ()F
	public fun getEffectiveLineHeight ()F
	public fun getFontFamily ()Ljava/lang/String;
	public fun getFontFeatureSettings ()Ljava/lang/String;
	public fun getFontStyle ()I
	public fun getFontWeight ()I
	public static fun getHyphenationFrequency (Ljava/lang/String;)I
	public static fun getJustificationMode (Lcom/facebook/react/uimanager/ReactStylesDiffMap;I)I
	public static fun getLayoutDirection (Ljava/lang/String;)I
	public fun getLetterSpacing ()F
	public fun getOpacity ()F
	public fun getRole ()Lcom/facebook/react/uimanager/ReactAccessibilityDelegate$Role;
	public static fun getTextAlignment (Lcom/facebook/react/uimanager/ReactStylesDiffMap;ZI)I
	public static fun getTextBreakStrategy (Ljava/lang/String;)I
	public fun getTextShadowColor ()I
	public fun getTextShadowOffsetDx ()F
	public fun getTextShadowOffsetDy ()F
	public fun getTextShadowRadius ()F
	public fun getTextTransform ()Lcom/facebook/react/views/text/TextTransform;
	public fun isBackgroundColorSet ()Z
	public fun isColorSet ()Z
	public fun isLineThroughTextDecorationSet ()Z
	public fun isUnderlineTextDecorationSet ()Z
}

public class com/facebook/react/views/text/TextAttributes {
	public static final field DEFAULT_MAX_FONT_SIZE_MULTIPLIER F
	public fun <init> ()V
	public fun applyChild (Lcom/facebook/react/views/text/TextAttributes;)Lcom/facebook/react/views/text/TextAttributes;
	public fun getAllowFontScaling ()Z
	public fun getEffectiveFontSize ()I
	public fun getEffectiveLetterSpacing ()F
	public fun getEffectiveLineHeight ()F
	public fun getEffectiveMaxFontSizeMultiplier ()F
	public fun getFontSize ()F
	public fun getHeightOfTallestInlineViewOrImage ()F
	public fun getLetterSpacing ()F
	public fun getLineHeight ()F
	public fun getMaxFontSizeMultiplier ()F
	public fun getTextTransform ()Lcom/facebook/react/views/text/TextTransform;
	public fun setAllowFontScaling (Z)V
	public fun setFontSize (F)V
	public fun setHeightOfTallestInlineViewOrImage (F)V
	public fun setLetterSpacing (F)V
	public fun setLineHeight (F)V
	public fun setMaxFontSizeMultiplier (F)V
	public fun setTextTransform (Lcom/facebook/react/views/text/TextTransform;)V
	public fun toString ()Ljava/lang/String;
}

public class com/facebook/react/views/text/TextLayoutManager {
	public static final field AS_KEY_BASE_ATTRIBUTES S
	public static final field AS_KEY_CACHE_ID S
	public static final field AS_KEY_FRAGMENTS S
	public static final field AS_KEY_HASH S
	public static final field AS_KEY_STRING S
	public static final field FR_KEY_HEIGHT S
	public static final field FR_KEY_IS_ATTACHMENT S
	public static final field FR_KEY_REACT_TAG S
	public static final field FR_KEY_STRING S
	public static final field FR_KEY_TEXT_ATTRIBUTES S
	public static final field FR_KEY_WIDTH S
	public static final field PA_KEY_ADJUST_FONT_SIZE_TO_FIT S
	public static final field PA_KEY_ELLIPSIZE_MODE S
	public static final field PA_KEY_HYPHENATION_FREQUENCY S
	public static final field PA_KEY_INCLUDE_FONT_PADDING S
	public static final field PA_KEY_MAXIMUM_FONT_SIZE S
	public static final field PA_KEY_MAX_NUMBER_OF_LINES S
	public static final field PA_KEY_MINIMUM_FONT_SIZE S
	public static final field PA_KEY_TEXT_BREAK_STRATEGY S
	public fun <init> ()V
	public static fun deleteCachedSpannableForTag (I)V
	public static fun getOrCreateSpannableForText (Landroid/content/Context;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/views/text/ReactTextViewManagerCallback;)Landroid/text/Spannable;
	public static fun getTextGravity (Lcom/facebook/react/common/mapbuffer/MapBuffer;Landroid/text/Spannable;I)I
	public static fun isRTL (Lcom/facebook/react/common/mapbuffer/MapBuffer;)Z
	public static fun measureLines (Landroid/content/Context;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;FF)Lcom/facebook/react/bridge/WritableArray;
	public static fun measureText (Landroid/content/Context;Lcom/facebook/react/common/mapbuffer/MapBuffer;Lcom/facebook/react/common/mapbuffer/MapBuffer;FLcom/facebook/yoga/YogaMeasureMode;FLcom/facebook/yoga/YogaMeasureMode;Lcom/facebook/react/views/text/ReactTextViewManagerCallback;[F)J
	public static fun setCachedSpannableForTag (ILandroid/text/Spannable;)V
}

public final class com/facebook/react/views/text/TextTransform : java/lang/Enum {
	public static final field CAPITALIZE Lcom/facebook/react/views/text/TextTransform;
	public static final field Companion Lcom/facebook/react/views/text/TextTransform$Companion;
	public static final field LOWERCASE Lcom/facebook/react/views/text/TextTransform;
	public static final field NONE Lcom/facebook/react/views/text/TextTransform;
	public static final field UNSET Lcom/facebook/react/views/text/TextTransform;
	public static final field UPPERCASE Lcom/facebook/react/views/text/TextTransform;
	public static final fun apply (Ljava/lang/String;Lcom/facebook/react/views/text/TextTransform;)Ljava/lang/String;
	public static fun getEntries ()Lkotlin/enums/EnumEntries;
	public static fun valueOf (Ljava/lang/String;)Lcom/facebook/react/views/text/TextTransform;
	public static fun values ()[Lcom/facebook/react/views/text/TextTransform;
}

public final class com/facebook/react/views/text/TextTransform$Companion {
	public final fun apply (Ljava/lang/String;Lcom/facebook/react/views/text/TextTransform;)Ljava/lang/String;
}

public final class com/facebook/react/views/text/TextTransformKt {
	public static final fun applyTextTransform (Ljava/lang/String;Lcom/facebook/react/views/text/TextTransform;)Ljava/lang/String;
}

public abstract interface class com/facebook/react/views/textinput/ContentSizeWatcher {
	public abstract fun onLayout ()V
}

public class com/facebook/react/views/textinput/ReactContentSizeChangedEvent : com/facebook/react/uimanager/events/Event {
	public static final field EVENT_NAME Ljava/lang/String;
	public fun <init> (IFF)V
	public fun <init> (IIFF)V
	protected fun getEventData ()Lcom/facebook/react/bridge/WritableMap;
	public fun getEventName ()Ljava/lang/String;
}

public class com/facebook/react/views/textinput/ReactEditText : androidx/appcompat/widget/AppCompatEditText {
	public static final field DEBUG_MODE Z
	protected field mContainsImages Z
	protected field mDisableTextDiffing Z
	protected field mIsSettingTextFromJS Z
	protected field mIsSettingTextFromState Z
	protected field mNativeEventCount I
	public fun <init> (Landroid/content/Context;)V
	public fun addTextChangedListener (Landroid/text/TextWatcher;)V
	protected fun applyTextAttributes ()V
	public fun canUpdateWithEventCount (I)Z
	public fun clearFocus ()V
	protected fun finalize ()V
	public fun getBorderColor (I)I
	public fun getDisableFullscreenUI ()Z
	public fun getReturnKeyType ()Ljava/lang/String;
	public fun getStateWrapper ()Lcom/facebook/react/uimanager/StateWrapper;
	public fun getSubmitBehavior ()Ljava/lang/String;
	protected fun hideSoftKeyboard ()V
	public fun incrementAndGetEventCounter ()I
	public fun invalidateDrawable (Landroid/graphics/drawable/Drawable;)V
	public fun isLayoutRequested ()Z
	public fun maybeSetSelection (III)V
	public fun maybeSetText (Lcom/facebook/react/views/text/ReactTextUpdate;)V
	public fun maybeSetTextFromJS (Lcom/facebook/react/views/text/ReactTextUpdate;)V
	public fun maybeSetTextFromState (Lcom/facebook/react/views/text/ReactTextUpdate;)V
	public fun maybeUpdateTypeface ()V
	public fun onAttachedToWindow ()V
	public fun onCreateInputConnection (Landroid/view/inputmethod/EditorInfo;)Landroid/view/inputmethod/InputConnection;
	public fun onDetachedFromWindow ()V
	public fun onDraw (Landroid/graphics/Canvas;)V
	public fun onFinishTemporaryDetach ()V
	protected fun onFocusChanged (ZILandroid/graphics/Rect;)V
	public fun onKeyUp (ILandroid/view/KeyEvent;)Z
	protected fun onLayout (ZIIII)V
	protected fun onScrollChanged (IIII)V
	protected fun onSelectionChanged (II)V
	public fun onStartTemporaryDetach ()V
	public fun onTextContextMenuItem (I)Z
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun removeTextChangedListener (Landroid/text/TextWatcher;)V
	public fun requestFocus (ILandroid/graphics/Rect;)Z
	public fun requestFocusFromJS ()V
	public fun setAllowFontScaling (Z)V
	public fun setAutoFocus (Z)V
	public fun setBackgroundColor (I)V
	public fun setBorderColor (ILjava/lang/Integer;)V
	public fun setBorderRadius (F)V
	public fun setBorderRadius (FI)V
	public fun setBorderStyle (Ljava/lang/String;)V
	public fun setBorderWidth (IF)V
	public fun setContentSizeWatcher (Lcom/facebook/react/views/textinput/ContentSizeWatcher;)V
	public fun setContextMenuHidden (Z)V
	public fun setDisableFullscreenUI (Z)V
	public fun setFontFamily (Ljava/lang/String;)V
	public fun setFontFeatureSettings (Ljava/lang/String;)V
	public fun setFontSize (F)V
	public fun setFontStyle (Ljava/lang/String;)V
	public fun setFontWeight (Ljava/lang/String;)V
	public fun setInputType (I)V
	public fun setLetterSpacingPt (F)V
	public fun setLineHeight (I)V
	public fun setMaxFontSizeMultiplier (F)V
	public fun setOnKeyPress (Z)V
	public fun setOverflow (Ljava/lang/String;)V
	public fun setPlaceholder (Ljava/lang/String;)V
	public fun setReturnKeyType (Ljava/lang/String;)V
	public fun setScrollWatcher (Lcom/facebook/react/views/textinput/ScrollWatcher;)V
	public fun setSelectTextOnFocus (Z)V
	public fun setSelection (II)V
	public fun setSelectionWatcher (Lcom/facebook/react/views/textinput/SelectionWatcher;)V
	public fun setStateWrapper (Lcom/facebook/react/uimanager/StateWrapper;)V
	public fun setSubmitBehavior (Ljava/lang/String;)V
	public fun shouldBlurOnReturn ()Z
	public fun shouldSubmitOnReturn ()Z
	protected fun showSoftKeyboard ()Z
	protected fun verifyDrawable (Landroid/graphics/drawable/Drawable;)Z
}

public class com/facebook/react/views/textinput/ReactTextChangedEvent : com/facebook/react/uimanager/events/Event {
	public static final field EVENT_NAME Ljava/lang/String;
	public fun <init> (IILjava/lang/String;I)V
	public fun <init> (ILjava/lang/String;I)V
	protected fun getEventData ()Lcom/facebook/react/bridge/WritableMap;
	public fun getEventName ()Ljava/lang/String;
}

public final class com/facebook/react/views/textinput/ReactTextInputLocalData {
	public fun <init> (Landroid/widget/EditText;)V
	public fun apply (Landroid/widget/EditText;)V
}

public class com/facebook/react/views/textinput/ReactTextInputManager : com/facebook/react/uimanager/BaseViewManager {
	public static final field REACT_CLASS Ljava/lang/String;
	public static final field TAG Ljava/lang/String;
	protected field mReactTextViewManagerCallback Lcom/facebook/react/views/text/ReactTextViewManagerCallback;
	public fun <init> ()V
	protected synthetic fun addEventEmitters (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)V
	protected fun addEventEmitters (Lcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/views/textinput/ReactEditText;)V
	public synthetic fun createShadowNodeInstance ()Lcom/facebook/react/uimanager/ReactShadowNode;
	public fun createShadowNodeInstance ()Lcom/facebook/react/views/text/ReactBaseTextShadowNode;
	public fun createShadowNodeInstance (Lcom/facebook/react/views/text/ReactTextViewManagerCallback;)Lcom/facebook/react/views/text/ReactBaseTextShadowNode;
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/textinput/ReactEditText;
	public fun getCommandsMap ()Ljava/util/Map;
	public fun getExportedCustomBubblingEventTypeConstants ()Ljava/util/Map;
	public fun getExportedCustomDirectEventTypeConstants ()Ljava/util/Map;
	public fun getExportedViewConstants ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public fun getReactTextUpdate (Lcom/facebook/react/views/textinput/ReactEditText;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/common/mapbuffer/MapBuffer;)Ljava/lang/Object;
	public fun getShadowNodeClass ()Ljava/lang/Class;
	protected synthetic fun onAfterUpdateTransaction (Landroid/view/View;)V
	protected fun onAfterUpdateTransaction (Lcom/facebook/react/views/textinput/ReactEditText;)V
	public synthetic fun receiveCommand (Landroid/view/View;ILcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/textinput/ReactEditText;ILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setAllowFontScaling (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setAutoCapitalize (Lcom/facebook/react/views/textinput/ReactEditText;Lcom/facebook/react/bridge/Dynamic;)V
	public fun setAutoCorrect (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Boolean;)V
	public fun setAutoFocus (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setBorderColor (Lcom/facebook/react/views/textinput/ReactEditText;ILjava/lang/Integer;)V
	public fun setBorderRadius (Lcom/facebook/react/views/textinput/ReactEditText;IF)V
	public fun setBorderStyle (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setBorderWidth (Lcom/facebook/react/views/textinput/ReactEditText;IF)V
	public fun setCaretHidden (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setColor (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun setContextMenuHidden (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setCursorColor (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun setDisableFullscreenUI (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setEditable (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setFontFamily (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setFontSize (Lcom/facebook/react/views/textinput/ReactEditText;F)V
	public fun setFontStyle (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setFontVariant (Lcom/facebook/react/views/textinput/ReactEditText;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setFontWeight (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setImportantForAutofill (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setIncludeFontPadding (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setInlineImageLeft (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setInlineImagePadding (Lcom/facebook/react/views/textinput/ReactEditText;I)V
	public fun setKeyboardType (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setLetterSpacing (Lcom/facebook/react/views/textinput/ReactEditText;F)V
	public fun setLineHeight (Lcom/facebook/react/views/textinput/ReactEditText;I)V
	public fun setMaxFontSizeMultiplier (Lcom/facebook/react/views/textinput/ReactEditText;F)V
	public fun setMaxLength (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun setMultiline (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setNumLines (Lcom/facebook/react/views/textinput/ReactEditText;I)V
	public fun setOnContentSizeChange (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setOnKeyPress (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setOnScroll (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setOnSelectionChange (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setOverflow (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public synthetic fun setPadding (Landroid/view/View;IIII)V
	public fun setPadding (Lcom/facebook/react/views/textinput/ReactEditText;IIII)V
	public fun setPlaceholder (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setPlaceholderTextColor (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun setReturnKeyLabel (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setReturnKeyType (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setSecureTextEntry (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setSelectTextOnFocus (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public fun setSelectionColor (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun setSelectionHandleColor (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun setSubmitBehavior (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setTextAlign (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setTextAlignVertical (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setTextContentType (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setTextDecorationLine (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/String;)V
	public fun setUnderlineColor (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Integer;)V
	public fun showKeyboardOnFocus (Lcom/facebook/react/views/textinput/ReactEditText;Z)V
	public synthetic fun updateExtraData (Landroid/view/View;Ljava/lang/Object;)V
	public fun updateExtraData (Lcom/facebook/react/views/textinput/ReactEditText;Ljava/lang/Object;)V
	public synthetic fun updateState (Landroid/view/View;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
	public fun updateState (Lcom/facebook/react/views/textinput/ReactEditText;Lcom/facebook/react/uimanager/ReactStylesDiffMap;Lcom/facebook/react/uimanager/StateWrapper;)Ljava/lang/Object;
}

public abstract interface class com/facebook/react/views/textinput/ScrollWatcher {
	public abstract fun onScrollChanged (IIII)V
}

public final class com/facebook/react/views/view/ColorUtil {
	public static final field INSTANCE Lcom/facebook/react/views/view/ColorUtil;
	public static final fun normalize (DDDD)I
}

public final class com/facebook/react/views/view/MeasureUtil {
	public static final field INSTANCE Lcom/facebook/react/views/view/MeasureUtil;
	public static final fun getMeasureSpec (FLcom/facebook/yoga/YogaMeasureMode;)I
}

public abstract class com/facebook/react/views/view/ReactClippingViewManager : com/facebook/react/uimanager/ViewGroupManager {
	public fun <init> ()V
	public synthetic fun addView (Landroid/view/View;Landroid/view/View;I)V
	public synthetic fun addView (Landroid/view/ViewGroup;Landroid/view/View;I)V
	public fun addView (Lcom/facebook/react/views/view/ReactViewGroup;Landroid/view/View;I)V
	public synthetic fun getChildAt (Landroid/view/View;I)Landroid/view/View;
	public synthetic fun getChildAt (Landroid/view/ViewGroup;I)Landroid/view/View;
	public fun getChildAt (Lcom/facebook/react/views/view/ReactViewGroup;I)Landroid/view/View;
	public synthetic fun getChildCount (Landroid/view/View;)I
	public synthetic fun getChildCount (Landroid/view/ViewGroup;)I
	public fun getChildCount (Lcom/facebook/react/views/view/ReactViewGroup;)I
	public synthetic fun removeAllViews (Landroid/view/View;)V
	public fun removeAllViews (Lcom/facebook/react/views/view/ReactViewGroup;)V
	public synthetic fun removeViewAt (Landroid/view/View;I)V
	public synthetic fun removeViewAt (Landroid/view/ViewGroup;I)V
	public fun removeViewAt (Lcom/facebook/react/views/view/ReactViewGroup;I)V
	public fun setRemoveClippedSubviews (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
}

public final class com/facebook/react/views/view/ReactDrawableHelper {
	public static final field INSTANCE Lcom/facebook/react/views/view/ReactDrawableHelper;
	public static final fun createDrawableFromJSDescription (Landroid/content/Context;Lcom/facebook/react/bridge/ReadableMap;)Landroid/graphics/drawable/Drawable;
}

public class com/facebook/react/views/view/ReactViewGroup : android/view/ViewGroup, com/facebook/react/touch/ReactHitSlopView, com/facebook/react/touch/ReactInterceptingViewGroup, com/facebook/react/uimanager/ReactClippingViewGroup, com/facebook/react/uimanager/ReactOverflowViewWithInset, com/facebook/react/uimanager/ReactPointerEventsView, com/facebook/react/uimanager/ReactZIndexedViewGroup {
	public fun <init> (Landroid/content/Context;)V
	protected fun dispatchDraw (Landroid/graphics/Canvas;)V
	public fun dispatchGenericMotionEvent (Landroid/view/MotionEvent;)Z
	public fun dispatchProvideStructure (Landroid/view/ViewStructure;)V
	protected fun dispatchSetPressed (Z)V
	public fun draw (Landroid/graphics/Canvas;)V
	protected fun drawChild (Landroid/graphics/Canvas;Landroid/view/View;J)Z
	public fun endViewTransition (Landroid/view/View;)V
	protected fun getChildDrawingOrder (II)I
	public fun getClippingRect (Landroid/graphics/Rect;)V
	public fun getHitSlopRect ()Landroid/graphics/Rect;
	public fun getOverflow ()Ljava/lang/String;
	public fun getOverflowInset ()Landroid/graphics/Rect;
	public fun getPointerEvents ()Lcom/facebook/react/uimanager/PointerEvents;
	public fun getRemoveClippedSubviews ()Z
	public fun getZIndexMappedChildIndex (I)I
	public fun hasOverlappingRendering ()Z
	protected fun onAttachedToWindow ()V
	public fun onHoverEvent (Landroid/view/MotionEvent;)Z
	public fun onInterceptTouchEvent (Landroid/view/MotionEvent;)Z
	protected fun onLayout (ZIIII)V
	protected fun onMeasure (II)V
	protected fun onSizeChanged (IIII)V
	public fun onTouchEvent (Landroid/view/MotionEvent;)Z
	public fun onViewAdded (Landroid/view/View;)V
	public fun onViewRemoved (Landroid/view/View;)V
	public fun requestLayout ()V
	public fun setBackfaceVisibility (Ljava/lang/String;)V
	public fun setBackfaceVisibilityDependantOpacity ()V
	public fun setBackgroundColor (I)V
	public fun setBorderColor (ILjava/lang/Integer;)V
	public fun setBorderRadius (F)V
	public fun setBorderRadius (FI)V
	public fun setBorderRadius (Lcom/facebook/react/uimanager/style/BorderRadiusProp;Lcom/facebook/react/uimanager/LengthPercentage;)V
	public fun setBorderStyle (Ljava/lang/String;)V
	public fun setBorderWidth (IF)V
	public fun setHitSlopRect (Landroid/graphics/Rect;)V
	public fun setNeedsOffscreenAlphaCompositing (Z)V
	public fun setOnInterceptTouchEventListener (Lcom/facebook/react/touch/OnInterceptTouchEventListener;)V
	public fun setOpacityIfPossible (F)V
	public fun setOverflow (Ljava/lang/String;)V
	public fun setOverflowInset (IIII)V
	public fun setPointerEvents (Lcom/facebook/react/uimanager/PointerEvents;)V
	public fun setRemoveClippedSubviews (Z)V
	public fun setTranslucentBackgroundDrawable (Landroid/graphics/drawable/Drawable;)V
	public fun updateClippingRect ()V
	public fun updateDrawingOrder ()V
}

public class com/facebook/react/views/view/ReactViewManager : com/facebook/react/views/view/ReactClippingViewManager {
	public static final field Companion Lcom/facebook/react/views/view/ReactViewManager$Companion;
	public static final field REACT_CLASS Ljava/lang/String;
	public fun <init> ()V
	public synthetic fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Landroid/view/View;
	public fun createViewInstance (Lcom/facebook/react/uimanager/ThemedReactContext;)Lcom/facebook/react/views/view/ReactViewGroup;
	public fun getCommandsMap ()Ljava/util/Map;
	public fun getName ()Ljava/lang/String;
	public fun nextFocusDown (Lcom/facebook/react/views/view/ReactViewGroup;I)V
	public fun nextFocusForward (Lcom/facebook/react/views/view/ReactViewGroup;I)V
	public fun nextFocusLeft (Lcom/facebook/react/views/view/ReactViewGroup;I)V
	public fun nextFocusRight (Lcom/facebook/react/views/view/ReactViewGroup;I)V
	public fun nextFocusUp (Lcom/facebook/react/views/view/ReactViewGroup;I)V
	public synthetic fun prepareToRecycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Landroid/view/View;)Landroid/view/View;
	protected fun prepareToRecycleView (Lcom/facebook/react/uimanager/ThemedReactContext;Lcom/facebook/react/views/view/ReactViewGroup;)Lcom/facebook/react/views/view/ReactViewGroup;
	public synthetic fun receiveCommand (Landroid/view/View;ILcom/facebook/react/bridge/ReadableArray;)V
	public synthetic fun receiveCommand (Landroid/view/View;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/view/ReactViewGroup;ILcom/facebook/react/bridge/ReadableArray;)V
	public fun receiveCommand (Lcom/facebook/react/views/view/ReactViewGroup;Ljava/lang/String;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setAccessible (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
	public fun setBackfaceVisibility (Lcom/facebook/react/views/view/ReactViewGroup;Ljava/lang/String;)V
	public fun setBackgroundImage (Lcom/facebook/react/views/view/ReactViewGroup;Lcom/facebook/react/bridge/ReadableArray;)V
	public fun setBorderColor (Lcom/facebook/react/views/view/ReactViewGroup;ILjava/lang/Integer;)V
	public fun setBorderRadius (Lcom/facebook/react/views/view/ReactViewGroup;IF)V
	public fun setBorderRadius (Lcom/facebook/react/views/view/ReactViewGroup;ILcom/facebook/react/bridge/Dynamic;)V
	public fun setBorderStyle (Lcom/facebook/react/views/view/ReactViewGroup;Ljava/lang/String;)V
	public fun setBorderWidth (Lcom/facebook/react/views/view/ReactViewGroup;IF)V
	public fun setCollapsable (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
	public fun setCollapsableChildren (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
	public fun setFocusable (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
	public fun setHitSlop (Lcom/facebook/react/views/view/ReactViewGroup;Lcom/facebook/react/bridge/Dynamic;)V
	public fun setNativeBackground (Lcom/facebook/react/views/view/ReactViewGroup;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setNativeForeground (Lcom/facebook/react/views/view/ReactViewGroup;Lcom/facebook/react/bridge/ReadableMap;)V
	public fun setNeedsOffscreenAlphaCompositing (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
	public synthetic fun setOpacity (Landroid/view/View;F)V
	public fun setOpacity (Lcom/facebook/react/views/view/ReactViewGroup;F)V
	public fun setOverflow (Lcom/facebook/react/views/view/ReactViewGroup;Ljava/lang/String;)V
	public fun setPointerEvents (Lcom/facebook/react/views/view/ReactViewGroup;Ljava/lang/String;)V
	public fun setTVPreferredFocus (Lcom/facebook/react/views/view/ReactViewGroup;Z)V
	public synthetic fun setTransformProperty (Landroid/view/View;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;)V
	protected fun setTransformProperty (Lcom/facebook/react/views/view/ReactViewGroup;Lcom/facebook/react/bridge/ReadableArray;Lcom/facebook/react/bridge/ReadableArray;)V
}

public final class com/facebook/react/views/view/ReactViewManager$Companion {
}

public final class com/facebook/react/views/view/ViewGroupClickEvent : com/facebook/react/uimanager/events/Event {
	public fun <init> (I)V
	public fun <init> (II)V
	public fun canCoalesce ()Z
	public fun getEventName ()Ljava/lang/String;
}

