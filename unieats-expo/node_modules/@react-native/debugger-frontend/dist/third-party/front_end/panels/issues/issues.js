import*as e from"../../core/common/common.js";import*as t from"../../models/issues_manager/issues_manager.js";import*as s from"../../ui/legacy/legacy.js";import*as i from"../../core/i18n/i18n.js";import*as n from"../../ui/components/issue_counter/issue_counter.js";import*as r from"../../ui/visual_logging/visual_logging.js";import*as a from"../../ui/components/adorners/adorners.js";import*as o from"../../ui/components/icon_button/icon_button.js";import*as d from"./components/components.js";import*as l from"../../core/host/host.js";import*as c from"../network/forward/forward.js";import*as u from"../../ui/components/markdown_view/markdown_view.js";import*as p from"../../core/sdk/sdk.js";import*as h from"../../models/logs/logs.js";import*as g from"../../ui/components/request_link_icon/request_link_icon.js";import*as f from"../../ui/legacy/components/utils/utils.js";import*as m from"../../core/platform/platform.js";class C extends t.Issue.Issue{#e=new Map;#t=new Map;#s=new Map;#i=new Map;#n=new Set;#r=new Map;#a=new Set;#o=new Set;#d=new Set;#l=new Set;#c="Improvement";#u=new Set;#p=new Set;#h=new Set;#g=new Set;#f=new Set;#m=new Set;#C=new Set;#I;#w=0;#k;constructor(e,t){super(e),this.#k=t}primaryKey(){throw new Error("This should never be called")}aggregationKey(){return this.#k}getBlockedByResponseDetails(){return this.#r.values()}cookies(){return Array.from(this.#e.values()).map((e=>e.cookie))}getRawCookieLines(){return this.#t.values()}sources(){return this.#i.values()}getBounceTrackingSites(){return this.#a.values()}cookiesWithRequestIndicator(){return this.#e.values()}getHeavyAdIssues(){return this.#n}getCookieDeprecationMetadataIssues(){return this.#p}getMixedContentIssues(){return this.#h}getCorsIssues(){return this.#o}getCspIssues(){return this.#d}getDeprecationIssues(){return this.#l}getLowContrastIssues(){return this.#u}requests(){return this.#s.values()}getSharedArrayBufferIssues(){return this.#g}getQuirksModeIssues(){return this.#f}getAttributionReportingIssues(){return this.#m}getGenericIssues(){return this.#C}getDescription(){return this.#I?this.#I.getDescription():null}getCategory(){return this.#I?this.#I.getCategory():"Other"}getAggregatedIssuesCount(){return this.#w}#y(e){const{domain:t,path:s,name:i}=e;return`${t};${s};${i}`}addInstance(e){this.#w++,this.#I||(this.#I=e),this.#c=t.Issue.unionIssueKind(this.#c,e.getKind());let s=!1;for(const t of e.requests())s=!0,this.#s.has(t.requestId)||this.#s.set(t.requestId,t);for(const t of e.cookies()){const e=this.#y(t);this.#e.has(e)||this.#e.set(e,{cookie:t,hasRequest:s})}for(const t of e.rawCookieLines())this.#t.has(t)||this.#t.set(t,{rawCookieLine:t,hasRequest:s});for(const t of e.trackingSites())this.#a.has(t)||this.#a.add(t);for(const t of e.sources()){const e=JSON.stringify(t);this.#i.has(e)||this.#i.set(e,t)}e instanceof t.CookieDeprecationMetadataIssue.CookieDeprecationMetadataIssue&&this.#p.add(e),e instanceof t.MixedContentIssue.MixedContentIssue&&this.#h.add(e),e instanceof t.HeavyAdIssue.HeavyAdIssue&&this.#n.add(e);for(const t of e.getBlockedByResponseDetails()){const e=JSON.stringify(t,["parentFrame","blockedFrame","requestId","frameId","reason","request"]);this.#r.set(e,t)}e instanceof t.ContentSecurityPolicyIssue.ContentSecurityPolicyIssue&&this.#d.add(e),e instanceof t.DeprecationIssue.DeprecationIssue&&this.#l.add(e),e instanceof t.SharedArrayBufferIssue.SharedArrayBufferIssue&&this.#g.add(e),e instanceof t.LowTextContrastIssue.LowTextContrastIssue&&this.#u.add(e),e instanceof t.CorsIssue.CorsIssue&&this.#o.add(e),e instanceof t.QuirksModeIssue.QuirksModeIssue&&this.#f.add(e),e instanceof t.AttributionReportingIssue.AttributionReportingIssue&&this.#m.add(e),e instanceof t.GenericIssue.GenericIssue&&this.#C.add(e)}getKind(){return this.#c}isHidden(){return this.#I?.isHidden()||!1}setHidden(e){throw new Error("Should not call setHidden on aggregatedIssue")}}class I extends e.ObjectWrapper.ObjectWrapper{issuesManager;#v=new Map;#b=new Map;constructor(e){super(),this.issuesManager=e,this.issuesManager.addEventListener("IssueAdded",this.#R,this),this.issuesManager.addEventListener("FullUpdateRequired",this.#A,this);for(const e of this.issuesManager.issues())this.#S(e)}#R(e){this.#S(e.data.issue)}#A(){this.#v.clear(),this.#b.clear();for(const e of this.issuesManager.issues())this.#S(e);this.dispatchEventToListeners("FullUpdateRequired")}#S(e){const t=e.isHidden()?this.#b:this.#v,s=this.#x(t,e);return this.dispatchEventToListeners("AggregatedIssueUpdated",s),s}#x(e,t){const s=t.code();let i=e.get(s);return i||(i=new C(t.code(),s),e.set(s,i)),i.addInstance(t),i}aggregatedIssues(){return[...this.#v.values(),...this.#b.values()]}hiddenAggregatedIssues(){return this.#b.values()}aggregatedIssueCodes(){return new Set([...this.#v.keys(),...this.#b.keys()])}aggregatedIssueCategories(){const e=new Set;for(const t of this.#v.values())e.add(t.getCategory());return e}aggregatedIssueKinds(){const e=new Set;for(const t of this.#v.values())e.add(t.getKind());return e}numberOfAggregatedIssues(){return this.#v.size}numberOfHiddenAggregatedIssues(){return this.#b.size}keyForIssue(e){return e.code()}}var w=Object.freeze({__proto__:null,AggregatedIssue:C,IssueAggregator:I});const k={hiddenIssues:"Hidden issues",unhideAll:"Unhide all"},y=i.i18n.registerUIStrings("panels/issues/HiddenIssuesRow.ts",k),v=i.i18n.getLocalizedString.bind(void 0,y);class b extends s.TreeOutline.TreeElement{#T;constructor(){super(void 0,!0),this.#T=document.createElement("span"),this.toggleOnClick=!0,this.listItemElement.classList.add("issue-category","hidden-issues"),this.childrenListElement.classList.add("hidden-issues-body"),this.#E()}#E(){const e=s.UIUtils.createTextButton(v(k.unhideAll),(()=>t.IssuesManager.IssuesManager.instance().unhideAllIssues()),{className:"unhide-all-issues-button",jslogContext:"issues.unhide-all-hiddes"}),i=new a.Adorner.Adorner;i.data={name:"countWrapper",content:this.#T},i.classList.add("aggregated-issues-count"),this.#T.textContent="0";const n=document.createElement("div"),r=document.createElement("div");n.classList.add("header"),r.classList.add("title"),r.textContent=v(k.hiddenIssues),n.appendChild(i),n.appendChild(r),n.appendChild(e),this.listItemElement.appendChild(n)}update(e){this.#T.textContent=`${e}`}}const R={hideAllCurrentPageErrors:"Hide all current Page Errors",hideAllCurrentBreakingChanges:"Hide all current Breaking Changes",hideAllCurrentImprovements:"Hide all current Improvements"},A=i.i18n.registerUIStrings("panels/issues/IssueKindView.ts",R),S=i.i18n.getLocalizedString.bind(void 0,A);function x(){return e.Settings.Settings.instance().createSetting("group-issues-by-kind",!1)}class T extends s.TreeOutline.TreeElement{#L;#M;constructor(e){super(void 0,!0),this.#L=e,this.#M=document.createElement("span"),this.toggleOnClick=!0,this.listItemElement.classList.add("issue-kind"),this.listItemElement.classList.add(function(e){switch(e){case"BreakingChange":return"breaking-changes";case"Improvement":return"improvements";case"PageError":return"page-errors"}}(e)),this.childrenListElement.classList.add("issue-kind-body")}getKind(){return this.#L}getHideAllCurrentKindString(){switch(this.#L){case"PageError":return S(R.hideAllCurrentPageErrors);case"Improvement":return S(R.hideAllCurrentImprovements);case"BreakingChange":return S(R.hideAllCurrentBreakingChanges)}}#E(){const e=document.createElement("div");e.classList.add("header");const s=new o.Icon.Icon;s.data=n.IssueCounter.getIssueKindIconData(this.#L),s.classList.add("leading-issue-icon");const i=new a.Adorner.Adorner;i.data={name:"countWrapper",content:this.#M},i.classList.add("aggregated-issues-count"),this.#M.textContent="0";const r=document.createElement("div");r.classList.add("title"),r.textContent=t.Issue.getIssueKindName(this.#L);const l=new d.HideIssuesMenu.HideIssuesMenu;l.classList.add("hide-available-issues"),l.data={menuItemLabel:this.getHideAllCurrentKindString(),menuItemAction:()=>{const e=t.IssuesManager.getHideIssueByCodeSetting(),s=e.get();for(const e of t.IssuesManager.IssuesManager.instance().issues())e.getKind()===this.#L&&(s[e.code()]="Hidden");e.set(s)}},e.appendChild(s),e.appendChild(i),e.appendChild(r),e.appendChild(l),this.listItemElement.appendChild(e)}onattach(){this.#E(),this.expand()}update(e){this.#M.textContent=`${e}`}}const E=new CSSStyleSheet;E.replaceSync(".issues-pane{overflow:hidden}.issues-pane-no-issues{align-items:center;background-color:var(--sys-color-cdt-base-container);display:flex;flex:1 1 auto;font-size:14px;justify-content:center;padding:30px}.issues-toolbar-container{display:flex;flex:none}.issues-toolbar-container > .toolbar{background-color:var(--sys-color-cdt-base-container);border-bottom:1px solid var(--sys-color-divider)}.issues-toolbar-left{flex:1 1 auto}.issues-toolbar-right{padding-right:6px}\n/*# sourceURL=issuesPane.css */\n");const L=new CSSStyleSheet;L.replaceSync(':host,\n.issues{padding:0;overflow:auto}.issues{--issue-indent:8px}.issues li{white-space:normal;align-items:flex-start;min-height:var(--sys-size-13)}.issues .always-parent::before{display:none}.issues li.parent::before{margin-top:10px}.issues .affected-resources li.parent::before{margin-top:3px;margin-right:0}.issue-category,\n.issue-kind,\n.issue{padding:0 8px;padding-left:var(--issue-indent);overflow:hidden;flex:none;transition:background-color 200ms;border:1px solid var(--sys-color-divider);border-width:0 0 1px}.issue-category.hidden-issues.parent.expanded,\n.issue-kind.parent.expanded{border-width:0 0 1px;background-color:var(--sys-color-surface2)}.issue-category + .children .issue,\n.issue.expanded{background:var(--sys-color-cdt-base-container)}.issue.expanded{border-width:0}.issue.selected,\n.issue.expanded.selected{background-color:var(--sys-color-surface2);&:focus{background-color:var(--sys-color-tonal-container)}}.tree-outline li:not(.selected):hover .selection{background-color:unset}.tree-outline li.issue:not(.expanded):not(.selected):hover .selection{background-color:var(--sys-color-state-hover-on-subtle)}.tree-outline li.issue.expanded:not(.selected):hover{background-color:var(--sys-color-state-hover-on-subtle)}.unhide-all-issues-button{margin:0}p{margin-block-start:2px;margin-block-end:2px}.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow) .tree-outline.hide-selection-when-blurred .issue-category.selected:focus-visible,\n.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow) .tree-outline.hide-selection-when-blurred .issue-kind.selected:focus-visible,\n.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow) .tree-outline.hide-selection-when-blurred .issue.selected:focus-visible{width:auto;padding-right:8px}.header{display:flex;flex-direction:row;align-items:flex-start;padding:4px 0;cursor:pointer;width:100%;& > :not(.unhide-all-issues-button){margin-top:var(--sys-size-2)}}.header devtools-hide-issues-menu{visibility:hidden}.header:hover devtools-hide-issues-menu,\n.issue.selected devtools-hide-issues-menu{visibility:visible}.title{flex:1;font-size:12px;color:var(--sys-color-on-surface);font-weight:normal;user-select:text;padding-top:2px}.issue.expanded .title{font-weight:450}.body.children{border-bottom:1px solid var(--sys-color-divider);padding:6px 0;position:relative;padding-left:calc(var(--issue-indent) + 43px);padding-bottom:26px;padding-right:8px}.issue-category + .children,\n.issue-kind + .children{--issue-indent:24px;padding-left:0}.body::before{content:"";display:block;position:absolute;left:calc(var(--issue-indent) + 23px);top:0;bottom:20px;width:2px}.issue-kind-breaking-change.body::before{border-left:2px solid var(--issue-color-yellow)}.issue-kind-page-error.body::before{border-left:2px solid var(--issue-color-red)}.issue-kind-improvement.body::before{border-left:2px solid var(--issue-color-blue)}devtools-icon.leading-issue-icon{margin:1px 0 -1px 7px}.message{line-height:18px;font-size:12px;color:var(--sys-color-token-subtle);margin-bottom:4px;user-select:text}.message p{margin-bottom:16px}.message li{margin-top:8px}.message code{color:var(--sys-color-on-surface);padding:0 2px;font-size:12px;user-select:text;cursor:text;background:var(--sys-color-surface2)}.separator::before{content:"·";padding-left:1ex;padding-right:1ex}.link{font-size:12px;color:var(--sys-color-primary)}.link-wrapper{margin-top:15px;user-select:text}.affected-resources-label,\n.resolutions-label{margin-top:5px;font-size:10px;font-weight:500;letter-spacing:0.06em;text-transform:uppercase;color:var(--sys-color-on-surface);display:inline-block}.link-list{list-style-type:none;list-style-position:inside;padding-inline-start:0}.resolutions-list{list-style-type:none;list-style-position:inside;padding-inline-start:0}.link-list li::before{content:none;mask-image:none}.resolutions-list li::before{content:"→";mask-image:none;padding-right:5px;position:relative;top:-1px}.resolutions-list li{display:list-item}ul > li.plain-enum{display:list-item}ul > li.plain-enum::before{content:"";padding:0;margin:0;max-width:0}.affected-resources-label + .affected-resources{padding:3px 0 0;position:relative;user-select:text}.affected-resource-label{font-size:12px;line-height:18px;color:var(--sys-color-on-surface);position:relative;cursor:pointer}.affected-resource-cookie{font-size:12px;line-height:18px;border:0;border-collapse:collapse}.affected-resource-element{font-size:12px;line-height:18px;color:var(--sys-color-primary);border:0;border-collapse:collapse}.affected-resource-row{font-size:12px;line-height:18px;border:0;border-collapse:collapse;vertical-align:top}.affected-resource-mixed-content{font-size:12px;line-height:18px;border:0;border-collapse:collapse}.affected-resource-heavy-ad{font-size:12px;line-height:18px;border:0;border-collapse:collapse}.affected-resource-request{font-size:12px;line-height:18px;border:0;border-collapse:collapse}.affected-resource-source{font-size:12px;line-height:18px;color:var(--sys-color-primary);border:0;border-collapse:collapse}.affected-resource-list{border-spacing:10px 0;margin-left:-12px}.affected-resource-header{font-size:12px;color:var(--sys-color-on-surface);padding-left:2px}.code-example{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)}.affected-resource-blocked-status{color:var(--issue-color-red)}.affected-resource-report-only-status{color:var(--issue-color-yellow)}.affected-resource-cookie-info{color:var(--sys-color-token-subtle);padding:2px;text-align:right}.affected-resource-cookie-info-header{text-align:right}.affected-resource-mixed-content-info{color:var(--sys-color-token-subtle);padding:2px}.affected-resource-heavy-ad-info{color:var(--sys-color-token-subtle);padding:2px}.affected-resource-heavy-ad-info-frame{display:flex;align-items:center;color:var(--sys-color-token-subtle);padding:2px}.affected-resource-cell{color:var(--sys-color-token-subtle);padding:2px}.affected-resource-cell.link{color:var(--sys-color-primary)}.affected-resource-cell span.icon{margin-right:0.5ex;vertical-align:sub}.affected-resources > .parent{margin-top:0;padding:2px 5px 0}.affected-resources > .parent.expanded{background:var(--sys-color-cdt-base-container)}.affected-resources > .children.expanded{background:var(--sys-color-cdt-base-container);padding:6px 0 9px 5px}.aggregated-issues-count{padding:3px 7px 0}.affected-resource-directive-info-header{text-align:left}.affected-resource-directive{font-size:12px;line-height:18px;border:0;border-collapse:collapse}.affected-resource-directive-info{color:var(--sys-color-token-subtle);padding:2px;text-align:left}.devtools-link{padding-top:4px}devtools-icon.link-icon{vertical-align:sub;margin-right:0.5ch}devtools-icon.elements-panel,\ndevtools-icon.network-panel{margin-right:0.5ex;vertical-align:baseline;height:14px}@media (forced-colors: active){.title{color:ButtonText}.tree-outline:not(.hide-selection-when-blurred) .selected .header .title,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible .header .title{color:HighlightText}}\n/*# sourceURL=issuesTree.css */\n');const M={unknown:"unknown",clickToRevealTheFramesDomNodeIn:"Click to reveal the frame's DOM node in the Elements panel",unavailable:"unavailable"},P=i.i18n.registerUIStrings("panels/issues/AffectedResourcesView.ts",M),D=i.i18n.getLocalizedString.bind(void 0,P);class q extends s.TreeOutline.TreeElement{#P;issue;affectedResourcesCountElement;affectedResources;#D;#q;#O;requestResolver;constructor(e,t,s){super(void 0,void 0,s),this.#P=e,this.issue=t,this.toggleOnClick=!0,this.affectedResourcesCountElement=this.createAffectedResourcesCounter(),this.affectedResources=this.createAffectedResources(),this.#D=0,this.requestResolver=new h.RequestResolver.RequestResolver,this.#q=[],this.#O=new Set}setIssue(e){this.issue=e}createAffectedResourcesCounter(){const e=document.createElement("div");return e.classList.add("affected-resource-label"),this.listItemElement.appendChild(e),e}createAffectedResources(){const e=new s.TreeOutline.TreeElement,t=document.createElement("table");return t.classList.add("affected-resource-list"),e.listItemElement.appendChild(t),this.appendChild(e),t}updateAffectedResourceCount(e){this.#D=e,this.affectedResourcesCountElement.textContent=this.getResourceNameWithCount(e),this.hidden=0===this.#D,this.#P.updateAffectedResourceVisibility()}isEmpty(){return 0===this.#D}clear(){this.affectedResources.textContent="",this.requestResolver.clear()}expandIfOneResource(){1===this.#D&&this.expand()}#V(e){const t=p.FrameManager.FrameManager.instance().getFrame(e);if(!(t&&t.url||(this.#O.add(e),this.#q.length))){const e=p.FrameManager.FrameManager.instance().addEventListener("FrameAddedToTarget",this.#H,this),t=p.FrameManager.FrameManager.instance().addEventListener("FrameNavigated",this.#H,this);this.#q=[e,t]}return t}#H(t){const s=t.data.frame;if(!s.url)return;const i=this.#O.delete(s.id);0===this.#O.size&&this.#q.length&&(e.EventTarget.removeEventListeners(this.#q),this.#q=[]),i&&this.update()}createFrameCell(t,s){const i=this.#V(t),n=i&&(i.unreachableUrl()||i.url)||D(M.unknown),r=document.createElement("td");if(r.classList.add("affected-resource-cell"),i){const i=new o.Icon.Icon;i.data={iconName:"code-circle",color:"var(--icon-link)",width:"16px",height:"16px"},i.classList.add("link","elements-panel"),i.onclick=async()=>{l.userMetrics.issuesPanelResourceOpened(s,"Element");const i=p.FrameManager.FrameManager.instance().getFrame(t);if(i){const t=await i.getOwnerDOMNodeOrDocument();t&&e.Revealer.reveal(t)}},i.title=D(M.clickToRevealTheFramesDomNodeIn),r.appendChild(i)}return r.appendChild(document.createTextNode(n)),r.onmouseenter=()=>{const e=p.FrameManager.FrameManager.instance().getFrame(t);e&&e.highlight()},r.onmouseleave=()=>p.OverlayModel.OverlayModel.hideDOMNodeHighlight(),r}createRequestCell(e,t={}){const s=document.createElement("td");s.classList.add("affected-resource-cell");const i=new g.RequestLinkIcon.RequestLinkIcon;return i.data={...t,affectedRequest:e,requestResolver:this.requestResolver,displayURL:!0},s.appendChild(i),s}async createElementCell({backendNodeId:t,nodeName:s,target:i},n){if(!i){const e=document.createElement("td");return e.textContent=s||D(M.unavailable),e}function r(){l.userMetrics.issuesPanelResourceOpened(n,"Element")}const a=new p.DOMModel.DeferredDOMNode(i,t),o=await e.Linkifier.Linkifier.linkify(a);o.textContent=s,o.addEventListener("click",(()=>r())),o.addEventListener("keydown",(e=>{"Enter"===e.key&&r()}));const d=document.createElement("td");return d.classList.add("affected-resource-element","devtools-link"),d.appendChild(o),d}appendSourceLocation(e,t,s){const i=document.createElement("td");if(i.classList.add("affected-source-location"),t){const e=40,n=new f.Linkifier.Linkifier(e).linkifyScriptLocation(s||null,t.scriptId||null,t.url,t.lineNumber,{columnNumber:t.columnNumber,inlineFrameIndex:0});n.setAttribute("jslog",`${r.link("source-location").track({click:!0})}`),i.appendChild(n)}e.appendChild(i)}appendColumnTitle(e,t,s=null){const i=document.createElement("td");i.classList.add("affected-resource-header"),s&&i.classList.add(s),i.textContent=t,e.appendChild(i)}createIssueDetailCell(e,t=null){const s=document.createElement("td");return"string"==typeof e?s.textContent=e:s.appendChild(e),t&&s.classList.add(t),s}appendIssueDetailCell(e,t,s=null){const i=this.createIssueDetailCell(t,s);return e.appendChild(i),i}}const O={nRequests:"{n, plural, =1 {# request} other {# requests}}",requestC:"Request",parentFrame:"Parent Frame",blockedResource:"Blocked Resource"},V=i.i18n.registerUIStrings("panels/issues/AffectedBlockedByResponseView.ts",O),H=i.i18n.getLocalizedString.bind(void 0,V);class U extends q{#U(e){const t=document.createElement("tr");this.appendColumnTitle(t,H(O.requestC)),this.appendColumnTitle(t,H(O.parentFrame)),this.appendColumnTitle(t,H(O.blockedResource)),this.affectedResources.appendChild(t);let s=0;for(const t of e)this.#N(t),s++;this.updateAffectedResourceCount(s)}getResourceNameWithCount(e){return H(O.nRequests,{n:e})}#N(e){const t=document.createElement("tr");t.classList.add("affected-resource-row");const s=this.createRequestCell(e.request,{additionalOnClickAction(){l.userMetrics.issuesPanelResourceOpened("CrossOriginEmbedderPolicy","Request")}});if(t.appendChild(s),e.parentFrame){const s=this.createFrameCell(e.parentFrame.frameId,this.issue.getCategory());t.appendChild(s)}else t.appendChild(document.createElement("td"));if(e.blockedFrame){const s=this.createFrameCell(e.blockedFrame.frameId,this.issue.getCategory());t.appendChild(s)}else t.appendChild(document.createElement("td"));this.affectedResources.appendChild(t)}update(){this.clear(),this.#U(this.issue.getBlockedByResponseDetails())}}const N={nCookies:"{n, plural, =1 {# cookie} other {# cookies}}",name:"Name",domain:"Domain",path:"Path",nRawCookieLines:"{n, plural, =1 {1 Raw `Set-Cookie` header} other {# Raw `Set-Cookie` headers}}",filterSetCookieTitle:"Show network requests that include this `Set-Cookie` header in the network panel"},F=i.i18n.registerUIStrings("panels/issues/AffectedCookiesView.ts",N),B=i.i18n.getLocalizedString.bind(void 0,F);class K extends q{getResourceNameWithCount(e){return B(N.nCookies,{n:e})}#F(e){const t=document.createElement("tr");this.appendColumnTitle(t,B(N.name)),this.appendColumnTitle(t,B(N.domain)+" & "+B(N.path),"affected-resource-cookie-info-header"),this.affectedResources.appendChild(t);let s=0;for(const t of e)s++,this.#B(t.cookie,t.hasRequest);this.updateAffectedResourceCount(s)}#B(t,s){const i=document.createElement("tr");i.classList.add("affected-resource-cookie");const n=document.createElement("td");if(s){const s=document.createElement("button");s.classList.add("link","devtools-link"),s.textContent=t.name,s.tabIndex=0,s.setAttribute("jslog",`${r.link("issues.filter-network-requests-by-cookie").track({click:!0})}`),s.addEventListener("click",(()=>{l.userMetrics.issuesPanelResourceOpened(this.issue.getCategory(),"Cookie"),e.Revealer.reveal(c.UIFilter.UIRequestFilter.filters([{filterType:c.UIFilter.FilterType.CookieDomain,filterValue:t.domain},{filterType:c.UIFilter.FilterType.CookieName,filterValue:t.name},{filterType:c.UIFilter.FilterType.CookiePath,filterValue:t.path}]))})),n.appendChild(s)}else n.textContent=t.name;i.appendChild(n),this.appendIssueDetailCell(i,`${t.domain}${t.path}`,"affected-resource-cookie-info"),this.affectedResources.appendChild(i)}update(){this.clear(),this.#F(this.issue.cookiesWithRequestIndicator())}}class z extends q{getResourceNameWithCount(e){return B(N.nRawCookieLines,{n:e})}update(){this.clear();const t=this.issue.getRawCookieLines();let s=0;for(const i of t){const t=document.createElement("tr");if(t.classList.add("affected-resource-directive"),i.hasRequest){const s=document.createElement("td"),n=document.createElement("button");n.classList.add("link","devtools-link"),n.textContent=i.rawCookieLine,n.title=B(N.filterSetCookieTitle),n.tabIndex=0,n.setAttribute("jslog",`${r.link("issues.filter-network-requests-by-raw-cookie").track({click:!0})}`),n.addEventListener("click",(()=>{e.Revealer.reveal(c.UIFilter.UIRequestFilter.filters([{filterType:c.UIFilter.FilterType.ResponseHeaderValueSetCookie,filterValue:i.rawCookieLine}]))})),s.appendChild(n),t.appendChild(s)}else this.appendIssueDetailCell(t,i.rawCookieLine);this.affectedResources.appendChild(t),s++}this.updateAffectedResourceCount(s)}}const W={nDirectives:"{n, plural, =1 {# directive} other {# directives}}",reportonly:"report-only",blocked:"blocked",clickToRevealTheViolatingDomNode:"Click to reveal the violating DOM node in the Elements panel",directiveC:"Directive",element:"Element",sourceLocation:"Source location",status:"Status",resourceC:"Resource"},j=i.i18n.registerUIStrings("panels/issues/AffectedDirectivesView.ts",W),_=i.i18n.getLocalizedString.bind(void 0,j);class $ extends q{#K(e,t){const s=document.createElement("td");t?(s.classList.add("affected-resource-report-only-status"),s.textContent=_(W.reportonly)):(s.classList.add("affected-resource-blocked-status"),s.textContent=_(W.blocked)),e.appendChild(s)}getResourceNameWithCount(e){return _(W.nDirectives,{n:e})}#z(e,t){const s=document.createElement("td");s.textContent=t,e.appendChild(s)}#W(e,t){const s=document.createElement("td");s.classList.add("affected-resource-directive-info"),s.textContent=t,e.appendChild(s)}#j(t,s,i){const n=new d.ElementsPanelLink.ElementsPanelLink;if(s){const t=s;n.title=_(W.clickToRevealTheViolatingDomNode);const r=()=>{const s=i.getTargetIfNotDisposed();if(s){l.userMetrics.issuesPanelResourceOpened(this.issue.getCategory(),"Element");const i=new p.DOMModel.DeferredDOMNode(s,t);e.Revealer.reveal(i)}},a=()=>{const e=i.getTargetIfNotDisposed();if(e){const s=new p.DOMModel.DeferredDOMNode(e,t);s&&s.highlight()}},o=()=>{p.OverlayModel.OverlayModel.hideDOMNodeHighlight()};n.data={onElementRevealIconClick:r,onElementRevealIconMouseEnter:a,onElementRevealIconMouseLeave:o}}const r=document.createElement("td");r.classList.add("affected-resource-csp-info-node"),r.appendChild(n),t.appendChild(r)}#_(e){const s=document.createElement("tr");if(this.issue.code()===t.ContentSecurityPolicyIssue.inlineViolationCode)this.appendColumnTitle(s,_(W.directiveC)),this.appendColumnTitle(s,_(W.element)),this.appendColumnTitle(s,_(W.sourceLocation)),this.appendColumnTitle(s,_(W.status));else if(this.issue.code()===t.ContentSecurityPolicyIssue.urlViolationCode)this.appendColumnTitle(s,_(W.resourceC),"affected-resource-directive-info-header"),this.appendColumnTitle(s,_(W.status)),this.appendColumnTitle(s,_(W.directiveC)),this.appendColumnTitle(s,_(W.sourceLocation));else if(this.issue.code()===t.ContentSecurityPolicyIssue.evalViolationCode)this.appendColumnTitle(s,_(W.sourceLocation)),this.appendColumnTitle(s,_(W.directiveC)),this.appendColumnTitle(s,_(W.status));else if(this.issue.code()===t.ContentSecurityPolicyIssue.trustedTypesSinkViolationCode)this.appendColumnTitle(s,_(W.sourceLocation)),this.appendColumnTitle(s,_(W.status));else{if(this.issue.code()!==t.ContentSecurityPolicyIssue.trustedTypesPolicyViolationCode)return void this.updateAffectedResourceCount(0);this.appendColumnTitle(s,_(W.sourceLocation)),this.appendColumnTitle(s,_(W.directiveC)),this.appendColumnTitle(s,_(W.status))}this.affectedResources.appendChild(s);let i=0;for(const t of e)i++,this.#$(t);this.updateAffectedResourceCount(i)}#$(e){const s=document.createElement("tr");s.classList.add("affected-resource-directive");const i=e.details(),n=t.Issue.toZeroBasedLocation(i.sourceCodeLocation),r=e.model(),a=e.model()?.getTargetIfNotDisposed();if(this.issue.code()===t.ContentSecurityPolicyIssue.inlineViolationCode&&r)this.#z(s,i.violatedDirective),this.#j(s,i.violatingNodeId,r),this.appendSourceLocation(s,n,a),this.#K(s,i.isReportOnly);else if(this.issue.code()===t.ContentSecurityPolicyIssue.urlViolationCode){const e=i.blockedURL?i.blockedURL:m.DevToolsPath.EmptyUrlString;this.#W(s,e),this.#K(s,i.isReportOnly),this.#z(s,i.violatedDirective),this.appendSourceLocation(s,n,a)}else if(this.issue.code()===t.ContentSecurityPolicyIssue.evalViolationCode)this.appendSourceLocation(s,n,a),this.#z(s,i.violatedDirective),this.#K(s,i.isReportOnly);else if(this.issue.code()===t.ContentSecurityPolicyIssue.trustedTypesSinkViolationCode)this.appendSourceLocation(s,n,a),this.#K(s,i.isReportOnly);else{if(this.issue.code()!==t.ContentSecurityPolicyIssue.trustedTypesPolicyViolationCode)return;this.appendSourceLocation(s,n,a),this.#z(s,i.violatedDirective),this.#K(s,i.isReportOnly)}this.affectedResources.appendChild(s)}update(){this.clear(),this.#_(this.issue.getCspIssues())}}const Q={nElements:"{n, plural, =1 {# element} other {# elements}}"},G=i.i18n.registerUIStrings("panels/issues/AffectedElementsView.ts",Q),J=i.i18n.getLocalizedString.bind(void 0,G);class X extends q{async#Q(e){let t=0;for(const s of e)await this.#G(s),t++;this.updateAffectedResourceCount(t)}getResourceNameWithCount(e){return J(Q.nElements,{n:e})}async#G(e){const t=await this.createElementCell(e,this.issue.getCategory()),s=document.createElement("tr");s.appendChild(t),this.affectedResources.appendChild(s)}update(){this.clear(),this.#Q(this.issue.elements())}}const Z={nDocuments:"{n, plural, =1 { document} other { documents}}",documentInTheDOMTree:"Document in the DOM tree",url:"URL",mode:"Mode"},Y=i.i18n.registerUIStrings("panels/issues/AffectedDocumentsInQuirksModeView.ts",Z),ee=i.i18n.getLocalizedString.bind(void 0,Y);class te extends X{#J=Promise.resolve();update(){this.#J=this.#J.then(this.#X.bind(this))}getResourceName(e){return ee(Z.nDocuments,{n:e})}async#X(){this.clear(),await this.#Z(this.issue.getQuirksModeIssues())}async#Y(e){const t=document.createElement("tr");t.classList.add("affected-resource-quirks-mode");const s=e.details(),i=p.FrameManager.FrameManager.instance().getFrame(s.frameId)?.resourceTreeModel().target()||null;t.appendChild(await this.createElementCell({nodeName:"document",backendNodeId:s.documentNodeId,target:i},e.getCategory())),this.appendIssueDetailCell(t,s.isLimitedQuirksMode?"Limited Quirks Mode":"Quirks Mode"),this.appendIssueDetailCell(t,s.url),this.affectedResources.appendChild(t)}async#Z(e){const t=document.createElement("tr");this.appendColumnTitle(t,ee(Z.documentInTheDOMTree)),this.appendColumnTitle(t,ee(Z.mode)),this.appendColumnTitle(t,ee(Z.url)),this.affectedResources.appendChild(t);let s=0;for(const t of e)s++,await this.#Y(t);this.updateAffectedResourceCount(s)}}class se extends X{#J=Promise.resolve();update(){this.#J=this.#J.then(this.#X.bind(this))}async#X(){this.clear(),await this.#ee(this.issue.getLowContrastIssues())}async#te(e){const t=document.createElement("tr");t.classList.add("affected-resource-low-contrast");const s=e.details(),i=e.model()?.target()||null;t.appendChild(await this.createElementCell({nodeName:s.violatingNodeSelector,backendNodeId:s.violatingNodeId,target:i},e.getCategory())),this.appendIssueDetailCell(t,String(m.NumberUtilities.floor(s.contrastRatio,2))),this.appendIssueDetailCell(t,String(s.thresholdAA)),this.appendIssueDetailCell(t,String(s.thresholdAAA)),this.appendIssueDetailCell(t,s.fontSize),this.appendIssueDetailCell(t,s.fontWeight),this.affectedResources.appendChild(t)}async#ee(e){const t=document.createElement("tr");this.appendColumnTitle(t,re(ie.element)),this.appendColumnTitle(t,re(ie.contrastRatio)),this.appendColumnTitle(t,re(ie.minimumAA)),this.appendColumnTitle(t,re(ie.minimumAAA)),this.appendColumnTitle(t,re(ie.textSize)),this.appendColumnTitle(t,re(ie.textWeight)),this.affectedResources.appendChild(t);let s=0;for(const t of e)s++,await this.#te(t);this.updateAffectedResourceCount(s)}}const ie={element:"Element",contrastRatio:"Contrast ratio",minimumAA:"Minimum AA ratio",minimumAAA:"Minimum AAA ratio",textSize:"Text size",textWeight:"Text weight"},ne=i.i18n.registerUIStrings("panels/issues/AffectedElementsWithLowContrastView.ts",ie),re=i.i18n.getLocalizedString.bind(void 0,ne),ae={nResources:"{n, plural, =1 {# resource} other {# resources}}",limitExceeded:"Limit exceeded",resolutionStatus:"Resolution Status",frameUrl:"Frame URL",removed:"Removed",warned:"Warned",cpuPeakLimit:"CPU peak limit",cpuTotalLimit:"CPU total limit",networkLimit:"Network limit"},oe=i.i18n.registerUIStrings("panels/issues/AffectedHeavyAdView.ts",ae),de=i.i18n.getLocalizedString.bind(void 0,oe);class le extends q{#se(e){const t=document.createElement("tr");this.appendColumnTitle(t,de(ae.limitExceeded)),this.appendColumnTitle(t,de(ae.resolutionStatus)),this.appendColumnTitle(t,de(ae.frameUrl)),this.affectedResources.appendChild(t);let s=0;for(const t of e)this.#ie(t.details()),s++;this.updateAffectedResourceCount(s)}getResourceNameWithCount(e){return de(ae.nResources,{n:e})}#ne(e){switch(e){case"HeavyAdBlocked":return de(ae.removed);case"HeavyAdWarning":return de(ae.warned)}return""}#re(e){switch(e){case"CpuPeakLimit":return de(ae.cpuPeakLimit);case"CpuTotalLimit":return de(ae.cpuTotalLimit);case"NetworkTotalLimit":return de(ae.networkLimit)}return""}#ie(e){const t=document.createElement("tr");t.classList.add("affected-resource-heavy-ad");const s=document.createElement("td");s.classList.add("affected-resource-heavy-ad-info"),s.textContent=this.#re(e.reason),t.appendChild(s);const i=document.createElement("td");i.classList.add("affected-resource-heavy-ad-info"),i.textContent=this.#ne(e.resolution),t.appendChild(i);const n=e.frame.frameId,r=this.createFrameCell(n,this.issue.getCategory());t.appendChild(r),this.affectedResources.appendChild(t)}update(){this.clear(),this.#se(this.issue.getHeavyAdIssues())}}const ce={nAllowedSites:"{n, plural, =1 {1 website allowed to access cookies} other {# websites allowed to access cookies}}"},ue=i.i18n.registerUIStrings("panels/issues/AffectedMetadataAllowedSitesView.ts",ce),pe=i.i18n.getLocalizedString.bind(void 0,ue);class he extends q{getResourceNameWithCount(e){return pe(ce.nAllowedSites,{n:e})}update(){this.clear();const e=this.issue.getCookieDeprecationMetadataIssues();let t=0;for(const i of e){const e=document.createElement("tr");e.classList.add("affected-resource-directive");const n=document.createElement("div"),r=document.createElement("span");if(r.textContent=i.details().allowedSites.join(", "),n.appendChild(r),!i.details().isOptOutTopLevel&&i.details().optOutPercentage>0){const e=document.createElement("span");e.textContent=" (opt-out: "+i.details().optOutPercentage+"% - ",n.appendChild(e);const t=s.XLink.XLink.create("https://developers.google.com/privacy-sandbox/blog/grace-period-opt-out","learn more");n.appendChild(t);const r=document.createElement("span");r.textContent=")",n.appendChild(r)}this.appendIssueDetailCell(e,n),this.affectedResources.appendChild(e),t++}this.updateAffectedResourceCount(t)}}const ge={nViolations:"{n, plural, =1 {# violation} other {# violations}}",warning:"warning",blocked:"blocked",instantiation:"Instantiation",aSharedarraybufferWas:"A `SharedArrayBuffer` was instantiated in a context that is not cross-origin isolated",transfer:"Transfer",sharedarraybufferWasTransferedTo:"`SharedArrayBuffer` was transfered to a context that is not cross-origin isolated",sourceLocation:"Source Location",trigger:"Trigger",status:"Status"},fe=i.i18n.registerUIStrings("panels/issues/AffectedSharedArrayBufferIssueDetailsView.ts",ge),me=i.i18n.getLocalizedString.bind(void 0,fe);class Ce extends q{getResourceNameWithCount(e){return me(ge.nViolations,{n:e})}#K(e,t){const s=document.createElement("td");t?(s.classList.add("affected-resource-report-only-status"),s.textContent=me(ge.warning)):(s.classList.add("affected-resource-blocked-status"),s.textContent=me(ge.blocked)),e.appendChild(s)}#ae(e,t){const s=document.createElement("td");switch(t){case"CreationIssue":s.textContent=me(ge.instantiation),s.title=me(ge.aSharedarraybufferWas);break;case"TransferIssue":s.textContent=me(ge.transfer),s.title=me(ge.sharedarraybufferWasTransferedTo)}e.appendChild(s)}#U(e){const t=document.createElement("tr");this.appendColumnTitle(t,me(ge.sourceLocation)),this.appendColumnTitle(t,me(ge.trigger)),this.appendColumnTitle(t,me(ge.status)),this.affectedResources.appendChild(t);let s=0;for(const t of e)s++,this.#N(t);this.updateAffectedResourceCount(s)}#N(e){const s=document.createElement("tr");s.classList.add("affected-resource-directive");const i=e.details(),n=t.Issue.toZeroBasedLocation(i.sourceCodeLocation);this.appendSourceLocation(s,n,e.model()?.getTargetIfNotDisposed()),this.#ae(s,i.type),this.#K(s,i.isWarning),this.affectedResources.appendChild(s)}update(){this.clear(),this.#U(this.issue.getSharedArrayBufferIssues())}}const Ie={nSources:"{n, plural, =1 {# source} other {# sources}}"},we=i.i18n.registerUIStrings("panels/issues/AffectedSourcesView.ts",Ie),ke=i.i18n.getLocalizedString.bind(void 0,we);class ye extends q{#oe(e){let t=0;for(const s of e)this.#de(s),t++;this.updateAffectedResourceCount(t)}getResourceNameWithCount(e){return ke(Ie.nSources,{n:e})}#de({url:e,lineNumber:t,columnNumber:s}){const i=document.createElement("td"),n={columnNumber:s,lineNumber:t,tabStop:!0,showColumnNumber:!1,inlineFrameIndex:0},a=f.Linkifier.Linkifier.linkifyURL(e,n);a.setAttribute("jslog",`${r.link("source-location").track({click:!0})}`),i.appendChild(a);const o=document.createElement("tr");o.classList.add("affected-resource-source"),o.appendChild(i),this.affectedResources.appendChild(o)}update(){this.clear(),this.#oe(this.issue.sources())}}const ve={nTrackingSites:"{n, plural, =1 {1 potentially tracking website} other {# potentially tracking websites}}"},be=i.i18n.registerUIStrings("panels/issues/AffectedTrackingSitesView.ts",ve),Re=i.i18n.getLocalizedString.bind(void 0,be);class Ae extends q{getResourceNameWithCount(e){return Re(ve.nTrackingSites,{n:e})}update(){this.clear();const e=this.issue.getBounceTrackingSites();let t=0;for(const s of e){const e=document.createElement("tr");e.classList.add("affected-resource-directive"),this.appendIssueDetailCell(e,s),this.affectedResources.appendChild(e),t++}this.updateAffectedResourceCount(t)}}const Se={nViolations:"{n, plural, =1 {# violation} other {# violations}}",element:"Element",invalidHeaderValue:"Invalid Header Value",request:"Request",untrustworthyOrigin:"Untrustworthy origin"},xe=i.i18n.registerUIStrings("panels/issues/AttributionReportingIssueDetailsView.ts",Se),Te=i.i18n.getLocalizedString.bind(void 0,xe);class Ee extends q{getResourceNameWithCount(e){return Te(Se.nViolations,{n:e})}update(){this.clear();const e=this.issue.getAttributionReportingIssues();e.size>0?this.#U(e.values().next().value.code(),e):this.updateAffectedResourceCount(0)}#U(e,t){const s=document.createElement("tr");switch(e){case"AttributionReportingIssue::InvalidRegisterSourceHeader":case"AttributionReportingIssue::InvalidRegisterTriggerHeader":case"AttributionReportingIssue::InvalidRegisterOsSourceHeader":case"AttributionReportingIssue::InvalidRegisterOsTriggerHeader":case"AttributionReportingIssue::OsSourceIgnored":case"AttributionReportingIssue::OsTriggerIgnored":case"AttributionReportingIssue::SourceIgnored":case"AttributionReportingIssue::TriggerIgnored":this.appendColumnTitle(s,Te(Se.request)),this.appendColumnTitle(s,Te(Se.invalidHeaderValue));break;case"AttributionReportingIssue::InsecureContext":case"AttributionReportingIssue::UntrustworthyReportingOrigin":this.appendColumnTitle(s,Te(Se.element)),this.appendColumnTitle(s,Te(Se.request)),this.appendColumnTitle(s,Te(Se.untrustworthyOrigin));break;case"AttributionReportingIssue::PermissionPolicyDisabled":this.appendColumnTitle(s,Te(Se.element)),this.appendColumnTitle(s,Te(Se.request));break;case"AttributionReportingIssue::SourceAndTriggerHeaders":case"AttributionReportingIssue::WebAndOsHeaders":this.appendColumnTitle(s,Te(Se.request));break;case"AttributionReportingIssue::NavigationRegistrationWithoutTransientUserActivation":this.appendColumnTitle(s,Te(Se.element))}this.affectedResources.appendChild(s);let i=0;for(const s of t)i++,this.#N(e,s);this.updateAffectedResourceCount(i)}async#N(e,t){const s=document.createElement("tr");s.classList.add("affected-resource-directive");const i=t.issueDetails;switch(e){case"AttributionReportingIssue::InvalidRegisterSourceHeader":case"AttributionReportingIssue::InvalidRegisterTriggerHeader":case"AttributionReportingIssue::InvalidRegisterOsSourceHeader":case"AttributionReportingIssue::InvalidRegisterOsTriggerHeader":case"AttributionReportingIssue::OsSourceIgnored":case"AttributionReportingIssue::OsTriggerIgnored":case"AttributionReportingIssue::SourceIgnored":case"AttributionReportingIssue::TriggerIgnored":this.#le(s,i.request),this.appendIssueDetailCell(s,i.invalidParameter||"");break;case"AttributionReportingIssue::InsecureContext":case"AttributionReportingIssue::UntrustworthyReportingOrigin":await this.#ce(s,t),this.#le(s,i.request),this.appendIssueDetailCell(s,i.invalidParameter||"");break;case"AttributionReportingIssue::PermissionPolicyDisabled":await this.#ce(s,t),this.#le(s,i.request);break;case"AttributionReportingIssue::SourceAndTriggerHeaders":case"AttributionReportingIssue::WebAndOsHeaders":this.#le(s,i.request);break;case"AttributionReportingIssue::NavigationRegistrationWithoutTransientUserActivation":await this.#ce(s,t)}this.affectedResources.appendChild(s)}async#ce(e,t){const s=t.issueDetails;if(void 0!==s.violatingNodeId){const i=t.model()?.target()||null;e.appendChild(await this.createElementCell({backendNodeId:s.violatingNodeId,target:i,nodeName:"Attribution source element"},t.getCategory()))}else this.appendIssueDetailCell(e,"")}#le(e,t){if(!t)return void this.appendIssueDetailCell(e,"");const s={additionalOnClickAction(){l.userMetrics.issuesPanelResourceOpened("AttributionReporting","Request")}};e.appendChild(this.createRequestCell(t,s))}}const Le={nRequests:"{n, plural, =1 {# request} other {# requests}}",warning:"warning",blocked:"blocked",status:"Status",request:"Request",resourceAddressSpace:"Resource Address",initiatorAddressSpace:"Initiator Address",secure:"secure",insecure:"insecure",initiatorContext:"Initiator Context",preflightRequestIfProblematic:"Preflight Request (if problematic)",preflightRequest:"Preflight Request",header:"Header",problem:"Problem",invalidValue:"Invalid Value (if available)",problemMissingHeader:"Missing Header",problemMultipleValues:"Multiple Values",problemInvalidValue:"Invalid Value",preflightDisallowedRedirect:"Response to preflight was a redirect",preflightInvalidStatus:"HTTP status of preflight request didn't indicate success",allowedOrigin:"Allowed Origin (from header)",allowCredentialsValueFromHeader:"`Access-Control-Allow-Credentials` Header Value",disallowedRequestMethod:"Disallowed Request Method",disallowedRequestHeader:"Disallowed Request Header",sourceLocation:"Source Location",unsupportedScheme:"Unsupported Scheme",failedRequest:"Failed Request"},Me=i.i18n.registerUIStrings("panels/issues/CorsIssueDetailsView.ts",Le),Pe=i.i18n.getLocalizedString.bind(void 0,Me);class De extends q{constructor(e,t,s){super(e,t,s),this.affectedResourcesCountElement.classList.add("cors-issue-affected-resource-label")}#K(e,t){const s=document.createElement("td");t?(s.classList.add("affected-resource-report-only-status"),s.textContent=Pe(Le.warning)):(s.classList.add("affected-resource-blocked-status"),s.textContent=Pe(Le.blocked)),e.appendChild(s)}getResourceNameWithCount(e){return Pe(Le.nRequests,{n:e})}#U(e,t){const s=document.createElement("tr");switch(this.appendColumnTitle(s,Pe(Le.request)),this.appendColumnTitle(s,Pe(Le.status)),e){case"CorsIssue::InvalidHeaders":this.appendColumnTitle(s,Pe(Le.preflightRequestIfProblematic)),this.appendColumnTitle(s,Pe(Le.header)),this.appendColumnTitle(s,Pe(Le.problem)),this.appendColumnTitle(s,Pe(Le.invalidValue));break;case"CorsIssue::WildcardOriginWithCredentials":this.appendColumnTitle(s,Pe(Le.preflightRequestIfProblematic));break;case"CorsIssue::PreflightResponseInvalid":this.appendColumnTitle(s,Pe(Le.preflightRequest)),this.appendColumnTitle(s,Pe(Le.problem));break;case"CorsIssue::OriginMismatch":this.appendColumnTitle(s,Pe(Le.preflightRequestIfProblematic)),this.appendColumnTitle(s,Pe(Le.initiatorContext)),this.appendColumnTitle(s,Pe(Le.allowedOrigin));break;case"CorsIssue::AllowCredentialsRequired":this.appendColumnTitle(s,Pe(Le.preflightRequestIfProblematic)),this.appendColumnTitle(s,Pe(Le.allowCredentialsValueFromHeader));break;case"CorsIssue::InsecurePrivateNetwork":this.appendColumnTitle(s,Pe(Le.resourceAddressSpace)),this.appendColumnTitle(s,Pe(Le.initiatorAddressSpace)),this.appendColumnTitle(s,Pe(Le.initiatorContext));break;case"CorsIssue::PreflightAllowPrivateNetworkError":this.appendColumnTitle(s,Pe(Le.preflightRequest)),this.appendColumnTitle(s,Pe(Le.invalidValue)),this.appendColumnTitle(s,Pe(Le.initiatorAddressSpace)),this.appendColumnTitle(s,Pe(Le.initiatorContext));break;case"CorsIssue::PreflightMissingPrivateNetworkAccessId":case"CorsIssue::PreflightMissingPrivateNetworkAccessName":this.appendColumnTitle(s,Pe(Le.preflightRequest)),this.appendColumnTitle(s,Pe(Le.invalidValue)),this.appendColumnTitle(s,Pe(Le.resourceAddressSpace)),this.appendColumnTitle(s,Pe(Le.initiatorAddressSpace)),this.appendColumnTitle(s,Pe(Le.initiatorContext));break;case"CorsIssue::MethodDisallowedByPreflightResponse":this.appendColumnTitle(s,Pe(Le.preflightRequest)),this.appendColumnTitle(s,Pe(Le.disallowedRequestMethod));break;case"CorsIssue::HeaderDisallowedByPreflightResponse":this.appendColumnTitle(s,Pe(Le.preflightRequest)),this.appendColumnTitle(s,Pe(Le.disallowedRequestHeader));break;case"CorsIssue::RedirectContainsCredentials":break;case"CorsIssue::DisallowedByMode":this.appendColumnTitle(s,Pe(Le.initiatorContext)),this.appendColumnTitle(s,Pe(Le.sourceLocation));break;case"CorsIssue::CorsDisabledScheme":this.appendColumnTitle(s,Pe(Le.initiatorContext)),this.appendColumnTitle(s,Pe(Le.sourceLocation)),this.appendColumnTitle(s,Pe(Le.unsupportedScheme));break;case"CorsIssue::NoCorsRedirectModeNotFollow":this.appendColumnTitle(s,Pe(Le.sourceLocation));break;default:m.assertUnhandled(e)}this.affectedResources.appendChild(s);let i=0;for(const s of t)i++,this.#N(e,s);this.updateAffectedResourceCount(i)}#ue(e,t){void 0!==t?this.appendIssueDetailCell(e,Pe(t?Le.secure:Le.insecure)):this.appendIssueDetailCell(e,"")}static getHeaderFromError(e){switch(e){case"InvalidAllowHeadersPreflightResponse":return"Access-Control-Allow-Headers";case"InvalidAllowMethodsPreflightResponse":case"MethodDisallowedByPreflightResponse":return"Access-Control-Allow-Methods";case"PreflightMissingAllowOriginHeader":case"PreflightMultipleAllowOriginValues":case"PreflightInvalidAllowOriginValue":case"MissingAllowOriginHeader":case"MultipleAllowOriginValues":case"InvalidAllowOriginValue":case"WildcardOriginNotAllowed":case"PreflightWildcardOriginNotAllowed":case"AllowOriginMismatch":case"PreflightAllowOriginMismatch":return"Access-Control-Allow-Origin";case"InvalidAllowCredentials":case"PreflightInvalidAllowCredentials":return"Access-Control-Allow-Credentials";case"PreflightMissingAllowPrivateNetwork":case"PreflightInvalidAllowPrivateNetwork":return"Access-Control-Allow-Private-Network";case"RedirectContainsCredentials":case"PreflightDisallowedRedirect":return"Location";case"PreflightInvalidStatus":return"Status-Code";case"PreflightMissingPrivateNetworkAccessId":return"Private-Network-Access-Id";case"PreflightMissingPrivateNetworkAccessName":return"Private-Network-Access-Name"}return""}static getProblemFromError(e){switch(e.corsError){case"InvalidAllowHeadersPreflightResponse":case"InvalidAllowMethodsPreflightResponse":case"PreflightInvalidAllowOriginValue":case"InvalidAllowOriginValue":return Pe(Le.problemInvalidValue);case"PreflightMultipleAllowOriginValues":case"MultipleAllowOriginValues":return Pe(Le.problemMultipleValues);case"MissingAllowOriginHeader":case"PreflightMissingAllowOriginHeader":return Pe(Le.problemMissingHeader);case"PreflightInvalidStatus":return Pe(Le.preflightInvalidStatus);case"PreflightDisallowedRedirect":return Pe(Le.preflightDisallowedRedirect);case"InvalidResponse":return Pe(Le.failedRequest)}throw new Error("Invalid Argument")}#N(e,t){const s=document.createElement("tr");s.classList.add("affected-resource-directive");const i=t.details(),n=i.corsErrorStatus,r=i.corsErrorStatus.corsError,a={section:"Response",name:De.getHeaderFromError(r)},o={additionalOnClickAction(){l.userMetrics.issuesPanelResourceOpened("Cors","Request")}};switch(e){case"CorsIssue::InvalidHeaders":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),r.includes("Preflight")?s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})):this.appendIssueDetailCell(s,""),this.appendIssueDetailCell(s,De.getHeaderFromError(r),"code-example"),this.appendIssueDetailCell(s,De.getProblemFromError(i.corsErrorStatus)),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter,"code-example");break;case"CorsIssue::WildcardOriginWithCredentials":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),r.includes("Preflight")?s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})):this.appendIssueDetailCell(s,"");break;case"CorsIssue::PreflightResponseInvalid":{s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning);const e="PreflightInvalidStatus"===r?{section:"General",name:"Status-Code"}:a;s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:e})),this.appendIssueDetailCell(s,De.getProblemFromError(i.corsErrorStatus));break}case"CorsIssue::OriginMismatch":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),r.includes("Preflight")?s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})):this.appendIssueDetailCell(s,""),this.appendIssueDetailCell(s,i.initiatorOrigin??"","code-example"),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter,"code-example");break;case"CorsIssue::AllowCredentialsRequired":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),r.includes("Preflight")?s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})):this.appendIssueDetailCell(s,""),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter,"code-example");break;case"CorsIssue::InsecurePrivateNetwork":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),this.appendIssueDetailCell(s,i.resourceIPAddressSpace??""),this.appendIssueDetailCell(s,i.clientSecurityState?.initiatorIPAddressSpace??""),this.#ue(s,i.clientSecurityState?.initiatorIsSecureContext);break;case"CorsIssue::PreflightAllowPrivateNetworkError":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter,"code-example"),this.appendIssueDetailCell(s,i.clientSecurityState?.initiatorIPAddressSpace??""),this.#ue(s,i.clientSecurityState?.initiatorIsSecureContext);break;case"CorsIssue::MethodDisallowedByPreflightResponse":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter,"code-example");break;case"CorsIssue::HeaderDisallowedByPreflightResponse":s.appendChild(this.createRequestCell(i.request,{...o,highlightHeader:{section:"Request",name:n.failedParameter}})),this.#K(s,i.isWarning),s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:{section:"Response",name:"Access-Control-Allow-Headers"}})),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter,"code-example");break;case"CorsIssue::RedirectContainsCredentials":s.appendChild(this.createRequestCell(i.request,{...o,highlightHeader:{section:"Response",name:De.getHeaderFromError(r)}})),this.#K(s,i.isWarning);break;case"CorsIssue::DisallowedByMode":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),this.appendIssueDetailCell(s,i.initiatorOrigin??"","code-example"),this.appendSourceLocation(s,i.location,t.model()?.getTargetIfNotDisposed());break;case"CorsIssue::CorsDisabledScheme":s.appendChild(this.createRequestCell(i.request,{...o,highlightHeader:{section:"Response",name:De.getHeaderFromError(r)}})),this.#K(s,i.isWarning),this.appendIssueDetailCell(s,i.initiatorOrigin??"","code-example"),this.appendSourceLocation(s,i.location,t.model()?.getTargetIfNotDisposed()),this.appendIssueDetailCell(s,i.corsErrorStatus.failedParameter??"","code-example");break;case"CorsIssue::NoCorsRedirectModeNotFollow":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),this.appendSourceLocation(s,i.location,t.model()?.getTargetIfNotDisposed());break;case"CorsIssue::PreflightMissingPrivateNetworkAccessId":case"CorsIssue::PreflightMissingPrivateNetworkAccessName":s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),s.appendChild(this.createRequestCell(i.request,{...o,linkToPreflight:!0,highlightHeader:a})),this.appendIssueDetailCell(s,De.getHeaderFromError(r)),this.appendIssueDetailCell(s,i.resourceIPAddressSpace??""),this.appendIssueDetailCell(s,i.clientSecurityState?.initiatorIPAddressSpace??""),this.#ue(s,i.clientSecurityState?.initiatorIsSecureContext);break;default:s.appendChild(this.createRequestCell(i.request,o)),this.#K(s,i.isWarning),m.assertUnhandled(e)}this.affectedResources.appendChild(s)}update(){this.clear();const e=this.issue.getCorsIssues();e.size>0?this.#U(e.values().next().value.code(),e):this.updateAffectedResourceCount(0)}}const qe={nResources:"{n, plural, =1 {# resource} other {# resources}}",frameId:"Frame",violatingNode:"Violating node"},Oe=i.i18n.registerUIStrings("panels/issues/GenericIssueDetailsView.ts",qe),Ve=i.i18n.getLocalizedString.bind(void 0,Oe);class He extends q{getResourceNameWithCount(e){return Ve(qe.nResources,{n:e})}#U(e){const t=document.createElement("tr");e.values().next().value.details().frameId&&this.appendColumnTitle(t,Ve(qe.frameId)),this.affectedResources.appendChild(t);let s=0;for(const t of e){(t.details().frameId||t.details().violatingNodeId)&&(s++,this.#N(t))}this.updateAffectedResourceCount(s)}async#N(e){const t=document.createElement("tr");t.classList.add("affected-resource-directive");const s=e.details();if(s.frameId&&t.appendChild(this.createFrameCell(s.frameId,e.getCategory())),s.violatingNodeId){const i=e.model()?.target()||null;t.appendChild(await this.createElementCell({backendNodeId:s.violatingNodeId,nodeName:this.violatingNodeIdName(s.errorType),target:i},e.getCategory()))}this.affectedResources.appendChild(t)}violatingNodeIdName(e){return"FormLabelForNameError"===e?i.i18n.lockedString("Label"):Ve(qe.violatingNode)}update(){this.clear();const e=this.issue.getGenericIssues();e.size>0?this.#U(e):this.updateAffectedResourceCount(0)}}const Ue={name:"Name",blocked:"blocked",nRequests:"{n, plural, =1 {# request} other {# requests}}",nResources:"{n, plural, =1 {# resource} other {# resources}}",restrictionStatus:"Restriction Status",warned:"Warned",affectedResources:"Affected Resources",learnMoreS:"Learn more: {PH1}",automaticallyUpgraded:"automatically upgraded",hideIssuesLikeThis:"Hide issues like this",unhideIssuesLikeThis:"Unhide issues like this"},Ne=i.i18n.registerUIStrings("panels/issues/IssueView.ts",Ue),Fe=i.i18n.getLocalizedString.bind(void 0,Ne);class Be extends q{#pe(e){let t=0;for(const s of e){const e=document.createElement("tr");e.classList.add("affected-resource-request");const i=this.issue.getCategory(),n=Ke.get(i)||"headers-component";e.appendChild(this.createRequestCell(s,{networkTab:n,additionalOnClickAction(){l.userMetrics.issuesPanelResourceOpened(i,"Request")}})),this.affectedResources.appendChild(e),t++}this.updateAffectedResourceCount(t)}getResourceNameWithCount(e){return Fe(Ue.nRequests,{n:e})}update(){this.clear();for(const e of this.issue.getBlockedByResponseDetails())return void this.updateAffectedResourceCount(0);"MixedContent"!==this.issue.getCategory()?this.#pe(this.issue.requests()):this.updateAffectedResourceCount(0)}}const Ke=new Map([["Cookie","cookies"],["CrossOriginEmbedderPolicy","headers-component"],["MixedContent","headers-component"]]);class ze extends q{#he(e){const t=document.createElement("tr");this.appendColumnTitle(t,Fe(Ue.name)),this.appendColumnTitle(t,Fe(Ue.restrictionStatus)),this.affectedResources.appendChild(t);let s=0;for(const t of e){const e=t.getDetails();this.appendAffectedMixedContent(e),s++}this.updateAffectedResourceCount(s)}getResourceNameWithCount(e){return Fe(Ue.nResources,{n:e})}appendAffectedMixedContent(e){const t=document.createElement("tr");if(t.classList.add("affected-resource-mixed-content"),e.request){const s=Ke.get(this.issue.getCategory())||"headers-component";t.appendChild(this.createRequestCell(e.request,{networkTab:s,additionalOnClickAction(){l.userMetrics.issuesPanelResourceOpened("MixedContent","Request")}}))}else{const i=(s=e.insecureURL,(/[^/]+$/.exec(s)||/[^/]+\/$/.exec(s)||[""])[0]);this.appendIssueDetailCell(t,i,"affected-resource-mixed-content-info").title=e.insecureURL}var s;this.appendIssueDetailCell(t,ze.translateStatus(e.resolutionStatus),"affected-resource-mixed-content-info"),this.affectedResources.appendChild(t)}static translateStatus(e){switch(e){case"MixedContentBlocked":return Fe(Ue.blocked);case"MixedContentAutomaticallyUpgraded":return Fe(Ue.automaticallyUpgraded);case"MixedContentWarning":return Fe(Ue.warned)}}update(){this.clear(),this.#he(this.issue.getMixedContentIssues())}}class We extends s.TreeOutline.TreeElement{#ge;#fe;toggleOnClick;affectedResources;#me;#w;#Ce=null;#Ie;#we;#ke=!0;#ye;#ve=!1;constructor(t,s){super(),this.#ge=t,this.#fe=s,this.#we=new e.Throttler.Throttler(250),this.toggleOnClick=!0,this.listItemElement.classList.add("issue"),this.childrenListElement.classList.add("body"),this.childrenListElement.classList.add(We.getBodyCSSClass(this.#ge.getKind())),this.affectedResources=this.#be(),this.#me=[new K(this,this.#ge,"affected-cookies"),new X(this,this.#ge,"affected-elements"),new Be(this,this.#ge,"affected-requests"),new ze(this,this.#ge,"mixed-content-details"),new ye(this,this.#ge,"affected-sources"),new le(this,this.#ge,"heavy-ad-details"),new $(this,this.#ge,"directives-details"),new U(this,this.#ge,"blocked-by-response-details"),new Ce(this,this.#ge,"sab-details"),new se(this,this.#ge,"low-contrast-details"),new De(this,this.#ge,"cors-details"),new He(this,this.#ge,"generic-details"),new te(this,this.#ge,"affected-documents"),new Ee(this,this.#ge,"attribution-reporting-details"),new z(this,this.#ge,"affected-raw-cookies"),new Ae(this,this.#ge,"tracking-sites-details"),new he(this,this.#ge,"metadata-allowed-sites-details")],this.#ye=new d.HideIssuesMenu.HideIssuesMenu,this.#w=null,this.#Ie=!1}setIssue(e){this.#ge!==e&&(this.#ke=!0),this.#ge=e,this.#me.forEach((t=>t.setIssue(e)))}static getBodyCSSClass(e){switch(e){case"BreakingChange":return"issue-kind-breaking-change";case"PageError":return"issue-kind-page-error";case"Improvement":return"issue-kind-improvement"}}getIssueTitle(){return this.#fe.title}onattach(){this.#ve?this.update():this.createContent()}createContent(){this.#E(),this.#Re(),this.appendChild(this.affectedResources);const e=[];for(const t of this.#me)this.appendAffectedResource(t),t.update(),t.isEmpty()||e.push(t);this.#Ae(e),this.#Se(),this.updateAffectedResourceVisibility(),this.#ve=!0}appendAffectedResource(e){this.affectedResources.appendChild(e)}#Ae(e){for(let t=0;t<e.length;t++){const i=e[t].listItemElement;s.ARIAUtils.setPositionInSet(i,t+1),s.ARIAUtils.setSetSize(i,e.length)}}#E(){const e=document.createElement("div");e.classList.add("header"),this.#Ce=new o.Icon.Icon,this.#Ce.classList.add("leading-issue-icon"),this.#w=document.createElement("span");const t=new a.Adorner.Adorner;t.data={name:"countWrapper",content:this.#w},t.classList.add("aggregated-issues-count"),e.appendChild(this.#Ce),e.appendChild(t);const s=document.createElement("div");s.classList.add("title"),s.textContent=this.#fe.title,e.appendChild(s),this.#ye&&e.appendChild(this.#ye),this.#xe(),this.listItemElement.appendChild(e)}onexpand(){const e=this.#ge.getCategory();if("Cookie"===e){const e=t.CookieIssue.CookieIssue.getSubCategory(this.#ge.code());l.userMetrics.issuesPanelIssueExpanded(e)}else l.userMetrics.issuesPanelIssueExpanded(e);if(this.#ke&&this.#X(),!this.#Ie){this.#Ie=!0;for(const e of this.#me)e.expandIfOneResource()}}#xe(){if(this.#Ce){const e=this.#ge.getKind();this.#Ce.data=n.IssueCounter.getIssueKindIconData(e),this.#Ce.title=t.Issue.getIssueKindDescription(e)}if(this.#w&&(this.#w.textContent=`${this.#ge.getAggregatedIssuesCount()}`),this.listItemElement.classList.toggle("hidden-issue",this.#ge.isHidden()),this.#ye){const e={menuItemLabel:this.#ge.isHidden()?Fe(Ue.unhideIssuesLikeThis):Fe(Ue.hideIssuesLikeThis),menuItemAction:()=>{const e=t.IssuesManager.getHideIssueByCodeSetting(),s=e.get();s[this.#ge.code()]=this.#ge.isHidden()?"Unhidden":"Hidden",e.set(s)}};this.#ye.data=e}}updateAffectedResourceVisibility(){const e=this.#me.every((e=>e.isEmpty()));this.affectedResources.hidden=e}#be(){const e=new s.TreeOutline.TreeElement;return e.setCollapsible(!1),e.setExpandable(!0),e.expand(),e.selectable=!1,e.listItemElement.classList.add("affected-resources-label"),e.listItemElement.textContent=Fe(Ue.affectedResources),e.childrenListElement.classList.add("affected-resources"),s.ARIAUtils.setPositionInSet(e.listItemElement,2),s.ARIAUtils.setSetSize(e.listItemElement,0===this.#fe.links.length?2:3),e}#Re(){const e=new s.TreeOutline.TreeElement;e.setCollapsible(!1),e.selectable=!1;const t=new u.MarkdownView.MarkdownView;t.data={tokens:this.#fe.markdown},e.listItemElement.appendChild(t),s.ARIAUtils.setPositionInSet(e.listItemElement,1),s.ARIAUtils.setSetSize(e.listItemElement,0===this.#fe.links.length?2:3),this.appendChild(e)}#Se(){if(0===this.#fe.links.length)return;const e=new s.TreeOutline.TreeElement;e.setCollapsible(!1),e.listItemElement.classList.add("link-wrapper"),s.ARIAUtils.setPositionInSet(e.listItemElement,3),s.ARIAUtils.setSetSize(e.listItemElement,3);const t=e.listItemElement.createChild("ul","link-list");for(const e of this.#fe.links){const i=s.Fragment.html`<x-link class="link devtools-link" tabindex="0" href=${e.link}>${Fe(Ue.learnMoreS,{PH1:e.linkTitle})}</x-link>`;i.setAttribute("jslog",`${r.link("learn-more").track({click:!0})}`);t.createChild("li").appendChild(i)}this.appendChild(e)}#X(){this.expanded&&(this.#me.forEach((e=>e.update())),this.updateAffectedResourceVisibility()),this.#ke=!this.expanded,this.#xe()}update(){this.#we.schedule((async()=>this.#X()))}clear(){this.#me.forEach((e=>e.clear()))}getIssueKind(){return this.#ge.getKind()}isForHiddenIssue(){return this.#ge.isHidden()}toggle(e){e||void 0===e&&!this.expanded?this.expand():this.collapse()}}var je=Object.freeze({__proto__:null,IssueView:We});const _e={crossOriginEmbedderPolicy:"Cross Origin Embedder Policy",mixedContent:"Mixed Content",samesiteCookie:"SameSite Cookie",heavyAds:"Heavy Ads",contentSecurityPolicy:"Content Security Policy",other:"Other",lowTextContrast:"Low Text Contrast",cors:"Cross Origin Resource Sharing",groupDisplayedIssuesUnder:"Group displayed issues under associated categories",groupByCategory:"Group by category",groupDisplayedIssuesUnderKind:"Group displayed issues as Page errors, Breaking changes and Improvements",groupByKind:"Group by kind",includeCookieIssuesCausedBy:"Include cookie Issues caused by third-party sites",includeThirdpartyCookieIssues:"Include third-party cookie issues",onlyThirdpartyCookieIssues:"Only third-party cookie issues detected so far",noIssuesDetectedSoFar:"No issues detected so far",attributionReporting:"Attribution Reporting `API`",quirksMode:"Quirks Mode",generic:"Generic"},$e=i.i18n.registerUIStrings("panels/issues/IssuesPane.ts",_e),Qe=i.i18n.getLocalizedString.bind(void 0,$e);class Ge extends s.TreeOutline.TreeElement{#Te;constructor(e){super(),this.#Te=e,this.toggleOnClick=!0,this.listItemElement.classList.add("issue-category"),this.childrenListElement.classList.add("issue-category-body")}getCategoryName(){switch(this.#Te){case"CrossOriginEmbedderPolicy":return Qe(_e.crossOriginEmbedderPolicy);case"MixedContent":return Qe(_e.mixedContent);case"Cookie":return Qe(_e.samesiteCookie);case"HeavyAd":return Qe(_e.heavyAds);case"ContentSecurityPolicy":return Qe(_e.contentSecurityPolicy);case"LowTextContrast":return Qe(_e.lowTextContrast);case"Cors":return Qe(_e.cors);case"AttributionReporting":return Qe(_e.attributionReporting);case"QuirksMode":return Qe(_e.quirksMode);case"Generic":return Qe(_e.generic);case"Other":return Qe(_e.other)}}onattach(){this.#E()}#E(){const e=document.createElement("div");e.classList.add("header");const t=document.createElement("div");t.classList.add("title"),t.textContent=this.getCategoryName(),e.appendChild(t),this.listItemElement.appendChild(e)}}function Je(){return e.Settings.Settings.instance().createSetting("group-issues-by-category",!1)}class Xe extends s.Widget.VBox{#Ee;#Le;#Me;#Pe;#De;#qe;#Oe;#Ve;#He;#Ue=Promise.resolve();constructor(){super(!0),this.element.setAttribute("jslog",`${r.panel("issues")}`),this.contentElement.classList.add("issues-pane"),this.#Ee=new Map,this.#Me=new Map,this.#Le=new Map,this.#Pe=null,this.#Ne(),this.#De=new s.TreeOutline.TreeOutlineInShadow,this.#De.setShowSelectionOnKeyboardFocus(!0),this.#De.contentElement.classList.add("issues"),this.contentElement.appendChild(this.#De.element),this.#qe=new b,this.#De.appendChild(this.#qe),this.#Oe=document.createElement("div"),this.#Oe.classList.add("issues-pane-no-issues"),this.contentElement.appendChild(this.#Oe),this.#Ve=t.IssuesManager.IssuesManager.instance(),this.#He=new I(this.#Ve),this.#He.addEventListener("AggregatedIssueUpdated",this.#Fe,this),this.#He.addEventListener("FullUpdateRequired",this.#Be,this),this.#qe.hidden=0===this.#Ve.numberOfHiddenIssues(),this.#Be(),this.#Ve.addEventListener("IssuesCountUpdated",this.#Ke,this)}elementsToRestoreScrollPositionsFor(){return[this.#De.element]}#Ne(){const e=this.contentElement.createChild("div","issues-toolbar-container");e.setAttribute("jslog",`${r.toolbar()}`),new s.Toolbar.Toolbar("issues-toolbar-left",e);const i=new s.Toolbar.Toolbar("issues-toolbar-right",e),a=Je(),o=new s.Toolbar.ToolbarSettingCheckbox(a,Qe(_e.groupDisplayedIssuesUnder),Qe(_e.groupByCategory));o.setVisible(!1),i.appendToolbarItem(o),a.addChangeListener((()=>{this.#ze(!0)}));const d=x(),l=new s.Toolbar.ToolbarSettingCheckbox(d,Qe(_e.groupDisplayedIssuesUnderKind),Qe(_e.groupByKind));i.appendToolbarItem(l),d.addChangeListener((()=>{this.#ze(!0)})),l.setVisible(!0);const c=t.Issue.getShowThirdPartyIssuesSetting();this.#Pe=new s.Toolbar.ToolbarSettingCheckbox(c,Qe(_e.includeCookieIssuesCausedBy),Qe(_e.includeThirdpartyCookieIssues)),i.appendToolbarItem(this.#Pe),this.setDefaultFocusedElement(this.#Pe.inputElement),i.appendSeparator();const u=new n.IssueCounter.IssueCounter;u.data={tooltipCallback:()=>{const e=n.IssueCounter.getIssueCountsEnumeration(t.IssuesManager.IssuesManager.instance(),!1);u.title=e},displayMode:"ShowAlways",issuesManager:t.IssuesManager.IssuesManager.instance()},u.id="console-issues-counter",u.setAttribute("jslog",`${r.counter("issues")}`);const p=new s.Toolbar.ToolbarItem(u);return i.appendToolbarItem(p),{toolbarContainer:e}}#Fe(e){this.#We(e.data)}#We(e){this.#Ue=this.#Ue.then((()=>this.#je(e)))}async#je(e){let i=this.#Le.get(e.aggregationKey());if(i){i.setIssue(e);const t=this.#_e(e);i.parent===t||t instanceof s.TreeOutline.TreeOutline&&i.parent===t.rootElement()||(i.parent?.removeChild(i),this.appendIssueViewToParent(i,t))}else{const s=e.getDescription();if(!s)return void console.warn("Could not find description for issue code:",e.code());const n=await t.MarkdownIssueDescription.createIssueDescriptionFromMarkdown(s);i=new We(e,n),this.#Le.set(e.aggregationKey(),i);const r=this.#_e(e);this.appendIssueViewToParent(i,r)}i.update(),this.#Ke()}appendIssueViewToParent(e,t){t.appendChild(e,((e,t)=>e instanceof b?1:t instanceof b?-1:e instanceof We&&t instanceof We?e.getIssueTitle().localeCompare(t.getIssueTitle()):(console.error("The issues tree should only contain IssueView objects as direct children"),0))),t instanceof s.TreeOutline.TreeElement&&this.#$e(t)}#$e(e){const t=e.childrenListNode.children;let i=0;for(let e=0;e<t.length;e++){const n=t[e];n.classList.contains("issue")&&(s.ARIAUtils.setPositionInSet(n,++i),s.ARIAUtils.setSetSize(n,t.length/2))}}#_e(e){if(e.isHidden())return this.#qe;if(x().get()){const t=e.getKind(),s=this.#Me.get(t);if(s)return s;const i=new T(t);return this.#De.appendChild(i,((e,t)=>e instanceof T&&t instanceof T?function(e,t){return e.getKind()===t.getKind()?0:"PageError"===e.getKind()||"BreakingChange"===e.getKind()&&"Improvement"===t.getKind()?-1:1}(e,t):0)),this.#Me.set(t,i),i}if(Je().get()){const t=e.getCategory(),s=this.#Ee.get(t);if(s)return s;const i=new Ge(t);return this.#De.appendChild(i,((e,t)=>e instanceof Ge&&t instanceof Ge?e.getCategoryName().localeCompare(t.getCategoryName()):0)),this.#Ee.set(t,i),i}return this.#De}#Qe(e,t){for(const[s,i]of Array.from(e.entries()))t?.has(s)||(i.parent&&i.parent.removeChild(i),e.delete(s))}#Be(){this.#ze(!1)}#ze(e){if(this.#Qe(this.#Ee,e?void 0:this.#He.aggregatedIssueCategories()),this.#Qe(this.#Me,e?void 0:this.#He.aggregatedIssueKinds()),this.#Qe(this.#Le,e?void 0:this.#He.aggregatedIssueCodes()),this.#He)for(const e of this.#He.aggregatedIssues())this.#We(e);this.#Ke()}#Ge(){for(const e of this.#Me.values()){const t=this.#Ve.numberOfIssues(e.getKind());e.update(t)}}#Ke(){this.#Je(this.#Ve.numberOfIssues(),this.#Ve.numberOfHiddenIssues()),x().get()&&this.#Ge()}#Je(e,t){if(e>0||t>0){this.#qe.hidden=0===t,this.#qe.update(t),this.#De.element.hidden=!1,this.#Oe.style.display="none";const e=this.#De.firstChild();e&&(e.select(!0),this.setDefaultFocusedElement(e.listItemElement))}else{this.#De.element.hidden=!0,this.#Pe&&this.setDefaultFocusedElement(this.#Pe.inputElement);const e=this.#Ve.numberOfAllStoredIssues()>0;this.#Oe.textContent=Qe(e?_e.onlyThirdpartyCookieIssues:_e.noIssuesDetectedSoFar),this.#Oe.style.display="flex"}}async reveal(e){await this.#Ue;const t=this.#He.keyForIssue(e),s=this.#Le.get(t);if(s){if(s.isForHiddenIssue()&&(this.#qe.expand(),this.#qe.reveal()),x().get()&&!s.isForHiddenIssue()){const e=this.#Me.get(s.getIssueKind());e?.expand(),e?.reveal()}s.expand(),s.reveal(),s.select(!1,!0)}}wasShown(){super.wasShown(),this.#De.registerCSSFiles([L]),this.registerCSSFiles([E])}}var Ze=Object.freeze({__proto__:null,getGroupIssuesByCategorySetting:Je,IssuesPane:Xe});var Ye=Object.freeze({__proto__:null,IssueRevealer:class{async reveal(e){await s.ViewManager.ViewManager.instance().showView("issues-pane");const t=s.ViewManager.ViewManager.instance().view("issues-pane");if(t){const s=await t.widget();if(!(s instanceof Xe))throw new Error("Expected issues pane to be an instance of IssuesPane");await s.reveal(e)}}}});export{w as IssueAggregator,Ye as IssueRevealer,je as IssueView,Ze as IssuesPane};
