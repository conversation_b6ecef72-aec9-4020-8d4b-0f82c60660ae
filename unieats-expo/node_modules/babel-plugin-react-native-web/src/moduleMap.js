// THIS FILE IS AUTOMATICALLY GENERATED. DO NOT EDIT.
module.exports = {
  AccessibilityInfo: true,
  ActivityIndicator: true,
  Alert: true,
  Animated: true,
  AppRegistry: true,
  AppState: true,
  Appearance: true,
  BackHandler: true,
  Button: true,
  CheckBox: true,
  Clipboard: true,
  DeviceEventEmitter: true,
  Dimensions: true,
  Easing: true,
  FlatList: true,
  I18nManager: true,
  Image: true,
  ImageBackground: true,
  InputAccessoryView: true,
  InteractionManager: true,
  Keyboard: true,
  KeyboardAvoidingView: true,
  LayoutAnimation: true,
  Linking: true,
  LogBox: true,
  Modal: true,
  NativeEventEmitter: true,
  NativeModules: true,
  PanResponder: true,
  Picker: true,
  PixelRatio: true,
  Platform: true,
  Pressable: true,
  ProgressBar: true,
  RefreshControl: true,
  SafeAreaView: true,
  ScrollView: true,
  SectionList: true,
  Share: true,
  StatusBar: true,
  StyleSheet: true,
  Switch: true,
  Text: true,
  TextInput: true,
  Touchable: true,
  TouchableHighlight: true,
  TouchableNativeFeedback: true,
  TouchableOpacity: true,
  TouchableWithoutFeedback: true,
  UIManager: true,
  Vibration: true,
  View: true,
  VirtualizedList: true,
  YellowBox: true,
  createElement: true,
  findNodeHandle: true,
  processColor: true,
  render: true,
  unmountComponentAtNode: true,
  useColorScheme: true,
  useLocaleContext: true,
  useWindowDimensions: true
};
