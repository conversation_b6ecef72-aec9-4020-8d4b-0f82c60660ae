const set = require('regenerate')(0x38C, 0x5BE, 0x5C0, 0x5C3, 0x5C6, 0x61B, 0x6DE, 0x6E9, 0x710, 0x7B1, 0x81A, 0x824, 0x828, 0x85E, 0x93B, 0x9B2, 0x9BD, 0x9CE, 0xA03, 0xA5E, 0xA76, 0xA83, 0xAC9, 0xAD0, 0xAF9, 0xB3D, 0xB40, 0xB83, 0xB9C, 0xBBF, 0xBD0, 0xC3D, 0xC5D, 0xCC1, 0xD3D, 0xDBD, 0xE84, 0xEA5, 0xEBD, 0xEC6, 0xF36, 0xF38, 0xF7F, 0xF85, 0x1031, 0x1038, 0x10C7, 0x10CD, 0x1258, 0x12C0, 0x17B6, 0x18AA, 0x1940, 0x1A57, 0x1A61, 0x1BE7, 0x1BEE, 0x1CD3, 0x1CE1, 0x1CFA, 0x1F59, 0x1F5B, 0x1F5D, 0x2D27, 0x2D2D, 0xA673, 0xA7D3, 0xA952, 0xAA4D, 0xAAB1, 0xAAC0, 0xAAC2, 0xFB1D, 0xFB3E, 0xFDCF, 0x101A0, 0x10808, 0x1083C, 0x1093F, 0x10EAD, 0x11000, 0x11075, 0x1112C, 0x111BF, 0x11288, 0x1133D, 0x1133F, 0x11350, 0x1138B, 0x1138E, 0x113B7, 0x113CA, 0x113D1, 0x11445, 0x1145D, 0x114B9, 0x114BE, 0x114C1, 0x115BE, 0x1163E, 0x116AC, 0x1171E, 0x11726, 0x11838, 0x1183B, 0x11909, 0x11A00, 0x11A50, 0x11A97, 0x11C3E, 0x11CA9, 0x11CB1, 0x11CB4, 0x11D46, 0x11D96, 0x11D98, 0x11FB0, 0x16AF5, 0x1B132, 0x1B155, 0x1BC9C, 0x1BC9F, 0x1D245, 0x1D4A2, 0x1D4BB, 0x1D546, 0x1E2FF, 0x1E5FF, 0x1E94B, 0x1EE24, 0x1EE27, 0x1EE39, 0x1EE3B, 0x1EE42, 0x1EE47, 0x1EE49, 0x1EE4B, 0x1EE54, 0x1EE57, 0x1EE59, 0x1EE5B, 0x1EE5D, 0x1EE5F, 0x1EE64, 0x1EE7E, 0x1F7F0);
set.addRange(0x20, 0x7E).addRange(0xA0, 0xAC).addRange(0xAE, 0x2FF).addRange(0x370, 0x377).addRange(0x37A, 0x37F).addRange(0x384, 0x38A).addRange(0x38E, 0x3A1).addRange(0x3A3, 0x482).addRange(0x48A, 0x52F).addRange(0x531, 0x556).addRange(0x559, 0x58A).addRange(0x58D, 0x58F).addRange(0x5D0, 0x5EA).addRange(0x5EF, 0x5F4).addRange(0x606, 0x60F).addRange(0x61D, 0x64A).addRange(0x660, 0x66F).addRange(0x671, 0x6D5).addRange(0x6E5, 0x6E6).addRange(0x6EE, 0x70D).addRange(0x712, 0x72F).addRange(0x74D, 0x7A5).addRange(0x7C0, 0x7EA).addRange(0x7F4, 0x7FA).addRange(0x7FE, 0x815).addRange(0x830, 0x83E).addRange(0x840, 0x858).addRange(0x860, 0x86A).addRange(0x870, 0x88E).addRange(0x8A0, 0x8C9).addRange(0x903, 0x939).addRange(0x93D, 0x940).addRange(0x949, 0x94C).addRange(0x94E, 0x950).addRange(0x958, 0x961).addRange(0x964, 0x980).addRange(0x982, 0x983).addRange(0x985, 0x98C).addRange(0x98F, 0x990).addRange(0x993, 0x9A8).addRange(0x9AA, 0x9B0).addRange(0x9B6, 0x9B9).addRange(0x9BF, 0x9C0).addRange(0x9C7, 0x9C8).addRange(0x9CB, 0x9CC).addRange(0x9DC, 0x9DD).addRange(0x9DF, 0x9E1).addRange(0x9E6, 0x9FD).addRange(0xA05, 0xA0A).addRange(0xA0F, 0xA10).addRange(0xA13, 0xA28);
set.addRange(0xA2A, 0xA30).addRange(0xA32, 0xA33).addRange(0xA35, 0xA36).addRange(0xA38, 0xA39).addRange(0xA3E, 0xA40).addRange(0xA59, 0xA5C).addRange(0xA66, 0xA6F).addRange(0xA72, 0xA74).addRange(0xA85, 0xA8D).addRange(0xA8F, 0xA91).addRange(0xA93, 0xAA8).addRange(0xAAA, 0xAB0).addRange(0xAB2, 0xAB3).addRange(0xAB5, 0xAB9).addRange(0xABD, 0xAC0).addRange(0xACB, 0xACC).addRange(0xAE0, 0xAE1).addRange(0xAE6, 0xAF1).addRange(0xB02, 0xB03).addRange(0xB05, 0xB0C).addRange(0xB0F, 0xB10).addRange(0xB13, 0xB28).addRange(0xB2A, 0xB30).addRange(0xB32, 0xB33).addRange(0xB35, 0xB39).addRange(0xB47, 0xB48).addRange(0xB4B, 0xB4C).addRange(0xB5C, 0xB5D).addRange(0xB5F, 0xB61).addRange(0xB66, 0xB77).addRange(0xB85, 0xB8A).addRange(0xB8E, 0xB90).addRange(0xB92, 0xB95).addRange(0xB99, 0xB9A).addRange(0xB9E, 0xB9F).addRange(0xBA3, 0xBA4).addRange(0xBA8, 0xBAA).addRange(0xBAE, 0xBB9).addRange(0xBC1, 0xBC2).addRange(0xBC6, 0xBC8).addRange(0xBCA, 0xBCC).addRange(0xBE6, 0xBFA).addRange(0xC01, 0xC03).addRange(0xC05, 0xC0C).addRange(0xC0E, 0xC10).addRange(0xC12, 0xC28).addRange(0xC2A, 0xC39).addRange(0xC41, 0xC44).addRange(0xC58, 0xC5A).addRange(0xC60, 0xC61).addRange(0xC66, 0xC6F);
set.addRange(0xC77, 0xC80).addRange(0xC82, 0xC8C).addRange(0xC8E, 0xC90).addRange(0xC92, 0xCA8).addRange(0xCAA, 0xCB3).addRange(0xCB5, 0xCB9).addRange(0xCBD, 0xCBE).addRange(0xCC3, 0xCC4).addRange(0xCDD, 0xCDE).addRange(0xCE0, 0xCE1).addRange(0xCE6, 0xCEF).addRange(0xCF1, 0xCF3).addRange(0xD02, 0xD0C).addRange(0xD0E, 0xD10).addRange(0xD12, 0xD3A).addRange(0xD3F, 0xD40).addRange(0xD46, 0xD48).addRange(0xD4A, 0xD4C).addRange(0xD4E, 0xD4F).addRange(0xD54, 0xD56).addRange(0xD58, 0xD61).addRange(0xD66, 0xD7F).addRange(0xD82, 0xD83).addRange(0xD85, 0xD96).addRange(0xD9A, 0xDB1).addRange(0xDB3, 0xDBB).addRange(0xDC0, 0xDC6).addRange(0xDD0, 0xDD1).addRange(0xDD8, 0xDDE).addRange(0xDE6, 0xDEF).addRange(0xDF2, 0xDF4).addRange(0xE01, 0xE30).addRange(0xE32, 0xE33).addRange(0xE3F, 0xE46).addRange(0xE4F, 0xE5B).addRange(0xE81, 0xE82).addRange(0xE86, 0xE8A).addRange(0xE8C, 0xEA3).addRange(0xEA7, 0xEB0).addRange(0xEB2, 0xEB3).addRange(0xEC0, 0xEC4).addRange(0xED0, 0xED9).addRange(0xEDC, 0xEDF).addRange(0xF00, 0xF17).addRange(0xF1A, 0xF34).addRange(0xF3A, 0xF47).addRange(0xF49, 0xF6C).addRange(0xF88, 0xF8C).addRange(0xFBE, 0xFC5).addRange(0xFC7, 0xFCC).addRange(0xFCE, 0xFDA);
set.addRange(0x1000, 0x102C).addRange(0x103B, 0x103C).addRange(0x103F, 0x1057).addRange(0x105A, 0x105D).addRange(0x1061, 0x1070).addRange(0x1075, 0x1081).addRange(0x1083, 0x1084).addRange(0x1087, 0x108C).addRange(0x108E, 0x109C).addRange(0x109E, 0x10C5).addRange(0x10D0, 0x1248).addRange(0x124A, 0x124D).addRange(0x1250, 0x1256).addRange(0x125A, 0x125D).addRange(0x1260, 0x1288).addRange(0x128A, 0x128D).addRange(0x1290, 0x12B0).addRange(0x12B2, 0x12B5).addRange(0x12B8, 0x12BE).addRange(0x12C2, 0x12C5).addRange(0x12C8, 0x12D6).addRange(0x12D8, 0x1310).addRange(0x1312, 0x1315).addRange(0x1318, 0x135A).addRange(0x1360, 0x137C).addRange(0x1380, 0x1399).addRange(0x13A0, 0x13F5).addRange(0x13F8, 0x13FD).addRange(0x1400, 0x169C).addRange(0x16A0, 0x16F8).addRange(0x1700, 0x1711).addRange(0x171F, 0x1731).addRange(0x1735, 0x1736).addRange(0x1740, 0x1751).addRange(0x1760, 0x176C).addRange(0x176E, 0x1770).addRange(0x1780, 0x17B3).addRange(0x17BE, 0x17C5).addRange(0x17C7, 0x17C8).addRange(0x17D4, 0x17DC).addRange(0x17E0, 0x17E9).addRange(0x17F0, 0x17F9).addRange(0x1800, 0x180A).addRange(0x1810, 0x1819).addRange(0x1820, 0x1878).addRange(0x1880, 0x1884).addRange(0x1887, 0x18A8).addRange(0x18B0, 0x18F5).addRange(0x1900, 0x191E).addRange(0x1923, 0x1926).addRange(0x1929, 0x192B);
set.addRange(0x1930, 0x1931).addRange(0x1933, 0x1938).addRange(0x1944, 0x196D).addRange(0x1970, 0x1974).addRange(0x1980, 0x19AB).addRange(0x19B0, 0x19C9).addRange(0x19D0, 0x19DA).addRange(0x19DE, 0x1A16).addRange(0x1A19, 0x1A1A).addRange(0x1A1E, 0x1A55).addRange(0x1A63, 0x1A64).addRange(0x1A6D, 0x1A72).addRange(0x1A80, 0x1A89).addRange(0x1A90, 0x1A99).addRange(0x1AA0, 0x1AAD).addRange(0x1B04, 0x1B33).addRange(0x1B3E, 0x1B41).addRange(0x1B45, 0x1B4C).addRange(0x1B4E, 0x1B6A).addRange(0x1B74, 0x1B7F).addRange(0x1B82, 0x1BA1).addRange(0x1BA6, 0x1BA7).addRange(0x1BAE, 0x1BE5).addRange(0x1BEA, 0x1BEC).addRange(0x1BFC, 0x1C2B).addRange(0x1C34, 0x1C35).addRange(0x1C3B, 0x1C49).addRange(0x1C4D, 0x1C8A).addRange(0x1C90, 0x1CBA).addRange(0x1CBD, 0x1CC7).addRange(0x1CE9, 0x1CEC).addRange(0x1CEE, 0x1CF3).addRange(0x1CF5, 0x1CF7).addRange(0x1D00, 0x1DBF).addRange(0x1E00, 0x1F15).addRange(0x1F18, 0x1F1D).addRange(0x1F20, 0x1F45).addRange(0x1F48, 0x1F4D).addRange(0x1F50, 0x1F57).addRange(0x1F5F, 0x1F7D).addRange(0x1F80, 0x1FB4).addRange(0x1FB6, 0x1FC4).addRange(0x1FC6, 0x1FD3).addRange(0x1FD6, 0x1FDB).addRange(0x1FDD, 0x1FEF).addRange(0x1FF2, 0x1FF4).addRange(0x1FF6, 0x1FFE).addRange(0x2000, 0x200A).addRange(0x2010, 0x2027).addRange(0x202F, 0x205F).addRange(0x2070, 0x2071);
set.addRange(0x2074, 0x208E).addRange(0x2090, 0x209C).addRange(0x20A0, 0x20C0).addRange(0x2100, 0x218B).addRange(0x2190, 0x2429).addRange(0x2440, 0x244A).addRange(0x2460, 0x2B73).addRange(0x2B76, 0x2B95).addRange(0x2B97, 0x2CEE).addRange(0x2CF2, 0x2CF3).addRange(0x2CF9, 0x2D25).addRange(0x2D30, 0x2D67).addRange(0x2D6F, 0x2D70).addRange(0x2D80, 0x2D96).addRange(0x2DA0, 0x2DA6).addRange(0x2DA8, 0x2DAE).addRange(0x2DB0, 0x2DB6).addRange(0x2DB8, 0x2DBE).addRange(0x2DC0, 0x2DC6).addRange(0x2DC8, 0x2DCE).addRange(0x2DD0, 0x2DD6).addRange(0x2DD8, 0x2DDE).addRange(0x2E00, 0x2E5D).addRange(0x2E80, 0x2E99).addRange(0x2E9B, 0x2EF3).addRange(0x2F00, 0x2FD5).addRange(0x2FF0, 0x3029).addRange(0x3030, 0x303F).addRange(0x3041, 0x3096).addRange(0x309B, 0x30FF).addRange(0x3105, 0x312F).addRange(0x3131, 0x318E).addRange(0x3190, 0x31E5).addRange(0x31EF, 0x321E).addRange(0x3220, 0xA48C).addRange(0xA490, 0xA4C6).addRange(0xA4D0, 0xA62B).addRange(0xA640, 0xA66E).addRange(0xA67E, 0xA69D).addRange(0xA6A0, 0xA6EF).addRange(0xA6F2, 0xA6F7).addRange(0xA700, 0xA7CD).addRange(0xA7D0, 0xA7D1).addRange(0xA7D5, 0xA7DC).addRange(0xA7F2, 0xA801).addRange(0xA803, 0xA805).addRange(0xA807, 0xA80A).addRange(0xA80C, 0xA824).addRange(0xA827, 0xA82B).addRange(0xA830, 0xA839).addRange(0xA840, 0xA877);
set.addRange(0xA880, 0xA8C3).addRange(0xA8CE, 0xA8D9).addRange(0xA8F2, 0xA8FE).addRange(0xA900, 0xA925).addRange(0xA92E, 0xA946).addRange(0xA95F, 0xA97C).addRange(0xA983, 0xA9B2).addRange(0xA9B4, 0xA9B5).addRange(0xA9BA, 0xA9BB).addRange(0xA9BE, 0xA9BF).addRange(0xA9C1, 0xA9CD).addRange(0xA9CF, 0xA9D9).addRange(0xA9DE, 0xA9E4).addRange(0xA9E6, 0xA9FE).addRange(0xAA00, 0xAA28).addRange(0xAA2F, 0xAA30).addRange(0xAA33, 0xAA34).addRange(0xAA40, 0xAA42).addRange(0xAA44, 0xAA4B).addRange(0xAA50, 0xAA59).addRange(0xAA5C, 0xAA7B).addRange(0xAA7D, 0xAAAF).addRange(0xAAB5, 0xAAB6).addRange(0xAAB9, 0xAABD).addRange(0xAADB, 0xAAEB).addRange(0xAAEE, 0xAAF5).addRange(0xAB01, 0xAB06).addRange(0xAB09, 0xAB0E).addRange(0xAB11, 0xAB16).addRange(0xAB20, 0xAB26).addRange(0xAB28, 0xAB2E).addRange(0xAB30, 0xAB6B).addRange(0xAB70, 0xABE4).addRange(0xABE6, 0xABE7).addRange(0xABE9, 0xABEC).addRange(0xABF0, 0xABF9).addRange(0xAC00, 0xD7A3).addRange(0xD7B0, 0xD7C6).addRange(0xD7CB, 0xD7FB).addRange(0xF900, 0xFA6D).addRange(0xFA70, 0xFAD9).addRange(0xFB00, 0xFB06).addRange(0xFB13, 0xFB17).addRange(0xFB1F, 0xFB36).addRange(0xFB38, 0xFB3C).addRange(0xFB40, 0xFB41).addRange(0xFB43, 0xFB44).addRange(0xFB46, 0xFBC2).addRange(0xFBD3, 0xFD8F).addRange(0xFD92, 0xFDC7).addRange(0xFDF0, 0xFDFF);
set.addRange(0xFE10, 0xFE19).addRange(0xFE30, 0xFE52).addRange(0xFE54, 0xFE66).addRange(0xFE68, 0xFE6B).addRange(0xFE70, 0xFE74).addRange(0xFE76, 0xFEFC).addRange(0xFF01, 0xFF9D).addRange(0xFFA0, 0xFFBE).addRange(0xFFC2, 0xFFC7).addRange(0xFFCA, 0xFFCF).addRange(0xFFD2, 0xFFD7).addRange(0xFFDA, 0xFFDC).addRange(0xFFE0, 0xFFE6).addRange(0xFFE8, 0xFFEE).addRange(0xFFFC, 0xFFFD).addRange(0x10000, 0x1000B).addRange(0x1000D, 0x10026).addRange(0x10028, 0x1003A).addRange(0x1003C, 0x1003D).addRange(0x1003F, 0x1004D).addRange(0x10050, 0x1005D).addRange(0x10080, 0x100FA).addRange(0x10100, 0x10102).addRange(0x10107, 0x10133).addRange(0x10137, 0x1018E).addRange(0x10190, 0x1019C).addRange(0x101D0, 0x101FC).addRange(0x10280, 0x1029C).addRange(0x102A0, 0x102D0).addRange(0x102E1, 0x102FB).addRange(0x10300, 0x10323).addRange(0x1032D, 0x1034A).addRange(0x10350, 0x10375).addRange(0x10380, 0x1039D).addRange(0x1039F, 0x103C3).addRange(0x103C8, 0x103D5).addRange(0x10400, 0x1049D).addRange(0x104A0, 0x104A9).addRange(0x104B0, 0x104D3).addRange(0x104D8, 0x104FB).addRange(0x10500, 0x10527).addRange(0x10530, 0x10563).addRange(0x1056F, 0x1057A).addRange(0x1057C, 0x1058A).addRange(0x1058C, 0x10592).addRange(0x10594, 0x10595).addRange(0x10597, 0x105A1).addRange(0x105A3, 0x105B1).addRange(0x105B3, 0x105B9).addRange(0x105BB, 0x105BC).addRange(0x105C0, 0x105F3);
set.addRange(0x10600, 0x10736).addRange(0x10740, 0x10755).addRange(0x10760, 0x10767).addRange(0x10780, 0x10785).addRange(0x10787, 0x107B0).addRange(0x107B2, 0x107BA).addRange(0x10800, 0x10805).addRange(0x1080A, 0x10835).addRange(0x10837, 0x10838).addRange(0x1083F, 0x10855).addRange(0x10857, 0x1089E).addRange(0x108A7, 0x108AF).addRange(0x108E0, 0x108F2).addRange(0x108F4, 0x108F5).addRange(0x108FB, 0x1091B).addRange(0x1091F, 0x10939).addRange(0x10980, 0x109B7).addRange(0x109BC, 0x109CF).addRange(0x109D2, 0x10A00).addRange(0x10A10, 0x10A13).addRange(0x10A15, 0x10A17).addRange(0x10A19, 0x10A35).addRange(0x10A40, 0x10A48).addRange(0x10A50, 0x10A58).addRange(0x10A60, 0x10A9F).addRange(0x10AC0, 0x10AE4).addRange(0x10AEB, 0x10AF6).addRange(0x10B00, 0x10B35).addRange(0x10B39, 0x10B55).addRange(0x10B58, 0x10B72).addRange(0x10B78, 0x10B91).addRange(0x10B99, 0x10B9C).addRange(0x10BA9, 0x10BAF).addRange(0x10C00, 0x10C48).addRange(0x10C80, 0x10CB2).addRange(0x10CC0, 0x10CF2).addRange(0x10CFA, 0x10D23).addRange(0x10D30, 0x10D39).addRange(0x10D40, 0x10D65).addRange(0x10D6E, 0x10D85).addRange(0x10D8E, 0x10D8F).addRange(0x10E60, 0x10E7E).addRange(0x10E80, 0x10EA9).addRange(0x10EB0, 0x10EB1).addRange(0x10EC2, 0x10EC4).addRange(0x10F00, 0x10F27).addRange(0x10F30, 0x10F45).addRange(0x10F51, 0x10F59).addRange(0x10F70, 0x10F81).addRange(0x10F86, 0x10F89).addRange(0x10FB0, 0x10FCB);
set.addRange(0x10FE0, 0x10FF6).addRange(0x11002, 0x11037).addRange(0x11047, 0x1104D).addRange(0x11052, 0x1106F).addRange(0x11071, 0x11072).addRange(0x11082, 0x110B2).addRange(0x110B7, 0x110B8).addRange(0x110BB, 0x110BC).addRange(0x110BE, 0x110C1).addRange(0x110D0, 0x110E8).addRange(0x110F0, 0x110F9).addRange(0x11103, 0x11126).addRange(0x11136, 0x11147).addRange(0x11150, 0x11172).addRange(0x11174, 0x11176).addRange(0x11182, 0x111B5).addRange(0x111C1, 0x111C8).addRange(0x111CD, 0x111CE).addRange(0x111D0, 0x111DF).addRange(0x111E1, 0x111F4).addRange(0x11200, 0x11211).addRange(0x11213, 0x1122E).addRange(0x11232, 0x11233).addRange(0x11238, 0x1123D).addRange(0x1123F, 0x11240).addRange(0x11280, 0x11286).addRange(0x1128A, 0x1128D).addRange(0x1128F, 0x1129D).addRange(0x1129F, 0x112A9).addRange(0x112B0, 0x112DE).addRange(0x112E0, 0x112E2).addRange(0x112F0, 0x112F9).addRange(0x11302, 0x11303).addRange(0x11305, 0x1130C).addRange(0x1130F, 0x11310).addRange(0x11313, 0x11328).addRange(0x1132A, 0x11330).addRange(0x11332, 0x11333).addRange(0x11335, 0x11339).addRange(0x11341, 0x11344).addRange(0x11347, 0x11348).addRange(0x1134B, 0x1134C).addRange(0x1135D, 0x11363).addRange(0x11380, 0x11389).addRange(0x11390, 0x113B5).addRange(0x113B9, 0x113BA).addRange(0x113CC, 0x113CD).addRange(0x113D3, 0x113D5).addRange(0x113D7, 0x113D8).addRange(0x11400, 0x11437).addRange(0x11440, 0x11441);
set.addRange(0x11447, 0x1145B).addRange(0x1145F, 0x11461).addRange(0x11480, 0x114AF).addRange(0x114B1, 0x114B2).addRange(0x114BB, 0x114BC).addRange(0x114C4, 0x114C7).addRange(0x114D0, 0x114D9).addRange(0x11580, 0x115AE).addRange(0x115B0, 0x115B1).addRange(0x115B8, 0x115BB).addRange(0x115C1, 0x115DB).addRange(0x11600, 0x11632).addRange(0x1163B, 0x1163C).addRange(0x11641, 0x11644).addRange(0x11650, 0x11659).addRange(0x11660, 0x1166C).addRange(0x11680, 0x116AA).addRange(0x116AE, 0x116AF).addRange(0x116B8, 0x116B9).addRange(0x116C0, 0x116C9).addRange(0x116D0, 0x116E3).addRange(0x11700, 0x1171A).addRange(0x11720, 0x11721).addRange(0x11730, 0x11746).addRange(0x11800, 0x1182E).addRange(0x118A0, 0x118F2).addRange(0x118FF, 0x11906).addRange(0x1190C, 0x11913).addRange(0x11915, 0x11916).addRange(0x11918, 0x1192F).addRange(0x11931, 0x11935).addRange(0x11937, 0x11938).addRange(0x1193F, 0x11942).addRange(0x11944, 0x11946).addRange(0x11950, 0x11959).addRange(0x119A0, 0x119A7).addRange(0x119AA, 0x119D3).addRange(0x119DC, 0x119DF).addRange(0x119E1, 0x119E4).addRange(0x11A0B, 0x11A32).addRange(0x11A39, 0x11A3A).addRange(0x11A3F, 0x11A46).addRange(0x11A57, 0x11A58).addRange(0x11A5C, 0x11A89).addRange(0x11A9A, 0x11AA2).addRange(0x11AB0, 0x11AF8).addRange(0x11B00, 0x11B09).addRange(0x11BC0, 0x11BE1).addRange(0x11BF0, 0x11BF9).addRange(0x11C00, 0x11C08).addRange(0x11C0A, 0x11C2F);
set.addRange(0x11C40, 0x11C45).addRange(0x11C50, 0x11C6C).addRange(0x11C70, 0x11C8F).addRange(0x11D00, 0x11D06).addRange(0x11D08, 0x11D09).addRange(0x11D0B, 0x11D30).addRange(0x11D50, 0x11D59).addRange(0x11D60, 0x11D65).addRange(0x11D67, 0x11D68).addRange(0x11D6A, 0x11D8E).addRange(0x11D93, 0x11D94).addRange(0x11DA0, 0x11DA9).addRange(0x11EE0, 0x11EF2).addRange(0x11EF5, 0x11EF8).addRange(0x11F02, 0x11F10).addRange(0x11F12, 0x11F35).addRange(0x11F3E, 0x11F3F).addRange(0x11F43, 0x11F59).addRange(0x11FC0, 0x11FF1).addRange(0x11FFF, 0x12399).addRange(0x12400, 0x1246E).addRange(0x12470, 0x12474).addRange(0x12480, 0x12543).addRange(0x12F90, 0x12FF2).addRange(0x13000, 0x1342F).addRange(0x13441, 0x13446).addRange(0x13460, 0x143FA).addRange(0x14400, 0x14646).addRange(0x16100, 0x1611D).addRange(0x1612A, 0x1612C).addRange(0x16130, 0x16139).addRange(0x16800, 0x16A38).addRange(0x16A40, 0x16A5E).addRange(0x16A60, 0x16A69).addRange(0x16A6E, 0x16ABE).addRange(0x16AC0, 0x16AC9).addRange(0x16AD0, 0x16AED).addRange(0x16B00, 0x16B2F).addRange(0x16B37, 0x16B45).addRange(0x16B50, 0x16B59).addRange(0x16B5B, 0x16B61).addRange(0x16B63, 0x16B77).addRange(0x16B7D, 0x16B8F).addRange(0x16D40, 0x16D79).addRange(0x16E40, 0x16E9A).addRange(0x16F00, 0x16F4A).addRange(0x16F50, 0x16F87).addRange(0x16F93, 0x16F9F).addRange(0x16FE0, 0x16FE3).addRange(0x17000, 0x187F7).addRange(0x18800, 0x18CD5);
set.addRange(0x18CFF, 0x18D08).addRange(0x1AFF0, 0x1AFF3).addRange(0x1AFF5, 0x1AFFB).addRange(0x1AFFD, 0x1AFFE).addRange(0x1B000, 0x1B122).addRange(0x1B150, 0x1B152).addRange(0x1B164, 0x1B167).addRange(0x1B170, 0x1B2FB).addRange(0x1BC00, 0x1BC6A).addRange(0x1BC70, 0x1BC7C).addRange(0x1BC80, 0x1BC88).addRange(0x1BC90, 0x1BC99).addRange(0x1CC00, 0x1CCF9).addRange(0x1CD00, 0x1CEB3).addRange(0x1CF50, 0x1CFC3).addRange(0x1D000, 0x1D0F5).addRange(0x1D100, 0x1D126).addRange(0x1D129, 0x1D164).addRange(0x1D16A, 0x1D16C).addRange(0x1D183, 0x1D184).addRange(0x1D18C, 0x1D1A9).addRange(0x1D1AE, 0x1D1EA).addRange(0x1D200, 0x1D241).addRange(0x1D2C0, 0x1D2D3).addRange(0x1D2E0, 0x1D2F3).addRange(0x1D300, 0x1D356).addRange(0x1D360, 0x1D378).addRange(0x1D400, 0x1D454).addRange(0x1D456, 0x1D49C).addRange(0x1D49E, 0x1D49F).addRange(0x1D4A5, 0x1D4A6).addRange(0x1D4A9, 0x1D4AC).addRange(0x1D4AE, 0x1D4B9).addRange(0x1D4BD, 0x1D4C3).addRange(0x1D4C5, 0x1D505).addRange(0x1D507, 0x1D50A).addRange(0x1D50D, 0x1D514).addRange(0x1D516, 0x1D51C).addRange(0x1D51E, 0x1D539).addRange(0x1D53B, 0x1D53E).addRange(0x1D540, 0x1D544).addRange(0x1D54A, 0x1D550).addRange(0x1D552, 0x1D6A5).addRange(0x1D6A8, 0x1D7CB).addRange(0x1D7CE, 0x1D9FF).addRange(0x1DA37, 0x1DA3A).addRange(0x1DA6D, 0x1DA74).addRange(0x1DA76, 0x1DA83).addRange(0x1DA85, 0x1DA8B).addRange(0x1DF00, 0x1DF1E).addRange(0x1DF25, 0x1DF2A);
set.addRange(0x1E030, 0x1E06D).addRange(0x1E100, 0x1E12C).addRange(0x1E137, 0x1E13D).addRange(0x1E140, 0x1E149).addRange(0x1E14E, 0x1E14F).addRange(0x1E290, 0x1E2AD).addRange(0x1E2C0, 0x1E2EB).addRange(0x1E2F0, 0x1E2F9).addRange(0x1E4D0, 0x1E4EB).addRange(0x1E4F0, 0x1E4F9).addRange(0x1E5D0, 0x1E5ED).addRange(0x1E5F0, 0x1E5FA).addRange(0x1E7E0, 0x1E7E6).addRange(0x1E7E8, 0x1E7EB).addRange(0x1E7ED, 0x1E7EE).addRange(0x1E7F0, 0x1E7FE).addRange(0x1E800, 0x1E8C4).addRange(0x1E8C7, 0x1E8CF).addRange(0x1E900, 0x1E943).addRange(0x1E950, 0x1E959).addRange(0x1E95E, 0x1E95F).addRange(0x1EC71, 0x1ECB4).addRange(0x1ED01, 0x1ED3D).addRange(0x1EE00, 0x1EE03).addRange(0x1EE05, 0x1EE1F).addRange(0x1EE21, 0x1EE22).addRange(0x1EE29, 0x1EE32).addRange(0x1EE34, 0x1EE37).addRange(0x1EE4D, 0x1EE4F).addRange(0x1EE51, 0x1EE52).addRange(0x1EE61, 0x1EE62).addRange(0x1EE67, 0x1EE6A).addRange(0x1EE6C, 0x1EE72).addRange(0x1EE74, 0x1EE77).addRange(0x1EE79, 0x1EE7C).addRange(0x1EE80, 0x1EE89).addRange(0x1EE8B, 0x1EE9B).addRange(0x1EEA1, 0x1EEA3).addRange(0x1EEA5, 0x1EEA9).addRange(0x1EEAB, 0x1EEBB).addRange(0x1EEF0, 0x1EEF1).addRange(0x1F000, 0x1F02B).addRange(0x1F030, 0x1F093).addRange(0x1F0A0, 0x1F0AE).addRange(0x1F0B1, 0x1F0BF).addRange(0x1F0C1, 0x1F0CF).addRange(0x1F0D1, 0x1F0F5).addRange(0x1F100, 0x1F1AD).addRange(0x1F1E6, 0x1F202).addRange(0x1F210, 0x1F23B).addRange(0x1F240, 0x1F248);
set.addRange(0x1F250, 0x1F251).addRange(0x1F260, 0x1F265).addRange(0x1F300, 0x1F6D7).addRange(0x1F6DC, 0x1F6EC).addRange(0x1F6F0, 0x1F6FC).addRange(0x1F700, 0x1F776).addRange(0x1F77B, 0x1F7D9).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F800, 0x1F80B).addRange(0x1F810, 0x1F847).addRange(0x1F850, 0x1F859).addRange(0x1F860, 0x1F887).addRange(0x1F890, 0x1F8AD).addRange(0x1F8B0, 0x1F8BB).addRange(0x1F8C0, 0x1F8C1).addRange(0x1F900, 0x1FA53).addRange(0x1FA60, 0x1FA6D).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC).addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8).addRange(0x1FB00, 0x1FB92).addRange(0x1FB94, 0x1FBF9).addRange(0x20000, 0x2A6DF).addRange(0x2A700, 0x2B739).addRange(0x2B740, 0x2B81D).addRange(0x2B820, 0x2CEA1).addRange(0x2CEB0, 0x2EBE0).addRange(0x2EBF0, 0x2EE5D).addRange(0x2F800, 0x2FA1D).addRange(0x30000, 0x3134A).addRange(0x31350, 0x323AF);
exports.characters = set;
