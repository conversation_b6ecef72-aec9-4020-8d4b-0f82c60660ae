{"version": 3, "file": "withEdgeToEdge.js", "names": ["_configPlugins", "data", "require", "_helpers", "_withConfigureEdgeToEdgeEnforcement", "_withEdgeToEdgeEnabledGradleProperties", "_withRestoreDefaultTheme", "TAG", "withEdgeToEdge", "config", "projectRoot", "applyEdgeToEdge", "exports", "pluginIndex", "edgeToEdgePluginIndex", "android", "edgeToEdgeEnabled", "undefined", "WarningAggregator", "addWarningAndroid", "edgeToEdgeConfigPlugin", "loadEdgeToEdgeConfigPlugin", "withConfigureEdgeToEdgeEnforcement", "disableEdgeToEdgeEnforcement", "withEdgeToEdgeEnabledGradleProperties", "withRestoreDefaultTheme", "hasEnabledEdgeToEdge", "warning", "constructWarning", "parentTheme", "enforceNavigationBarContrast", "_default", "default"], "sources": ["../../../../src/plugins/unversioned/react-native-edge-to-edge/withEdgeToEdge.ts"], "sourcesContent": ["import {\n  ConfigPlugin,\n  ExportedConfigWithProps,\n  WarningAggregator,\n  AndroidConfig,\n} from '@expo/config-plugins';\nimport { type ExpoConfig } from '@expo/config-types';\n\nimport { edgeToEdgePluginIndex, hasEnabledEdgeToEdge, loadEdgeToEdgeConfigPlugin } from './helpers';\nimport { withConfigureEdgeToEdgeEnforcement } from './withConfigureEdgeToEdgeEnforcement';\nimport { withEdgeToEdgeEnabledGradleProperties } from './withEdgeToEdgeEnabledGradleProperties';\nimport { withRestoreDefaultTheme } from './withRestoreDefaultTheme';\n\nconst TAG = 'EDGE_TO_EDGE_PLUGIN';\n\nexport type EdgeToEdgePlugin = ConfigPlugin<{\n  android: {\n    parentTheme?: string;\n    enforceNavigationBarContrast?: boolean;\n  };\n}>;\n\nexport type ResourceXMLConfig = ExportedConfigWithProps<AndroidConfig.Resources.ResourceXML>;\nexport type GradlePropertiesConfig = ExportedConfigWithProps<\n  AndroidConfig.Properties.PropertiesItem[]\n>;\n\nexport const withEdgeToEdge: ConfigPlugin<{ projectRoot: string }> = (config, { projectRoot }) => {\n  return applyEdgeToEdge(config, projectRoot);\n};\n\nexport function applyEdgeToEdge(config: ExpoConfig, projectRoot: string): ExpoConfig {\n  // Check if someone has manually configured the config plugin\n  const pluginIndex = edgeToEdgePluginIndex(config);\n  if (config.android?.edgeToEdgeEnabled === undefined && pluginIndex === null) {\n    WarningAggregator.addWarningAndroid(\n      TAG,\n      'No configuration found for `edgeToEdgeEnabled` field in the project app config, falling back to false. In Android 16+ (targetSdkVersion 36) it will no longer be possible to disable edge-to-edge. Learn more:',\n      'https://expo.fyi/edge-to-edge-rollout'\n    );\n  } else if (config.android?.edgeToEdgeEnabled === false && pluginIndex === null) {\n    WarningAggregator.addWarningAndroid(\n      TAG,\n      '`edgeToEdgeEnabled` field is explicitly set to false in the project app config. In Android 16+ (targetSdkVersion 36) it will no longer be possible to disable edge-to-edge. Learn more:',\n      'https://expo.fyi/edge-to-edge-rollout'\n    );\n  }\n\n  const edgeToEdgeConfigPlugin = loadEdgeToEdgeConfigPlugin(projectRoot);\n\n  if (edgeToEdgeConfigPlugin === null) {\n    WarningAggregator.addWarningAndroid(\n      TAG,\n      'Failed to load the react-native-edge-to-edge config plugin, edge to edge functionality will be disabled. ' +\n        'To enable edge-to-edge make sure that `react-native-edge-to-edge` is installed in your project.'\n    );\n\n    // Disable edge-to-edge enforcement if the plugin is not installed\n    config = withConfigureEdgeToEdgeEnforcement(config, {\n      disableEdgeToEdgeEnforcement: true,\n    });\n\n    config = withEdgeToEdgeEnabledGradleProperties(config, { edgeToEdgeEnabled: false });\n    return withRestoreDefaultTheme(config);\n  }\n  const edgeToEdgeEnabled = hasEnabledEdgeToEdge(config);\n\n  config = withEdgeToEdgeEnabledGradleProperties(config, { edgeToEdgeEnabled });\n\n  // Enable/disable edge-to-edge enforcement\n  config = withConfigureEdgeToEdgeEnforcement(config, {\n    disableEdgeToEdgeEnforcement: !edgeToEdgeEnabled,\n  });\n\n  if (pluginIndex !== null) {\n    const warning = constructWarning(pluginIndex, config);\n    if (warning) {\n      WarningAggregator.addWarningAndroid('EDGE_TO_EDGE_CONFLICT', warning);\n    }\n    return config;\n  }\n\n  if (config.android?.edgeToEdgeEnabled !== true) {\n    return withRestoreDefaultTheme(config);\n  }\n\n  // Run `react-native-edge-to-edge` config plugin configuration if edge-to-edge is enabled and the user hasn't added their own\n  // plugin configuration in `app.json` / `app.config.json`.\n  return edgeToEdgeConfigPlugin(config, {\n    android: {\n      parentTheme: 'Default',\n      enforceNavigationBarContrast: true,\n    },\n  });\n}\n\nfunction constructWarning(pluginIndex: number | null, config: ExpoConfig): string | null {\n  if (pluginIndex === null) {\n    return null;\n  }\n\n  if (hasEnabledEdgeToEdge(config) && config?.android?.edgeToEdgeEnabled === false) {\n    return (\n      `You have configured the \\`react-native-edge-to-edge\\` plugin in your config file, while also setting the \\`android.edgeToEdgeEnabled\\` ` +\n      `field to \\`false\\`. The value of \\`android.edgeToEdgeEnabled\\` field will be ignored`\n    );\n  }\n  return null;\n}\n\nexport default withEdgeToEdge;\n"], "mappings": ";;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAQA,SAAAE,SAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,QAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,oCAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,mCAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,uCAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,sCAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAK,yBAAA;EAAA,MAAAL,IAAA,GAAAC,OAAA;EAAAI,wBAAA,YAAAA,CAAA;IAAA,OAAAL,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEA,MAAMM,GAAG,GAAG,qBAAqB;AAc1B,MAAMC,cAAqD,GAAGA,CAACC,MAAM,EAAE;EAAEC;AAAY,CAAC,KAAK;EAChG,OAAOC,eAAe,CAACF,MAAM,EAAEC,WAAW,CAAC;AAC7C,CAAC;AAACE,OAAA,CAAAJ,cAAA,GAAAA,cAAA;AAEK,SAASG,eAAeA,CAACF,MAAkB,EAAEC,WAAmB,EAAc;EACnF;EACA,MAAMG,WAAW,GAAG,IAAAC,gCAAqB,EAACL,MAAM,CAAC;EACjD,IAAIA,MAAM,CAACM,OAAO,EAAEC,iBAAiB,KAAKC,SAAS,IAAIJ,WAAW,KAAK,IAAI,EAAE;IAC3EK,kCAAiB,CAACC,iBAAiB,CACjCZ,GAAG,EACH,gNAAgN,EAChN,uCACF,CAAC;EACH,CAAC,MAAM,IAAIE,MAAM,CAACM,OAAO,EAAEC,iBAAiB,KAAK,KAAK,IAAIH,WAAW,KAAK,IAAI,EAAE;IAC9EK,kCAAiB,CAACC,iBAAiB,CACjCZ,GAAG,EACH,yLAAyL,EACzL,uCACF,CAAC;EACH;EAEA,MAAMa,sBAAsB,GAAG,IAAAC,qCAA0B,EAACX,WAAW,CAAC;EAEtE,IAAIU,sBAAsB,KAAK,IAAI,EAAE;IACnCF,kCAAiB,CAACC,iBAAiB,CACjCZ,GAAG,EACH,2GAA2G,GACzG,iGACJ,CAAC;;IAED;IACAE,MAAM,GAAG,IAAAa,wEAAkC,EAACb,MAAM,EAAE;MAClDc,4BAA4B,EAAE;IAChC,CAAC,CAAC;IAEFd,MAAM,GAAG,IAAAe,8EAAqC,EAACf,MAAM,EAAE;MAAEO,iBAAiB,EAAE;IAAM,CAAC,CAAC;IACpF,OAAO,IAAAS,kDAAuB,EAAChB,MAAM,CAAC;EACxC;EACA,MAAMO,iBAAiB,GAAG,IAAAU,+BAAoB,EAACjB,MAAM,CAAC;EAEtDA,MAAM,GAAG,IAAAe,8EAAqC,EAACf,MAAM,EAAE;IAAEO;EAAkB,CAAC,CAAC;;EAE7E;EACAP,MAAM,GAAG,IAAAa,wEAAkC,EAACb,MAAM,EAAE;IAClDc,4BAA4B,EAAE,CAACP;EACjC,CAAC,CAAC;EAEF,IAAIH,WAAW,KAAK,IAAI,EAAE;IACxB,MAAMc,OAAO,GAAGC,gBAAgB,CAACf,WAAW,EAAEJ,MAAM,CAAC;IACrD,IAAIkB,OAAO,EAAE;MACXT,kCAAiB,CAACC,iBAAiB,CAAC,uBAAuB,EAAEQ,OAAO,CAAC;IACvE;IACA,OAAOlB,MAAM;EACf;EAEA,IAAIA,MAAM,CAACM,OAAO,EAAEC,iBAAiB,KAAK,IAAI,EAAE;IAC9C,OAAO,IAAAS,kDAAuB,EAAChB,MAAM,CAAC;EACxC;;EAEA;EACA;EACA,OAAOW,sBAAsB,CAACX,MAAM,EAAE;IACpCM,OAAO,EAAE;MACPc,WAAW,EAAE,SAAS;MACtBC,4BAA4B,EAAE;IAChC;EACF,CAAC,CAAC;AACJ;AAEA,SAASF,gBAAgBA,CAACf,WAA0B,EAAEJ,MAAkB,EAAiB;EACvF,IAAII,WAAW,KAAK,IAAI,EAAE;IACxB,OAAO,IAAI;EACb;EAEA,IAAI,IAAAa,+BAAoB,EAACjB,MAAM,CAAC,IAAIA,MAAM,EAAEM,OAAO,EAAEC,iBAAiB,KAAK,KAAK,EAAE;IAChF,OACE,yIAAyI,GACzI,sFAAsF;EAE1F;EACA,OAAO,IAAI;AACb;AAAC,IAAAe,QAAA,GAAAnB,OAAA,CAAAoB,OAAA,GAEcxB,cAAc", "ignoreList": []}