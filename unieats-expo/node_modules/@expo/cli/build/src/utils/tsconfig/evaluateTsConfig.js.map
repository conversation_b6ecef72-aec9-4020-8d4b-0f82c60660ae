{"version": 3, "sources": ["../../../../src/utils/tsconfig/evaluateTsConfig.ts"], "sourcesContent": ["import path from 'path';\nimport resolveFrom from 'resolve-from';\n\nexport function evaluateTsConfig(ts: typeof import('typescript'), tsConfigPath: string) {\n  const formatDiagnosticsHost: import('typescript').FormatDiagnosticsHost = {\n    getNewLine: () => require('os').EOL,\n    getCurrentDirectory: ts.sys.getCurrentDirectory,\n    getCanonicalFileName: (fileName: string) => fileName,\n  };\n\n  try {\n    const { config, error } = ts.readConfigFile(tsConfigPath, ts.sys.readFile);\n\n    if (error) {\n      throw new Error(ts.formatDiagnostic(error, formatDiagnosticsHost));\n    }\n\n    const jsonFileContents = ts.parseJsonConfigFileContent(\n      config,\n      {\n        ...ts.sys,\n        readDirectory: (_, ext) => [ext ? `file${ext[0]}` : `file.ts`],\n      },\n      path.dirname(tsConfigPath)\n    );\n\n    if (jsonFileContents.errors) {\n      jsonFileContents.errors = jsonFileContents.errors\n        .filter(({ code }) => {\n          // TS18003: filter out \"no inputs were found in config file\" error */\n          // TS6046: filter out \"Argument for '--module' option must be\" error\n          //         this error can be ignored since we're only typically interested in `paths` and `baseUrl`\n          return code !== 18003 && code !== 6046;\n        })\n        // filter out non-error diagnostics\n        .filter(({ category }) => category !== 1 /*DiagnosticCategory.Error = 1*/);\n    }\n\n    if (jsonFileContents.errors?.length) {\n      throw new Error(ts.formatDiagnostic(jsonFileContents.errors[0], formatDiagnosticsHost));\n    }\n\n    return { compilerOptions: jsonFileContents.options, raw: config.raw };\n  } catch (error: any) {\n    if (error?.name === 'SyntaxError') {\n      throw new Error('tsconfig.json is invalid:\\n' + (error.message ?? ''));\n    }\n    throw error;\n  }\n}\n\nexport function importTypeScriptFromProjectOptionally(\n  projectRoot: string\n): typeof import('typescript') | null {\n  const resolvedPath = resolveFrom.silent(projectRoot, 'typescript');\n  if (!resolvedPath) {\n    return null;\n  }\n  return require(resolvedPath);\n}\n"], "names": ["evaluateTsConfig", "importTypeScriptFromProjectOptionally", "ts", "tsConfigPath", "formatDiagnosticsHost", "getNewLine", "require", "EOL", "getCurrentDirectory", "sys", "getCanonicalFileName", "fileName", "jsonFileContents", "config", "error", "readConfigFile", "readFile", "Error", "formatDiagnostic", "parseJsonConfigFileContent", "readDirectory", "_", "ext", "path", "dirname", "errors", "filter", "code", "category", "length", "compilerOptions", "options", "raw", "name", "message", "projectRoot", "<PERSON><PERSON><PERSON>", "resolveFrom", "silent"], "mappings": ";;;;;;;;;;;IAGgBA,gBAAgB;eAAhBA;;IAgDAC,qCAAqC;eAArCA;;;;gEAnDC;;;;;;;gEACO;;;;;;;;;;;AAEjB,SAASD,iBAAiBE,EAA+B,EAAEC,YAAoB;IACpF,MAAMC,wBAAoE;QACxEC,YAAY,IAAMC,QAAQ,MAAMC,GAAG;QACnCC,qBAAqBN,GAAGO,GAAG,CAACD,mBAAmB;QAC/CE,sBAAsB,CAACC,WAAqBA;IAC9C;IAEA,IAAI;YA4BEC;QA3BJ,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE,GAAGZ,GAAGa,cAAc,CAACZ,cAAcD,GAAGO,GAAG,CAACO,QAAQ;QAEzE,IAAIF,OAAO;YACT,MAAM,IAAIG,MAAMf,GAAGgB,gBAAgB,CAACJ,OAAOV;QAC7C;QAEA,MAAMQ,mBAAmBV,GAAGiB,0BAA0B,CACpDN,QACA;YACE,GAAGX,GAAGO,GAAG;YACTW,eAAe,CAACC,GAAGC,MAAQ;oBAACA,MAAM,CAAC,IAAI,EAAEA,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,OAAO,CAAC;iBAAC;QAChE,GACAC,eAAI,CAACC,OAAO,CAACrB;QAGf,IAAIS,iBAAiBa,MAAM,EAAE;YAC3Bb,iBAAiBa,MAAM,GAAGb,iBAAiBa,MAAM,CAC9CC,MAAM,CAAC,CAAC,EAAEC,IAAI,EAAE;gBACf,qEAAqE;gBACrE,oEAAoE;gBACpE,mGAAmG;gBACnG,OAAOA,SAAS,SAASA,SAAS;YACpC,EACA,mCAAmC;aAClCD,MAAM,CAAC,CAAC,EAAEE,QAAQ,EAAE,GAAKA,aAAa,EAAE,8BAA8B;QAC3E;QAEA,KAAIhB,2BAAAA,iBAAiBa,MAAM,qBAAvBb,yBAAyBiB,MAAM,EAAE;YACnC,MAAM,IAAIZ,MAAMf,GAAGgB,gBAAgB,CAACN,iBAAiBa,MAAM,CAAC,EAAE,EAAErB;QAClE;QAEA,OAAO;YAAE0B,iBAAiBlB,iBAAiBmB,OAAO;YAAEC,KAAKnB,OAAOmB,GAAG;QAAC;IACtE,EAAE,OAAOlB,OAAY;QACnB,IAAIA,CAAAA,yBAAAA,MAAOmB,IAAI,MAAK,eAAe;YACjC,MAAM,IAAIhB,MAAM,gCAAiCH,CAAAA,MAAMoB,OAAO,IAAI,EAAC;QACrE;QACA,MAAMpB;IACR;AACF;AAEO,SAASb,sCACdkC,WAAmB;IAEnB,MAAMC,eAAeC,sBAAW,CAACC,MAAM,CAACH,aAAa;IACrD,IAAI,CAACC,cAAc;QACjB,OAAO;IACT;IACA,OAAO9B,QAAQ8B;AACjB"}