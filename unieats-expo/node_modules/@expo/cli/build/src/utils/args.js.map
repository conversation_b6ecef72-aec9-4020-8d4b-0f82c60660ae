{"version": 3, "sources": ["../../../src/utils/args.ts"], "sourcesContent": ["// Common utilities for interacting with `args` library.\n// These functions should be used by every command.\nimport arg from 'arg';\nimport chalk from 'chalk';\nimport { existsSync } from 'fs';\nimport { resolve } from 'path';\n\nimport * as Log from '../log';\n\n/**\n * Parse the first argument as a project directory.\n *\n * @returns valid project directory.\n */\nexport function getProjectRoot(args: arg.Result<arg.Spec>) {\n  const projectRoot = resolve(args._[0] || '.');\n\n  if (!existsSync(projectRoot)) {\n    Log.exit(`Invalid project root: ${projectRoot}`);\n  }\n\n  return projectRoot;\n}\n\n/**\n * Parse args and assert unknown options.\n *\n * @param schema the `args` schema for parsing the command line arguments.\n * @param argv extra strings\n * @returns processed args object.\n */\nexport function assertArgs(schema: arg.Spec, argv?: string[]): arg.Result<arg.Spec> {\n  return assertWithOptionsArgs(schema, { argv });\n}\n\nexport function assertWithOptionsArgs(\n  schema: arg.Spec,\n  options: arg.Options\n): arg.Result<arg.Spec> {\n  try {\n    return arg(schema, options);\n  } catch (error: any) {\n    // Handle errors caused by user input.\n    // Only errors from `arg`, which does not start with `ARG_CONFIG_` are user input errors.\n    // See: https://github.com/vercel/arg/releases/tag/5.0.0\n    if ('code' in error && error.code.startsWith('ARG_') && !error.code.startsWith('ARG_CONFIG_')) {\n      Log.exit(error.message, 1);\n    }\n    // Otherwise rethrow the error.\n    throw error;\n  }\n}\n\nexport function printHelp(info: string, usage: string, options: string, extra: string = ''): never {\n  Log.exit(\n    chalk`\n  {bold Info}\n    ${info}\n\n  {bold Usage}\n    {dim $} ${usage}\n\n  {bold Options}\n    ${options.split('\\n').join('\\n    ')}\n` + extra,\n    0\n  );\n}\n"], "names": ["assertArgs", "assertWithOptionsArgs", "getProjectRoot", "printHelp", "args", "projectRoot", "resolve", "_", "existsSync", "Log", "exit", "schema", "argv", "options", "arg", "error", "code", "startsWith", "message", "info", "usage", "extra", "chalk", "split", "join"], "mappings": "AAAA,wDAAwD;AACxD,mDAAmD;;;;;;;;;;;;IA8BnCA,UAAU;eAAVA;;IAIAC,qBAAqB;eAArBA;;IArBAC,cAAc;eAAdA;;IAuCAC,SAAS;eAATA;;;;gEAnDA;;;;;;;gEACE;;;;;;;yBACS;;;;;;;yBACH;;;;;;6DAEH;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOd,SAASD,eAAeE,IAA0B;IACvD,MAAMC,cAAcC,IAAAA,eAAO,EAACF,KAAKG,CAAC,CAAC,EAAE,IAAI;IAEzC,IAAI,CAACC,IAAAA,gBAAU,EAACH,cAAc;QAC5BI,KAAIC,IAAI,CAAC,CAAC,sBAAsB,EAAEL,aAAa;IACjD;IAEA,OAAOA;AACT;AASO,SAASL,WAAWW,MAAgB,EAAEC,IAAe;IAC1D,OAAOX,sBAAsBU,QAAQ;QAAEC;IAAK;AAC9C;AAEO,SAASX,sBACdU,MAAgB,EAChBE,OAAoB;IAEpB,IAAI;QACF,OAAOC,IAAAA,cAAG,EAACH,QAAQE;IACrB,EAAE,OAAOE,OAAY;QACnB,sCAAsC;QACtC,yFAAyF;QACzF,wDAAwD;QACxD,IAAI,UAAUA,SAASA,MAAMC,IAAI,CAACC,UAAU,CAAC,WAAW,CAACF,MAAMC,IAAI,CAACC,UAAU,CAAC,gBAAgB;YAC7FR,KAAIC,IAAI,CAACK,MAAMG,OAAO,EAAE;QAC1B;QACA,+BAA+B;QAC/B,MAAMH;IACR;AACF;AAEO,SAASZ,UAAUgB,IAAY,EAAEC,KAAa,EAAEP,OAAe,EAAEQ,QAAgB,EAAE;IACxFZ,KAAIC,IAAI,CACNY,IAAAA,gBAAK,CAAA,CAAC;;IAEN,EAAEH,KAAK;;;YAGC,EAAEC,MAAM;;;IAGhB,EAAEP,QAAQU,KAAK,CAAC,MAAMC,IAAI,CAAC,UAAU;AACzC,CAAC,GAAGH,OACA;AAEJ"}