{"version": 3, "sources": ["../../../src/prebuild/updateFromTemplate.ts"], "sourcesContent": ["import { ExpoConfig, PackageJSONConfig } from '@expo/config';\nimport { ModPlatform } from '@expo/config-plugins';\nimport chalk from 'chalk';\n\nimport { copyTemplateFiles, createCopyFilesSuccessMessage } from './copyTemplateFiles';\nimport { getTemplateFilesToRenameAsync, renameTemplateAppNameAsync } from './renameTemplateAppName';\nimport { type ResolvedTemplateOption } from './resolveOptions';\nimport { cloneTemplateAsync } from './resolveTemplate';\nimport { DependenciesModificationResults, updatePackageJSONAsync } from './updatePackageJson';\nimport { validateTemplatePlatforms } from './validateTemplatePlatforms';\nimport * as Log from '../log';\nimport { createTempDirectoryPath } from '../utils/createTempPath';\nimport { AbortCommandError, SilentError } from '../utils/errors';\nimport { logNewSection } from '../utils/ora';\nimport { profile } from '../utils/profile';\n\n/**\n * Creates local native files from an input template file path.\n *\n * @return `true` if the project is prebuilding, and `false` if it's syncing.\n */\nexport async function updateFromTemplateAsync(\n  projectRoot: string,\n  {\n    exp,\n    pkg,\n    template,\n    templateDirectory,\n    platforms,\n    skipDependencyUpdate,\n  }: {\n    /** Expo Config */\n    exp: ExpoConfig;\n    /** package.json as JSON */\n    pkg: PackageJSONConfig;\n    /** Template to clone from. */\n    template?: ResolvedTemplateOption;\n    /** Directory to write the template to before copying into the project. */\n    templateDirectory?: string;\n    /** List of platforms to clone. */\n    platforms: ModPlatform[];\n    /** List of dependencies to skip updating. */\n    skipDependencyUpdate?: string[];\n  }\n): Promise<\n  {\n    /** Indicates if new files were created in the project. */\n    hasNewProjectFiles: boolean;\n    /** Indicates that the project needs to run `pod install` */\n    needsPodInstall: boolean;\n    /** The template checksum used to create the native project. */\n    templateChecksum: string;\n  } & DependenciesModificationResults\n> {\n  if (!templateDirectory) {\n    templateDirectory = createTempDirectoryPath();\n  }\n\n  const { copiedPaths, templateChecksum } = await profile(cloneTemplateAndCopyToProjectAsync)({\n    projectRoot,\n    template,\n    templateDirectory,\n    exp,\n    platforms,\n  });\n\n  const depsResults = await profile(updatePackageJSONAsync)(projectRoot, {\n    templateDirectory,\n    pkg,\n    skipDependencyUpdate,\n  });\n\n  return {\n    hasNewProjectFiles: !!copiedPaths.length,\n    // If the iOS folder changes or new packages are added, we should rerun pod install.\n    needsPodInstall: copiedPaths.includes('ios') || !!depsResults.changedDependencies.length,\n    templateChecksum,\n    ...depsResults,\n  };\n}\n\n/**\n * Extract the template and copy the ios and android directories over to the project directory.\n *\n * @return `true` if any project files were created.\n */\nexport async function cloneTemplateAndCopyToProjectAsync({\n  projectRoot,\n  templateDirectory,\n  template,\n  exp,\n  platforms: unknownPlatforms,\n}: {\n  projectRoot: string;\n  templateDirectory: string;\n  template?: ResolvedTemplateOption;\n  exp: Pick<ExpoConfig, 'name' | 'sdkVersion'>;\n  platforms: ModPlatform[];\n}): Promise<{ copiedPaths: string[]; templateChecksum: string }> {\n  const platformDirectories = unknownPlatforms\n    .map((platform) => `./${platform}`)\n    .reverse()\n    .join(' and ');\n\n  const pluralized = unknownPlatforms.length > 1 ? 'directories' : 'directory';\n  const ora = logNewSection(`Creating native ${pluralized} (${platformDirectories})`);\n\n  try {\n    const templateChecksum = await cloneTemplateAsync({ templateDirectory, template, exp, ora });\n\n    const platforms = validateTemplatePlatforms({\n      templateDirectory,\n      platforms: unknownPlatforms,\n    });\n\n    const results = copyTemplateFiles(projectRoot, {\n      templateDirectory,\n      platforms,\n    });\n\n    const files = await getTemplateFilesToRenameAsync({ cwd: projectRoot });\n    await renameTemplateAppNameAsync({\n      cwd: projectRoot,\n      files,\n      name: exp.name,\n    });\n\n    // Says: \"Created native directories\"\n    ora.succeed(createCopyFilesSuccessMessage(platforms, results));\n\n    return {\n      copiedPaths: results.copiedPaths,\n      templateChecksum,\n    };\n  } catch (e: any) {\n    if (!(e instanceof AbortCommandError)) {\n      Log.error(e.message);\n    }\n    ora.fail(`Failed to create the native ${pluralized}`);\n    Log.log(\n      chalk.yellow(\n        chalk`You may want to delete the {bold ./ios} and/or {bold ./android} directories before trying again.`\n      )\n    );\n    throw new SilentError(e);\n  }\n}\n"], "names": ["cloneTemplateAndCopyToProjectAsync", "updateFromTemplateAsync", "projectRoot", "exp", "pkg", "template", "templateDirectory", "platforms", "skipDependencyUpdate", "createTempDirectoryPath", "copiedPaths", "templateChecksum", "profile", "depsResults", "updatePackageJSONAsync", "hasNewProjectFiles", "length", "needsPodInstall", "includes", "changedDependencies", "unknownPlatforms", "platformDirectories", "map", "platform", "reverse", "join", "pluralized", "ora", "logNewSection", "cloneTemplateAsync", "validateTemplatePlatforms", "results", "copyTemplateFiles", "files", "getTemplateFilesToRenameAsync", "cwd", "renameTemplateAppNameAsync", "name", "succeed", "createCopyFilesSuccessMessage", "e", "AbortCommandError", "Log", "error", "message", "fail", "log", "chalk", "yellow", "SilentError"], "mappings": ";;;;;;;;;;;IAsFsBA,kCAAkC;eAAlCA;;IAjEAC,uBAAuB;eAAvBA;;;;gEAnBJ;;;;;;mCAE+C;uCACS;iCAEvC;mCACqC;2CAC9B;6DACrB;gCACmB;wBACO;qBACjB;yBACN;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOjB,eAAeA,wBACpBC,WAAmB,EACnB,EACEC,GAAG,EACHC,GAAG,EACHC,QAAQ,EACRC,iBAAiB,EACjBC,SAAS,EACTC,oBAAoB,EAcrB;IAWD,IAAI,CAACF,mBAAmB;QACtBA,oBAAoBG,IAAAA,uCAAuB;IAC7C;IAEA,MAAM,EAAEC,WAAW,EAAEC,gBAAgB,EAAE,GAAG,MAAMC,IAAAA,gBAAO,EAACZ,oCAAoC;QAC1FE;QACAG;QACAC;QACAH;QACAI;IACF;IAEA,MAAMM,cAAc,MAAMD,IAAAA,gBAAO,EAACE,yCAAsB,EAAEZ,aAAa;QACrEI;QACAF;QACAI;IACF;IAEA,OAAO;QACLO,oBAAoB,CAAC,CAACL,YAAYM,MAAM;QACxC,oFAAoF;QACpFC,iBAAiBP,YAAYQ,QAAQ,CAAC,UAAU,CAAC,CAACL,YAAYM,mBAAmB,CAACH,MAAM;QACxFL;QACA,GAAGE,WAAW;IAChB;AACF;AAOO,eAAeb,mCAAmC,EACvDE,WAAW,EACXI,iBAAiB,EACjBD,QAAQ,EACRF,GAAG,EACHI,WAAWa,gBAAgB,EAO5B;IACC,MAAMC,sBAAsBD,iBACzBE,GAAG,CAAC,CAACC,WAAa,CAAC,EAAE,EAAEA,UAAU,EACjCC,OAAO,GACPC,IAAI,CAAC;IAER,MAAMC,aAAaN,iBAAiBJ,MAAM,GAAG,IAAI,gBAAgB;IACjE,MAAMW,MAAMC,IAAAA,kBAAa,EAAC,CAAC,gBAAgB,EAAEF,WAAW,EAAE,EAAEL,oBAAoB,CAAC,CAAC;IAElF,IAAI;QACF,MAAMV,mBAAmB,MAAMkB,IAAAA,mCAAkB,EAAC;YAAEvB;YAAmBD;YAAUF;YAAKwB;QAAI;QAE1F,MAAMpB,YAAYuB,IAAAA,oDAAyB,EAAC;YAC1CxB;YACAC,WAAWa;QACb;QAEA,MAAMW,UAAUC,IAAAA,oCAAiB,EAAC9B,aAAa;YAC7CI;YACAC;QACF;QAEA,MAAM0B,QAAQ,MAAMC,IAAAA,oDAA6B,EAAC;YAAEC,KAAKjC;QAAY;QACrE,MAAMkC,IAAAA,iDAA0B,EAAC;YAC/BD,KAAKjC;YACL+B;YACAI,MAAMlC,IAAIkC,IAAI;QAChB;QAEA,qCAAqC;QACrCV,IAAIW,OAAO,CAACC,IAAAA,gDAA6B,EAAChC,WAAWwB;QAErD,OAAO;YACLrB,aAAaqB,QAAQrB,WAAW;YAChCC;QACF;IACF,EAAE,OAAO6B,GAAQ;QACf,IAAI,CAAEA,CAAAA,aAAaC,yBAAiB,AAAD,GAAI;YACrCC,KAAIC,KAAK,CAACH,EAAEI,OAAO;QACrB;QACAjB,IAAIkB,IAAI,CAAC,CAAC,4BAA4B,EAAEnB,YAAY;QACpDgB,KAAII,GAAG,CACLC,gBAAK,CAACC,MAAM,CACVD,IAAAA,gBAAK,CAAA,CAAC,gGAAgG,CAAC;QAG3G,MAAM,IAAIE,mBAAW,CAACT;IACxB;AACF"}