{"version": 3, "file": "BuildScheme.js", "names": ["_Paths", "data", "require", "_Target", "_Xcodeproj", "_XML", "getSchemesFromXcodeproj", "projectRoot", "findSchemeNames", "getRunnableSchemesFromXcodeproj", "configuration", "project", "getPbxproj", "findSignableTargets", "map", "target", "osType", "type", "unquote", "productType", "TargetType", "WATCH", "startsWith", "APPLICATION", "xcConfigurationList", "hash", "objects", "XCConfigurationList", "buildConfigurationList", "buildConfiguration", "buildConfigurations", "find", "value", "comment", "xcBuildConfiguration", "XCBuildConfiguration", "buildSdkRoot", "buildSettings", "SDKROOT", "name", "readSchemeAsync", "scheme", "allSchemePaths", "findSchemePaths", "re", "RegExp", "schemePath", "i", "exec", "readXMLAsync", "path", "Error", "getApplicationTargetNameForSchemeAsync", "schemeXML", "buildActionEntry", "Scheme", "BuildAction", "BuildActionEntries", "BuildActionEntry", "targetName", "length", "getBlueprintName", "entry", "BuildableReference", "BuildableName", "endsWith", "getArchiveBuildConfigurationForSchemeAsync", "ArchiveAction", "BlueprintName"], "sources": ["../../src/ios/BuildScheme.ts"], "sourcesContent": ["import { findSchemeNames, findSchemePaths } from './Paths';\nimport { findSignableTargets, TargetType } from './Target';\nimport { getPbxproj, unquote } from './utils/Xcodeproj';\nimport { readXMLAsync } from '../utils/XML';\n\ninterface SchemeXML {\n  Scheme?: {\n    BuildAction?: {\n      BuildActionEntries?: {\n        BuildActionEntry?: BuildActionEntryType[];\n      }[];\n    }[];\n    ArchiveAction?: {\n      $?: {\n        buildConfiguration?: string;\n      };\n    }[];\n  };\n}\n\ninterface BuildActionEntryType {\n  BuildableReference?: {\n    $?: {\n      BlueprintName?: string;\n      BuildableName?: string;\n    };\n  }[];\n}\n\nexport function getSchemesFromXcodeproj(projectRoot: string): string[] {\n  return findSchemeNames(projectRoot);\n}\n\nexport function getRunnableSchemesFromXcodeproj(\n  projectRoot: string,\n  { configuration = 'Debug' }: { configuration?: 'Debug' | 'Release' } = {}\n): { name: string; osType: string; type: string }[] {\n  const project = getPbxproj(projectRoot);\n\n  return findSignableTargets(project).map(([, target]) => {\n    let osType = 'iOS';\n    const type = unquote(target.productType);\n\n    if (type === TargetType.WATCH) {\n      osType = 'watchOS';\n    } else if (\n      // (apps) com.apple.product-type.application\n      // (app clips) com.apple.product-type.application.on-demand-install-capable\n      // NOTE(EvanBacon): This matches against `watchOS` as well so we check for watch first.\n      type.startsWith(TargetType.APPLICATION)\n    ) {\n      // Attempt to resolve the platform SDK for each target so we can filter devices.\n      const xcConfigurationList =\n        project.hash.project.objects.XCConfigurationList[target.buildConfigurationList];\n\n      if (xcConfigurationList) {\n        const buildConfiguration =\n          xcConfigurationList.buildConfigurations.find(\n            (value: { comment: string; value: string }) => value.comment === configuration\n          ) || xcConfigurationList.buildConfigurations[0];\n        if (buildConfiguration?.value) {\n          const xcBuildConfiguration =\n            project.hash.project.objects.XCBuildConfiguration?.[buildConfiguration.value];\n\n          const buildSdkRoot = xcBuildConfiguration.buildSettings.SDKROOT;\n          if (\n            buildSdkRoot === 'appletvos' ||\n            'TVOS_DEPLOYMENT_TARGET' in xcBuildConfiguration.buildSettings\n          ) {\n            // Is a TV app...\n            osType = 'tvOS';\n          } else if (buildSdkRoot === 'iphoneos') {\n            osType = 'iOS';\n          }\n        }\n      }\n    }\n\n    return {\n      name: unquote(target.name),\n      osType,\n      type: unquote(target.productType),\n    };\n  });\n}\n\nasync function readSchemeAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<SchemeXML | undefined> {\n  const allSchemePaths = findSchemePaths(projectRoot);\n  // NOTE(cedric): test on POSIX or UNIX separators, where UNIX needs to be double-escaped in the template literal and regex\n  const re = new RegExp(`[\\\\\\\\/]${scheme}.xcscheme`, 'i');\n  const schemePath = allSchemePaths.find((i) => re.exec(i));\n  if (schemePath) {\n    return (await readXMLAsync({ path: schemePath })) as unknown as SchemeXML | undefined;\n  } else {\n    throw new Error(`scheme '${scheme}' does not exist, make sure it's marked as shared`);\n  }\n}\n\nexport async function getApplicationTargetNameForSchemeAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<string> {\n  const schemeXML = await readSchemeAsync(projectRoot, scheme);\n  const buildActionEntry =\n    schemeXML?.Scheme?.BuildAction?.[0]?.BuildActionEntries?.[0]?.BuildActionEntry;\n  const targetName =\n    buildActionEntry?.length === 1\n      ? getBlueprintName(buildActionEntry[0])\n      : getBlueprintName(\n          buildActionEntry?.find((entry) => {\n            return entry.BuildableReference?.[0]?.['$']?.BuildableName?.endsWith('.app');\n          })\n        );\n  if (!targetName) {\n    throw new Error(`${scheme}.xcscheme seems to be corrupted`);\n  }\n  return targetName;\n}\n\nexport async function getArchiveBuildConfigurationForSchemeAsync(\n  projectRoot: string,\n  scheme: string\n): Promise<string> {\n  const schemeXML = await readSchemeAsync(projectRoot, scheme);\n  const buildConfiguration = schemeXML?.Scheme?.ArchiveAction?.[0]?.['$']?.buildConfiguration;\n  if (!buildConfiguration) {\n    throw new Error(`${scheme}.xcscheme seems to be corrupted`);\n  }\n  return buildConfiguration;\n}\n\nfunction getBlueprintName(entry?: BuildActionEntryType): string | undefined {\n  return entry?.BuildableReference?.[0]?.['$']?.BlueprintName;\n}\n"], "mappings": ";;;;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,QAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,OAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAG,WAAA;EAAA,MAAAH,IAAA,GAAAC,OAAA;EAAAE,UAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAI,KAAA;EAAA,MAAAJ,IAAA,GAAAC,OAAA;EAAAG,IAAA,YAAAA,CAAA;IAAA,OAAAJ,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AA0BO,SAASK,uBAAuBA,CAACC,WAAmB,EAAY;EACrE,OAAO,IAAAC,wBAAe,EAACD,WAAW,CAAC;AACrC;AAEO,SAASE,+BAA+BA,CAC7CF,WAAmB,EACnB;EAAEG,aAAa,GAAG;AAAiD,CAAC,GAAG,CAAC,CAAC,EACvB;EAClD,MAAMC,OAAO,GAAG,IAAAC,uBAAU,EAACL,WAAW,CAAC;EAEvC,OAAO,IAAAM,6BAAmB,EAACF,OAAO,CAAC,CAACG,GAAG,CAAC,CAAC,GAAGC,MAAM,CAAC,KAAK;IACtD,IAAIC,MAAM,GAAG,KAAK;IAClB,MAAMC,IAAI,GAAG,IAAAC,oBAAO,EAACH,MAAM,CAACI,WAAW,CAAC;IAExC,IAAIF,IAAI,KAAKG,oBAAU,CAACC,KAAK,EAAE;MAC7BL,MAAM,GAAG,SAAS;IACpB,CAAC,MAAM;IACL;IACA;IACA;IACAC,IAAI,CAACK,UAAU,CAACF,oBAAU,CAACG,WAAW,CAAC,EACvC;MACA;MACA,MAAMC,mBAAmB,GACvBb,OAAO,CAACc,IAAI,CAACd,OAAO,CAACe,OAAO,CAACC,mBAAmB,CAACZ,MAAM,CAACa,sBAAsB,CAAC;MAEjF,IAAIJ,mBAAmB,EAAE;QACvB,MAAMK,kBAAkB,GACtBL,mBAAmB,CAACM,mBAAmB,CAACC,IAAI,CACzCC,KAAyC,IAAKA,KAAK,CAACC,OAAO,KAAKvB,aACnE,CAAC,IAAIc,mBAAmB,CAACM,mBAAmB,CAAC,CAAC,CAAC;QACjD,IAAID,kBAAkB,EAAEG,KAAK,EAAE;UAC7B,MAAME,oBAAoB,GACxBvB,OAAO,CAACc,IAAI,CAACd,OAAO,CAACe,OAAO,CAACS,oBAAoB,GAAGN,kBAAkB,CAACG,KAAK,CAAC;UAE/E,MAAMI,YAAY,GAAGF,oBAAoB,CAACG,aAAa,CAACC,OAAO;UAC/D,IACEF,YAAY,KAAK,WAAW,IAC5B,wBAAwB,IAAIF,oBAAoB,CAACG,aAAa,EAC9D;YACA;YACArB,MAAM,GAAG,MAAM;UACjB,CAAC,MAAM,IAAIoB,YAAY,KAAK,UAAU,EAAE;YACtCpB,MAAM,GAAG,KAAK;UAChB;QACF;MACF;IACF;IAEA,OAAO;MACLuB,IAAI,EAAE,IAAArB,oBAAO,EAACH,MAAM,CAACwB,IAAI,CAAC;MAC1BvB,MAAM;MACNC,IAAI,EAAE,IAAAC,oBAAO,EAACH,MAAM,CAACI,WAAW;IAClC,CAAC;EACH,CAAC,CAAC;AACJ;AAEA,eAAeqB,eAAeA,CAC5BjC,WAAmB,EACnBkC,MAAc,EACkB;EAChC,MAAMC,cAAc,GAAG,IAAAC,wBAAe,EAACpC,WAAW,CAAC;EACnD;EACA,MAAMqC,EAAE,GAAG,IAAIC,MAAM,CAAC,UAAUJ,MAAM,WAAW,EAAE,GAAG,CAAC;EACvD,MAAMK,UAAU,GAAGJ,cAAc,CAACX,IAAI,CAAEgB,CAAC,IAAKH,EAAE,CAACI,IAAI,CAACD,CAAC,CAAC,CAAC;EACzD,IAAID,UAAU,EAAE;IACd,OAAQ,MAAM,IAAAG,mBAAY,EAAC;MAAEC,IAAI,EAAEJ;IAAW,CAAC,CAAC;EAClD,CAAC,MAAM;IACL,MAAM,IAAIK,KAAK,CAAC,WAAWV,MAAM,mDAAmD,CAAC;EACvF;AACF;AAEO,eAAeW,sCAAsCA,CAC1D7C,WAAmB,EACnBkC,MAAc,EACG;EACjB,MAAMY,SAAS,GAAG,MAAMb,eAAe,CAACjC,WAAW,EAAEkC,MAAM,CAAC;EAC5D,MAAMa,gBAAgB,GACpBD,SAAS,EAAEE,MAAM,EAAEC,WAAW,GAAG,CAAC,CAAC,EAAEC,kBAAkB,GAAG,CAAC,CAAC,EAAEC,gBAAgB;EAChF,MAAMC,UAAU,GACdL,gBAAgB,EAAEM,MAAM,KAAK,CAAC,GAC1BC,gBAAgB,CAACP,gBAAgB,CAAC,CAAC,CAAC,CAAC,GACrCO,gBAAgB,CACdP,gBAAgB,EAAEvB,IAAI,CAAE+B,KAAK,IAAK;IAChC,OAAOA,KAAK,CAACC,kBAAkB,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEC,aAAa,EAAEC,QAAQ,CAAC,MAAM,CAAC;EAC9E,CAAC,CACH,CAAC;EACP,IAAI,CAACN,UAAU,EAAE;IACf,MAAM,IAAIR,KAAK,CAAC,GAAGV,MAAM,iCAAiC,CAAC;EAC7D;EACA,OAAOkB,UAAU;AACnB;AAEO,eAAeO,0CAA0CA,CAC9D3D,WAAmB,EACnBkC,MAAc,EACG;EACjB,MAAMY,SAAS,GAAG,MAAMb,eAAe,CAACjC,WAAW,EAAEkC,MAAM,CAAC;EAC5D,MAAMZ,kBAAkB,GAAGwB,SAAS,EAAEE,MAAM,EAAEY,aAAa,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEtC,kBAAkB;EAC3F,IAAI,CAACA,kBAAkB,EAAE;IACvB,MAAM,IAAIsB,KAAK,CAAC,GAAGV,MAAM,iCAAiC,CAAC;EAC7D;EACA,OAAOZ,kBAAkB;AAC3B;AAEA,SAASgC,gBAAgBA,CAACC,KAA4B,EAAsB;EAC1E,OAAOA,KAAK,EAAEC,kBAAkB,GAAG,CAAC,CAAC,GAAG,GAAG,CAAC,EAAEK,aAAa;AAC7D", "ignoreList": []}